# Trigger Service

A backend trigger service for workflow automation platform, similar to the trigger system in n8n. The service enables users to set up event-based triggers and schedulers that automatically execute workflows.

## Features

- **Adapter Pattern Architecture**: Extensible design for adding new trigger types
- **Google Calendar Integration**: Monitor calendar events (create, update, delete, reminder)
- **Workflow Execution**: Automatic workflow triggering with event data
- **Error Handling**: Robust retry mechanisms with exponential backoff
- **Monitoring**: Health checks and metrics for production deployment

## Architecture

The service follows an adapter/connector pattern with clear separation of concerns:

- **Trigger Manager**: Orchestrates trigger lifecycle and event processing
- **Adapters**: Service-specific implementations (Google Calendar, future: Slack, Email, etc.)
- **Workflow Executor**: Handles workflow execution requests
- **Auth Client**: Manages authentication with external services

## Quick Start

### Prerequisites

- Python 3.9+
- PostgreSQL 12+
- Redis (for Celery)

### Installation

#### Option 1: Using Poetry (Recommended)

1. Clone the repository:

```bash
git clone <repository-url>
cd trigger-service
```

2. Install Poetry if you haven't already:

```bash
curl -sSL https://install.python-poetry.org | python3 -
```

3. Run the development setup script:

```bash
./scripts/setup-dev.sh
```

4. Start the development database:

```bash
docker-compose -f docker-compose.dev.yml up -d postgres redis
```

5. Run database migrations:

```bash
poetry run alembic upgrade head
```

6. Start the service:

```bash
poetry run python scripts/start.py
# or for development with hot reload:
poetry run uvicorn src.main:app --reload --host 0.0.0.0 --port 8000
```

#### Option 2: Using pip

1. Clone the repository:

```bash
git clone <repository-url>
cd trigger-service
```

2. Create virtual environment:

```bash
python -m venv venv
source venv/bin/activate  # On Windows: venv\Scripts\activate
```

3. Install dependencies:

```bash
pip install -r requirements.txt
pip install -r requirements-dev.txt
```

4. Set up environment variables:

```bash
cp .env.example .env
# Edit .env with your configuration
```

5. Run database migrations:

```bash
alembic upgrade head
```

6. Start the service:

```bash
python -m src.main
```

## Configuration

The service uses environment variables for configuration. See `.env.example` for all available options.

Key configuration variables:

- `DATABASE_URL`: PostgreSQL connection string
- `AUTH_SERVICE_URL`: URL of the authentication service
- `WORKFLOW_SERVICE_URL`: URL of the workflow execution service
- `LOG_LEVEL`: Logging level (DEBUG, INFO, WARNING, ERROR)

## API Documentation

For comprehensive API documentation including required/optional fields, possible values, and detailed examples, see:

**📖 [Complete API Documentation](API_DOCUMENTATION.md)**

This documentation includes:

- All API endpoints with detailed descriptions
- Required vs optional field specifications
- Authentication requirements per endpoint
- Field validation rules and possible values
- Comprehensive request/response examples
- Error handling and status codes

### Interactive Documentation

Once the service is running, you can also access interactive API documentation at:

- **Swagger UI**: http://localhost:8000/docs
- **ReDoc**: http://localhost:8000/redoc

## Testing

Run the test suite:

```bash
# All tests
pytest

# Unit tests only
pytest tests/unit

# Integration tests only
pytest tests/integration

# With coverage
pytest --cov=src --cov-report=html
```

## Development

### Code Style

The project uses:

- Black for code formatting
- isort for import sorting
- flake8 for linting
- mypy for type checking

Run code quality checks:

```bash
black src tests
isort src tests
flake8 src tests
mypy src
```

### Adding New Trigger Types

To add a new trigger type:

1. Create a new adapter class extending `BaseTriggerAdapter`
2. Implement all abstract methods
3. Register the adapter with the `TriggerManager`
4. Add appropriate tests

See `src/adapters/base.py` for the interface definition.

## Deployment

### Docker

Build and run with Docker:

```bash
docker build -t trigger-service .
docker run -p 8000:8000 trigger-service
```

### Docker Compose

For development with all dependencies:

```bash
docker-compose up -d
```

## Monitoring

The service provides:

- Health check endpoints at `/api/v1/health`
- Prometheus metrics at `/api/v1/metrics`
- Structured JSON logging

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Run the test suite
6. Submit a pull request

## License

[License information]
