"""
Base classes and utilities for API routes.

This module provides standardized base classes, response models,
and utilities for consistent API route implementation.
"""

from typing import Any, Dict, List, Optional, Generic, TypeVar, Union
from datetime import datetime
from uuid import UUID
from enum import Enum

from fastapi import HTTPException, status, Query, Path, Depends
from pydantic import BaseModel, Field, ConfigDict
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.exceptions import (
    TriggerServiceError,
    ValidationError,
    AuthenticationError,
    AuthorizationError,
    create_error_response,
)
from src.database.connection import get_async_session
from src.api.middleware.auth import get_authenticated_user
from src.utils.logger import get_logger

logger = get_logger(__name__)

# Generic type for data models
T = TypeVar("T")


class APIStatus(str, Enum):
    """API response status enumeration."""

    SUCCESS = "success"
    ERROR = "error"
    PARTIAL = "partial"


class PaginationParams(BaseModel):
    """Standard pagination parameters."""

    page: int = Field(default=1, ge=1, description="Page number (1-based)")

    page_size: int = Field(
        default=20, ge=1, le=100, description="Number of items per page"
    )

    @property
    def offset(self) -> int:
        """Calculate offset for database queries."""
        return (self.page - 1) * self.page_size

    @property
    def limit(self) -> int:
        """Get limit for database queries."""
        return self.page_size


class SortParams(BaseModel):
    """Standard sorting parameters."""

    sort_by: Optional[str] = Field(default=None, description="Field to sort by")

    sort_order: str = Field(
        default="asc", pattern="^(asc|desc)$", description="Sort order (asc or desc)"
    )


class FilterParams(BaseModel):
    """Base filter parameters."""

    created_after: Optional[datetime] = Field(
        default=None, description="Filter items created after this date"
    )

    created_before: Optional[datetime] = Field(
        default=None, description="Filter items created before this date"
    )

    is_active: Optional[bool] = Field(
        default=None, description="Filter by active status"
    )


class BaseResponse(BaseModel, Generic[T]):
    """Base response model for all API endpoints."""

    status: APIStatus = Field(description="Response status")
    message: str = Field(description="Response message")
    data: Optional[T] = Field(default=None, description="Response data")
    errors: Optional[List[Dict[str, Any]]] = Field(
        default=None, description="Error details if any"
    )
    timestamp: datetime = Field(
        default_factory=datetime.utcnow, description="Response timestamp"
    )

    model_config = ConfigDict(
        json_encoders={
            datetime: lambda v: v.isoformat(),
            UUID: str,
        }
    )


class PaginatedResponse(BaseResponse[List[T]]):
    """Paginated response model."""

    pagination: Dict[str, Any] = Field(description="Pagination metadata")

    @classmethod
    def create(
        cls,
        data: List[T],
        pagination_params: PaginationParams,
        total_count: int,
        message: str = "Success",
    ) -> "PaginatedResponse[T]":
        """
        Create a paginated response.

        Args:
            data: List of items
            pagination_params: Pagination parameters
            total_count: Total number of items
            message: Response message

        Returns:
            PaginatedResponse: Paginated response instance
        """
        total_pages = (
            total_count + pagination_params.page_size - 1
        ) // pagination_params.page_size

        return cls(
            status=APIStatus.SUCCESS,
            message=message,
            data=data,
            pagination={
                "page": pagination_params.page,
                "page_size": pagination_params.page_size,
                "total_count": total_count,
                "total_pages": total_pages,
                "has_next": pagination_params.page < total_pages,
                "has_previous": pagination_params.page > 1,
            },
        )


class SuccessResponse(BaseResponse[T]):
    """Success response model."""

    @classmethod
    def create(
        cls, data: Optional[T] = None, message: str = "Success"
    ) -> "SuccessResponse[T]":
        """
        Create a success response.

        Args:
            data: Response data
            message: Success message

        Returns:
            SuccessResponse: Success response instance
        """
        return cls(status=APIStatus.SUCCESS, message=message, data=data)


class ErrorResponse(BaseResponse[None]):
    """Error response model."""

    @classmethod
    def create(
        cls,
        message: str,
        errors: Optional[List[Dict[str, Any]]] = None,
        status: APIStatus = APIStatus.ERROR,
    ) -> "ErrorResponse":
        """
        Create an error response.

        Args:
            message: Error message
            errors: Error details
            status: Response status

        Returns:
            ErrorResponse: Error response instance
        """
        return cls(status=status, message=message, errors=errors or [])


class BaseAPIRouter:
    """
    Base class for API routers with common functionality.

    This class provides standardized methods for handling common
    API operations like pagination, filtering, and error handling.
    """

    def __init__(self, router_name: str):
        """
        Initialize the base router.

        Args:
            router_name: Name of the router for logging
        """
        self.router_name = router_name
        self.logger = get_logger(f"api.{router_name}")

    def handle_service_error(self, error: Exception) -> HTTPException:
        """
        Convert service errors to HTTP exceptions.

        Args:
            error: Service error

        Returns:
            HTTPException: HTTP exception with appropriate status code
        """
        if isinstance(error, TriggerServiceError):
            if isinstance(error, ValidationError):
                status_code = status.HTTP_422_UNPROCESSABLE_ENTITY
            elif isinstance(error, AuthenticationError):
                status_code = status.HTTP_401_UNAUTHORIZED
            elif isinstance(error, AuthorizationError):
                status_code = status.HTTP_403_FORBIDDEN
            else:
                status_code = status.HTTP_500_INTERNAL_SERVER_ERROR

            error_response = create_error_response(error)
            raise HTTPException(status_code=status_code, detail=error_response)
        else:
            self.logger.error(f"Unhandled error in {self.router_name}: {error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=ErrorResponse.create(
                    message="An internal server error occurred"
                ).dict(),
            )

    def validate_pagination(self, pagination: PaginationParams) -> PaginationParams:
        """
        Validate pagination parameters.

        Args:
            pagination: Pagination parameters

        Returns:
            PaginationParams: Validated pagination parameters
        """
        if pagination.page < 1:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=ErrorResponse.create(
                    message="Page number must be greater than 0"
                ).dict(),
            )

        if pagination.page_size < 1 or pagination.page_size > 100:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=ErrorResponse.create(
                    message="Page size must be between 1 and 100"
                ).dict(),
            )

        return pagination

    def validate_uuid(self, uuid_value: str, field_name: str) -> UUID:
        """
        Validate UUID parameter.

        Args:
            uuid_value: UUID string
            field_name: Name of the field for error messages

        Returns:
            UUID: Validated UUID

        Raises:
            HTTPException: If UUID is invalid
        """
        try:
            return UUID(uuid_value)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                detail=ErrorResponse.create(
                    message=f"Invalid {field_name} format"
                ).dict(),
            )


# Common dependency functions
def get_pagination_params(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
) -> PaginationParams:
    """Get pagination parameters from query string."""
    return PaginationParams(page=page, page_size=page_size)


def get_sort_params(
    sort_by: Optional[str] = Query(None, description="Field to sort by"),
    sort_order: str = Query("asc", pattern="^(asc|desc)$", description="Sort order"),
) -> SortParams:
    """Get sort parameters from query string."""
    return SortParams(sort_by=sort_by, sort_order=sort_order)


def get_filter_params(
    created_after: Optional[datetime] = Query(None, description="Created after"),
    created_before: Optional[datetime] = Query(None, description="Created before"),
    is_active: Optional[bool] = Query(None, description="Active status"),
) -> FilterParams:
    """Get filter parameters from query string."""
    return FilterParams(
        created_after=created_after, created_before=created_before, is_active=is_active
    )


def get_user_id_path(user_id: str = Path(..., description="User ID")) -> str:
    """Get user ID from path parameter."""
    return user_id


def get_uuid_path(id: str = Path(..., description="Resource ID")) -> UUID:
    """Get UUID from path parameter."""
    try:
        return UUID(id)
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=ErrorResponse.create(message="Invalid ID format").dict(),
        )
