"""
Google Drive API routes for managing subscriptions.

This module provides endpoints for:
- Creating and managing Google Drive webhook subscriptions
- Monitoring subscription health and status
"""

from typing import Dict, List, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from src.adapters.google_drive import GoogleDriveAdapter
from src.database.connection import get_async_session
from src.schemas.google_drive_subscription import (
    GoogleDriveSubscriptionCreate,
    GoogleDriveSubscriptionResponse,
)
from src.api.middleware.auth import get_authenticated_user_api_key_only
from src.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(
    prefix="/api/v1/google-drive/service-account",
    tags=["google-drive-service-account"],
)


def get_google_drive_service() -> GoogleDriveAdapter:
    """Get Google Drive service instance."""
    return GoogleDriveAdapter()


@router.post(
    "/subscribe",
    response_model=GoogleDriveSubscriptionResponse,
    summary="Subscribe to Google Drive events",
    description="Subscribe to Google Drive events for an organization with specified event types.",
)
async def subscribe_to_drive_events(
    subscription_data: GoogleDriveSubscriptionCreate,
    session: AsyncSession = Depends(get_async_session),
    drive_service: GoogleDriveAdapter = Depends(get_google_drive_service),
    current_user: Dict[str, Any] = Depends(get_authenticated_user_api_key_only),
):
    """Subscribe to Google Drive events for an organization."""
    try:
        return await drive_service.create_subscription(subscription_data, session)
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e),
        )
    except RuntimeError as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=str(e),
        )
    except Exception as e:
        logger.error(
            "Error creating Google Drive subscription",
            error=str(e),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Internal server error: {str(e)}",
        )


@router.get(
    "/subscriptions",
    response_model=List[GoogleDriveSubscriptionResponse],
    summary="List subscriptions",
    description="Get all Google Drive subscriptions.",
)
async def list_subscriptions(
    session: AsyncSession = Depends(get_async_session),
    drive_service: GoogleDriveAdapter = Depends(get_google_drive_service),
    current_user: Dict[str, Any] = Depends(get_authenticated_user_api_key_only),
):
    """List all Google Drive subscriptions."""
    try:
        return await drive_service.list_subscriptions(session)
    except Exception as e:
        logger.error("Failed to list subscriptions", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list subscriptions: {str(e)}",
        )


@router.delete(
    "/subscriptions/{subscription_id}",
    summary="Delete subscription",
    description="Delete a Google Drive subscription.",
)
async def delete_subscription(
    subscription_id: str,
    session: AsyncSession = Depends(get_async_session),
    drive_service: GoogleDriveAdapter = Depends(get_google_drive_service),
    current_user: Dict[str, Any] = Depends(get_authenticated_user_api_key_only),
):
    """Delete a Google Drive subscription."""
    try:
        success = await drive_service.delete_subscription(subscription_id, session)
        if success:
            return {"success": True, "message": "Subscription deleted successfully"}
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete subscription",
            )
    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=str(e),
        )
    except Exception as e:
        logger.error(
            "Failed to delete subscription",
            subscription_id=subscription_id,
            error=str(e),
        )
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete subscription: {str(e)}",
        )


@router.get(
    "/health",
    summary="Health check",
    description="Check health of Google Drive service account integration.",
)
async def health_check(
    drive_service: GoogleDriveAdapter = Depends(get_google_drive_service),
):
    """Health check for Google Drive service account integration."""
    try:
        return drive_service.get_health_status()
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        return {
            "healthy": False,
            "error": str(e),
            "timestamp": "error",
        }


@router.get(
    "/event-types",
    summary="Get supported event types",
    description="Get list of supported Google Drive event types.",
)
async def get_supported_event_types(
    drive_service: GoogleDriveAdapter = Depends(get_google_drive_service),
):
    """Get list of supported Google Drive event types."""
    return drive_service.get_supported_event_types()


@router.post(
    "/check-expired",
    summary="Check and update expired subscriptions",
    description="Check all subscriptions for expiration and update their status.",
)
async def check_expired_subscriptions(
    session: AsyncSession = Depends(get_async_session),
    drive_service: GoogleDriveAdapter = Depends(get_google_drive_service),
    current_user: Dict[str, Any] = Depends(get_authenticated_user_api_key_only),
):
    """Check and update expired subscriptions."""
    try:
        return await drive_service.check_expired_subscriptions(session)
    except Exception as e:
        logger.error("Failed to check expired subscriptions", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check expired subscriptions: {str(e)}",
        )
