"""
Refactored triggers API routes with standardized patterns.

This module provides a clean, consistent API for trigger management
with proper validation, error handling, and response formatting.
"""

from typing import List, Optional
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func
from sqlalchemy.orm import selectinload

from src.api.base import (
    BaseAPIRouter,
    SuccessResponse,
    PaginatedResponse,
    ErrorResponse,
    get_pagination_params,
    get_filter_params,
    get_sort_params,
    get_user_id_path,
    get_uuid_path,
    PaginationParams,
    FilterParams,
    SortParams,
)
from src.api.models import (
    CreateTriggerRequest,
    UpdateTriggerRequest,
    TriggerResponse,
    TriggerExecutionResponse,
)
from src.database.connection import get_async_session
from src.database.models import Trigger, TriggerExecution
from src.adapters.base import TriggerEventType
from src.core.dependencies import get_trigger_manager_dependency
from src.core.trigger_manager import TriggerManager
from src.api.middleware.auth import get_authenticated_user
from src.utils.logger import get_logger

logger = get_logger(__name__)

# Create router
router = APIRouter(prefix="/api/v2/triggers", tags=["triggers"])


class TriggersAPIRouter(BaseAPIRouter):
    """Triggers API router with standardized patterns."""

    def __init__(self):
        super().__init__("triggers")


# Router instance
triggers_router = TriggersAPIRouter()


@router.get(
    "/users/{user_id}",
    response_model=PaginatedResponse[TriggerResponse],
    summary="List user triggers",
    description="Get a paginated list of triggers for a specific user",
)
async def list_user_triggers(
    user_id: str = Depends(get_user_id_path),
    pagination: PaginationParams = Depends(get_pagination_params),
    filters: FilterParams = Depends(get_filter_params),
    sort: SortParams = Depends(get_sort_params),
    session: AsyncSession = Depends(get_async_session),
    current_user: str = Depends(get_authenticated_user),
) -> PaginatedResponse[TriggerResponse]:
    """List triggers for a user with pagination and filtering."""
    try:
        # Validate pagination
        pagination = triggers_router.validate_pagination(pagination)

        # Build query
        query = select(Trigger).where(Trigger.user_id == user_id)

        # Apply filters
        if filters.is_active is not None:
            query = query.where(Trigger.is_active == filters.is_active)

        if filters.created_after:
            query = query.where(Trigger.created_at >= filters.created_after)

        if filters.created_before:
            query = query.where(Trigger.created_at <= filters.created_before)

        # Apply sorting
        if sort.sort_by:
            sort_column = getattr(Trigger, sort.sort_by, None)
            if sort_column:
                if sort.sort_order == "desc":
                    query = query.order_by(sort_column.desc())
                else:
                    query = query.order_by(sort_column.asc())
            else:
                query = query.order_by(Trigger.created_at.desc())
        else:
            query = query.order_by(Trigger.created_at.desc())

        # Get total count
        count_query = select(func.count()).select_from(query.subquery())
        total_count = await session.scalar(count_query)

        # Apply pagination
        query = query.offset(pagination.offset).limit(pagination.limit)

        # Execute query
        result = await session.execute(query)
        triggers = result.scalars().all()

        # Convert to response models
        trigger_responses = [
            TriggerResponse.model_validate(trigger) for trigger in triggers
        ]

        return PaginatedResponse.create(
            data=trigger_responses,
            pagination_params=pagination,
            total_count=total_count,
            message=f"Retrieved {len(trigger_responses)} triggers",
        )

    except Exception as e:
        triggers_router.handle_service_error(e)


@router.post(
    "/users/{user_id}",
    response_model=SuccessResponse[TriggerResponse],
    status_code=status.HTTP_201_CREATED,
    summary="Create trigger",
    description="Create a new trigger for a user",
)
async def create_trigger(
    user_id: str = Depends(get_user_id_path),
    request: CreateTriggerRequest = ...,
    session: AsyncSession = Depends(get_async_session),
    trigger_manager: TriggerManager = Depends(get_trigger_manager_dependency),
    current_user: str = Depends(get_authenticated_user),
) -> SuccessResponse[TriggerResponse]:
    """Create a new trigger."""
    try:
        # Create trigger instance
        trigger = Trigger(
            user_id=user_id,
            name=request.name,
            description=request.description,
            trigger_type=request.trigger_type,
            workflow_id=request.workflow_id,
            event_types=request.event_types,
            config=request.config.model_dump(),
            is_active=request.is_active,
        )

        # Add to session
        session.add(trigger)
        await session.flush()  # Get the ID

        # Set up trigger with adapter
        if request.is_active:
            from src.adapters.base import TriggerConfiguration

            trigger_config = TriggerConfiguration(
                trigger_id=trigger.id,
                user_id=user_id,
                trigger_type=request.trigger_type,
                event_types=[
                    getattr(TriggerEventType, et.upper()) for et in request.event_types
                ],
                config=request.config.model_dump(),
            )

            success = await trigger_manager.setup_trigger(trigger_config, session)
            if not success:
                await session.rollback()
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=ErrorResponse.create(
                        message="Failed to set up trigger with external service"
                    ).model_dump(),
                )

        await session.commit()
        await session.refresh(trigger)

        # Convert to response model
        trigger_response = TriggerResponse.model_validate(trigger)

        return SuccessResponse.create(
            data=trigger_response, message="Trigger created successfully"
        )

    except Exception as e:
        await session.rollback()
        triggers_router.handle_service_error(e)


@router.get(
    "/{trigger_id}",
    response_model=SuccessResponse[TriggerResponse],
    summary="Get trigger",
    description="Get a specific trigger by ID",
)
async def get_trigger(
    trigger_id: UUID = Depends(get_uuid_path),
    session: AsyncSession = Depends(get_async_session),
    current_user: str = Depends(get_authenticated_user),
) -> SuccessResponse[TriggerResponse]:
    """Get a trigger by ID."""
    try:
        # Query trigger
        query = select(Trigger).where(Trigger.id == trigger_id)
        result = await session.execute(query)
        trigger = result.scalar_one_or_none()

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ErrorResponse.create(message="Trigger not found").model_dump(),
            )

        # Convert to response model
        trigger_response = TriggerResponse.model_validate(trigger)

        return SuccessResponse.create(
            data=trigger_response, message="Trigger retrieved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        triggers_router.handle_service_error(e)


@router.put(
    "/{trigger_id}",
    response_model=SuccessResponse[TriggerResponse],
    summary="Update trigger",
    description="Update a trigger",
)
async def update_trigger(
    trigger_id: UUID = Depends(get_uuid_path),
    request: UpdateTriggerRequest = ...,
    session: AsyncSession = Depends(get_async_session),
    trigger_manager: TriggerManager = Depends(get_trigger_manager_dependency),
    current_user: str = Depends(get_authenticated_user),
) -> SuccessResponse[TriggerResponse]:
    """Update a trigger."""
    try:
        # Get existing trigger
        query = select(Trigger).where(Trigger.id == trigger_id)
        result = await session.execute(query)
        trigger = result.scalar_one_or_none()

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ErrorResponse.create(message="Trigger not found").model_dump(),
            )

        # Update fields
        update_data = {}
        if request.name is not None:
            update_data["name"] = request.name
        if request.description is not None:
            update_data["description"] = request.description
        if request.event_types is not None:
            update_data["event_types"] = request.event_types
        if request.config is not None:
            update_data["config"] = request.config.model_dump()
        if request.is_active is not None:
            update_data["is_active"] = request.is_active

        if update_data:
            # Update trigger
            stmt = update(Trigger).where(Trigger.id == trigger_id).values(**update_data)
            await session.execute(stmt)

            # If activation status changed, handle external service
            if "is_active" in update_data:
                if update_data["is_active"] and not trigger.is_active:
                    # Activating trigger
                    from src.adapters.base import TriggerConfiguration

                    trigger_config = TriggerConfiguration(
                        trigger_id=trigger.id,
                        user_id=trigger.user_id,
                        trigger_type=trigger.trigger_type,
                        event_types=[
                            getattr(TriggerEventType, et.upper())
                            for et in trigger.event_types
                        ],
                        config=trigger.config,
                    )

                    success = await trigger_manager.setup_trigger(
                        trigger_config, session
                    )
                    if not success:
                        await session.rollback()
                        raise HTTPException(
                            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                            detail=ErrorResponse.create(
                                message="Failed to activate trigger"
                            ).model_dump(),
                        )

                elif not update_data["is_active"] and trigger.is_active:
                    # Deactivating trigger
                    await trigger_manager.remove_trigger(trigger.id)

            await session.commit()
            await session.refresh(trigger)

        # Convert to response model
        trigger_response = TriggerResponse.model_validate(trigger)

        return SuccessResponse.create(
            data=trigger_response, message="Trigger updated successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        await session.rollback()
        triggers_router.handle_service_error(e)


@router.delete(
    "/{trigger_id}",
    response_model=SuccessResponse[None],
    summary="Delete trigger",
    description="Delete a trigger",
)
async def delete_trigger(
    trigger_id: UUID = Depends(get_uuid_path),
    session: AsyncSession = Depends(get_async_session),
    trigger_manager: TriggerManager = Depends(get_trigger_manager_dependency),
    current_user: str = Depends(get_authenticated_user),
) -> SuccessResponse[None]:
    """Delete a trigger."""
    try:
        # Check if trigger exists
        query = select(Trigger).where(Trigger.id == trigger_id)
        result = await session.execute(query)
        trigger = result.scalar_one_or_none()

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ErrorResponse.create(message="Trigger not found").model_dump(),
            )

        # Remove from external service if active
        if trigger.is_active:
            await trigger_manager.remove_trigger(trigger_id)

        # Delete from database
        stmt = delete(Trigger).where(Trigger.id == trigger_id)
        await session.execute(stmt)
        await session.commit()

        return SuccessResponse.create(message="Trigger deleted successfully")

    except HTTPException:
        raise
    except Exception as e:
        await session.rollback()
        triggers_router.handle_service_error(e)
