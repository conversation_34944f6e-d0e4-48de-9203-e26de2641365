"""
Triggers API routes with comprehensive CRUD operations.

This module provides professional-grade API endpoints for trigger management
with proper validation, error handling, pagination, filtering, and documentation.
"""

import logging
from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, Body
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func, and_, or_
from sqlalchemy.orm import selectinload

from src.api.base import (
    BaseAPIRouter,
    SuccessResponse,
    PaginatedResponse,
    ErrorResponse,
    get_pagination_params,
    get_filter_params,
    get_sort_params,
    get_user_id_path,
    get_uuid_path,
    PaginationParams,
    FilterParams,
    SortParams,
)
from src.api.models import (
    CreateTriggerRequest,
    UpdateTriggerRequest,
    TriggerResponse,
    TriggerExecutionResponse,
    TriggerConfigModel,
)
from src.database.connection import get_async_session
from src.database.models import Trigger, TriggerExecution, TriggerStatus
from src.adapters.base import TriggerEventType
from src.core.dependencies import get_trigger_manager_dependency
from src.core.trigger_manager import TriggerManager
from src.api.middleware.auth import get_authenticated_user
from src.utils.logger import get_logger
from src.core.exceptions import (
    ValidationError,
    NotFoundError,
    ConflictError,
    ExternalServiceError,
)

logger = get_logger(__name__)

# Create router with comprehensive configuration
router = APIRouter(
    prefix="/api/v1/triggers",
    tags=["triggers"],
    responses={
        400: {"description": "Bad Request - Invalid input data"},
        401: {"description": "Unauthorized - Authentication required"},
        403: {"description": "Forbidden - Insufficient permissions"},
        404: {"description": "Not Found - Resource not found"},
        409: {"description": "Conflict - Resource already exists"},
        422: {"description": "Unprocessable Entity - Validation error"},
        500: {"description": "Internal Server Error"},
    },
)


class TriggersAPIRouter(BaseAPIRouter):
    """Professional triggers API router with standardized patterns."""

    def __init__(self):
        super().__init__("triggers")


# Router instance for error handling
triggers_router = TriggersAPIRouter()


@router.get(
    "/",
    response_model=PaginatedResponse[TriggerResponse],
    summary="List all triggers",
    description="""
    Retrieve a paginated list of all triggers with optional filtering and sorting.

    **Features:**
    - Pagination with configurable page size
    - Filtering by status, type, user, and date ranges
    - Sorting by any field in ascending or descending order
    - Search by trigger name or description
    - Include execution statistics
    """,
    responses={
        200: {
            "description": "Successfully retrieved triggers",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "data": [
                            {
                                "id": "123e4567-e89b-12d3-a456-************",
                                "name": "Google Calendar Meeting Alerts",
                                "description": "Trigger when new meetings are created",
                                "trigger_type": "google_calendar",
                                "workflow_id": "wf_123",
                                "event_types": ["created", "updated"],
                                "config": {"calendar_id": "primary"},
                                "status": "active",
                                "is_active": True,
                                "user_id": "user_123",
                                "created_at": "2024-01-01T10:00:00Z",
                                "updated_at": "2024-01-01T10:00:00Z",
                            }
                        ],
                        "pagination": {
                            "page": 1,
                            "page_size": 20,
                            "total": 1,
                            "pages": 1,
                        },
                        "message": "Retrieved 1 triggers",
                    }
                }
            },
        }
    },
)
async def list_triggers(
    pagination: PaginationParams = Depends(get_pagination_params),
    filters: FilterParams = Depends(get_filter_params),
    sort: SortParams = Depends(get_sort_params),
    search: Optional[str] = Query(
        None,
        description="Search triggers by name or description",
        min_length=1,
        max_length=255,
    ),
    trigger_type: Optional[str] = Query(
        None, description="Filter by trigger type (e.g., google_calendar, google_drive)"
    ),
    status: Optional[str] = Query(
        None, description="Filter by trigger status (active, inactive, error, pending)"
    ),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    include_executions: bool = Query(
        False, description="Include recent execution statistics"
    ),
    session: AsyncSession = Depends(get_async_session),
    current_user: str = Depends(get_authenticated_user),
) -> PaginatedResponse[TriggerResponse]:
    """
    List triggers with comprehensive filtering and pagination.

    This endpoint provides a robust way to retrieve triggers with:
    - Advanced filtering capabilities
    - Full-text search across name and description
    - Configurable pagination
    - Optional execution statistics
    - Proper error handling and validation
    """
    try:
        # Validate pagination parameters
        pagination = triggers_router.validate_pagination(pagination)

        # Build base query with eager loading for performance
        query = select(Trigger)
        if include_executions:
            query = query.options(
                selectinload(Trigger.executions).limit(5)  # Last 5 executions
            )

        # Apply search filter
        if search:
            search_filter = or_(
                Trigger.name.ilike(f"%{search}%"),
                Trigger.description.ilike(f"%{search}%"),
            )
            query = query.where(search_filter)

        # Apply filters
        filters_applied = []
        if trigger_type:
            query = query.where(Trigger.trigger_type == trigger_type)
            filters_applied.append(f"type={trigger_type}")

        if status:
            try:
                status_enum = TriggerStatus(status)
                query = query.where(Trigger.status == status_enum)
                filters_applied.append(f"status={status}")
            except ValueError:
                raise ValidationError(f"Invalid status: {status}")

        if user_id:
            query = query.where(Trigger.user_id == user_id)
            filters_applied.append(f"user_id={user_id}")

        if filters.is_active is not None:
            query = query.where(Trigger.is_active == filters.is_active)
            filters_applied.append(f"is_active={filters.is_active}")

        if filters.created_after:
            query = query.where(Trigger.created_at >= filters.created_after)
            filters_applied.append(f"created_after={filters.created_after}")

        if filters.created_before:
            query = query.where(Trigger.created_at <= filters.created_before)
            filters_applied.append(f"created_before={filters.created_before}")

        # Apply sorting
        if sort.sort_by:
            sort_column = getattr(Trigger, sort.sort_by, None)
            if sort_column:
                if sort.sort_order == "desc":
                    query = query.order_by(sort_column.desc())
                else:
                    query = query.order_by(sort_column.asc())
            else:
                logger.warning(f"Invalid sort field: {sort.sort_by}")
                query = query.order_by(Trigger.created_at.desc())
        else:
            query = query.order_by(Trigger.created_at.desc())

        # Get total count for pagination
        count_query = select(func.count()).select_from(query.subquery())
        total_count = await session.scalar(count_query)

        # Apply pagination
        query = query.offset(pagination.offset).limit(pagination.limit)

        # Execute query
        result = await session.execute(query)
        triggers = result.scalars().all()

        # Convert to response models
        trigger_responses = []
        for trigger in triggers:
            trigger_data = TriggerResponse.model_validate(trigger)

            # Add execution statistics if requested
            if include_executions and trigger.executions:
                trigger_data.execution_stats = {
                    "total_executions": len(trigger.executions),
                    "recent_executions": [
                        TriggerExecutionResponse.model_validate(exec)
                        for exec in trigger.executions[:5]
                    ],
                }

            trigger_responses.append(trigger_data)

        # Create response message
        message_parts = [f"Retrieved {len(trigger_responses)} triggers"]
        if filters_applied:
            message_parts.append(f"with filters: {', '.join(filters_applied)}")
        if search:
            message_parts.append(f"matching search: '{search}'")

        return PaginatedResponse.create(
            data=trigger_responses,
            pagination_params=pagination,
            total_count=total_count,
            message=" ".join(message_parts),
        )

    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=ErrorResponse.create(message=str(e)).model_dump(),
        )
    except Exception as e:
        logger.error(f"Failed to list triggers: {e}", exc_info=True)
        triggers_router.handle_service_error(e)


@router.post(
    "/",
    response_model=SuccessResponse[TriggerResponse],
    status_code=status.HTTP_201_CREATED,
    summary="Create a new trigger",
    description="""
    Create a new trigger with comprehensive validation and setup.

    **Features:**
    - Validates trigger configuration against adapter requirements
    - Sets up external service integrations (webhooks, etc.)
    - Supports all available trigger types and event types
    - Automatic activation with proper error handling
    - Comprehensive validation of workflow integration
    """,
    responses={
        201: {
            "description": "Trigger created successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "data": {
                            "id": "123e4567-e89b-12d3-a456-************",
                            "name": "Google Calendar Meeting Alerts",
                            "description": "Trigger when new meetings are created",
                            "trigger_type": "google_calendar",
                            "workflow_id": "wf_123",
                            "event_types": ["created", "updated"],
                            "config": {"calendar_id": "primary"},
                            "status": "active",
                            "is_active": True,
                            "user_id": "user_123",
                            "created_at": "2024-01-01T10:00:00Z",
                            "updated_at": "2024-01-01T10:00:00Z",
                        },
                        "message": "Trigger created and activated successfully",
                    }
                }
            },
        },
        400: {"description": "Invalid trigger configuration"},
        409: {"description": "Trigger with this name already exists"},
        422: {"description": "Validation error in request data"},
    },
)
async def create_trigger(
    request: CreateTriggerRequest = Body(
        ...,
        description="Trigger creation data",
        examples={
            "google_calendar": {
                "summary": "Google Calendar trigger example",
                "value": {
                    "name": "Meeting Notifications",
                    "description": "Notify when calendar events are created or updated",
                    "trigger_type": "google_calendar",
                    "workflow_id": "wf_meeting_notifications",
                    "event_types": ["created", "updated"],
                    "config": {
                        "calendar_id": "primary",
                        "webhook_ttl": 3600,
                        "event_filters": {"attendee_count_min": 2},
                    },
                    "is_active": True,
                },
            },
            "google_drive": {
                "summary": "Google Drive trigger example",
                "value": {
                    "name": "Document Changes",
                    "description": "Monitor changes to important documents",
                    "trigger_type": "google_drive",
                    "workflow_id": "wf_document_monitor",
                    "event_types": ["created", "updated"],
                    "config": {
                        "folder_ids": ["1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"],
                        "file_types": [
                            "application/pdf",
                            "application/vnd.google-apps.document",
                        ],
                        "webhook_ttl": 7200,
                    },
                    "is_active": True,
                },
            },
        },
    ),
    user_id: str = Query(
        ...,
        description="User ID for the trigger owner",
        min_length=1,
        max_length=255,
    ),
    session: AsyncSession = Depends(get_async_session),
    trigger_manager: TriggerManager = Depends(get_trigger_manager_dependency),
    current_user: str = Depends(get_authenticated_user),
) -> SuccessResponse[TriggerResponse]:
    """
    Create a new trigger with comprehensive validation and setup.

    This endpoint handles the complete trigger creation process:
    1. Validates the request data and configuration
    2. Checks for naming conflicts
    3. Validates the trigger configuration against the adapter
    4. Creates the database record
    5. Sets up external integrations (webhooks, etc.)
    6. Activates the trigger if requested

    The trigger will be automatically activated if `is_active` is True,
    which includes setting up any required external service integrations.
    """
    try:
        # Validate user authorization (basic check)
        if user_id != current_user:
            # In a real implementation, you might check if current_user has permission
            # to create triggers for the specified user_id
            logger.warning(
                f"User {current_user} attempting to create trigger for {user_id}"
            )

        # Check for naming conflicts
        existing_trigger_query = select(Trigger).where(
            and_(Trigger.user_id == user_id, Trigger.name == request.name)
        )
        existing_trigger = await session.scalar(existing_trigger_query)

        if existing_trigger:
            raise ConflictError(
                f"Trigger with name '{request.name}' already exists for user {user_id}"
            )

        # Validate trigger type is supported
        available_adapters = trigger_manager.list_available_adapters()
        if request.trigger_type not in available_adapters:
            raise ValidationError(
                f"Unsupported trigger type: {request.trigger_type}. "
                f"Available types: {', '.join(available_adapters)}"
            )

        # Validate event types for the adapter
        adapter = trigger_manager.get_adapter(request.trigger_type)
        if adapter:
            supported_event_types = adapter.get_supported_event_types()
            for event_type in request.event_types:
                try:
                    event_enum = TriggerEventType(event_type)
                    if event_enum not in supported_event_types:
                        raise ValidationError(
                            f"Event type '{event_type}' not supported by {request.trigger_type} adapter"
                        )
                except ValueError:
                    raise ValidationError(f"Invalid event type: {event_type}")

        # Create trigger instance
        trigger = Trigger(
            user_id=user_id,
            name=request.name,
            description=request.description,
            trigger_type=request.trigger_type,
            workflow_id=request.workflow_id,
            event_types=request.event_types,
            config=request.config.model_dump() if request.config else {},
            is_active=request.is_active,
            status=TriggerStatus.PENDING,  # Will be updated after setup
        )

        # Add to session and flush to get ID
        session.add(trigger)
        await session.flush()

        # Set up trigger with adapter if active
        setup_success = True
        setup_error = None

        if request.is_active:
            try:
                from src.adapters.base import TriggerConfiguration

                trigger_config = TriggerConfiguration(
                    trigger_id=trigger.id,
                    user_id=user_id,
                    trigger_type=request.trigger_type,
                    event_types=[TriggerEventType(et) for et in request.event_types],
                    config=request.config.model_dump() if request.config else {},
                )

                setup_success = await trigger_manager.setup_trigger(
                    trigger_config, session
                )

                if setup_success:
                    trigger.status = TriggerStatus.ACTIVE
                    logger.info(
                        f"Successfully set up trigger {trigger.id} with external service"
                    )
                else:
                    trigger.status = TriggerStatus.ERROR
                    trigger.is_active = False
                    setup_error = "Failed to set up trigger with external service"
                    logger.error(f"Failed to set up trigger {trigger.id}")

            except Exception as e:
                setup_success = False
                setup_error = f"Setup error: {str(e)}"
                trigger.status = TriggerStatus.ERROR
                trigger.is_active = False
                logger.error(f"Exception during trigger setup: {e}", exc_info=True)
        else:
            trigger.status = TriggerStatus.INACTIVE

        # Commit the transaction
        await session.commit()
        await session.refresh(trigger)

        # Convert to response model
        trigger_response = TriggerResponse.model_validate(trigger)

        # Create appropriate response message
        if setup_success and request.is_active:
            message = "Trigger created and activated successfully"
        elif not request.is_active:
            message = "Trigger created successfully (inactive)"
        else:
            message = f"Trigger created but activation failed: {setup_error}"

        logger.info(f"Created trigger {trigger.id} for user {user_id}: {message}")

        return SuccessResponse.create(
            data=trigger_response,
            message=message,
        )

    except ConflictError as e:
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=ErrorResponse.create(message=str(e)).model_dump(),
        )
    except ValidationError as e:
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=ErrorResponse.create(message=str(e)).model_dump(),
        )
    except Exception as e:
        await session.rollback()
        logger.error(f"Failed to create trigger: {e}", exc_info=True)
        triggers_router.handle_service_error(e)


@router.get(
    "/{trigger_id}",
    response_model=SuccessResponse[TriggerResponse],
    summary="Get trigger by ID",
    description="""
    Retrieve a specific trigger by its ID with optional execution history.

    **Features:**
    - Retrieves complete trigger information
    - Optional execution history with configurable limit
    - Includes adapter-specific configuration details
    - Proper error handling for not found cases
    """,
    responses={
        200: {
            "description": "Trigger retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "data": {
                            "id": "123e4567-e89b-12d3-a456-************",
                            "name": "Google Calendar Meeting Alerts",
                            "description": "Trigger when new meetings are created",
                            "trigger_type": "google_calendar",
                            "workflow_id": "wf_123",
                            "event_types": ["created", "updated"],
                            "config": {"calendar_id": "primary"},
                            "status": "active",
                            "is_active": True,
                            "user_id": "user_123",
                            "created_at": "2024-01-01T10:00:00Z",
                            "updated_at": "2024-01-01T10:00:00Z",
                            "execution_stats": {
                                "total_executions": 25,
                                "recent_executions": [],
                            },
                        },
                        "message": "Trigger retrieved successfully",
                    }
                }
            },
        },
        404: {"description": "Trigger not found"},
    },
)
async def get_trigger(
    trigger_id: UUID = Path(
        ...,
        description="Unique identifier of the trigger",
        example="123e4567-e89b-12d3-a456-************",
    ),
    include_executions: bool = Query(
        False, description="Include recent execution history"
    ),
    execution_limit: int = Query(
        10,
        ge=1,
        le=100,
        description="Maximum number of recent executions to include",
    ),
    session: AsyncSession = Depends(get_async_session),
    current_user: str = Depends(get_authenticated_user),
) -> SuccessResponse[TriggerResponse]:
    """
    Retrieve a specific trigger by ID.

    This endpoint provides detailed information about a trigger including:
    - Complete trigger configuration
    - Current status and metadata
    - Optional execution history
    - Adapter-specific configuration details
    """
    try:
        # Build query with optional execution loading
        query = select(Trigger).where(Trigger.id == trigger_id)

        if include_executions:
            query = query.options(
                selectinload(Trigger.executions).limit(execution_limit)
            )

        # Execute query
        result = await session.execute(query)
        trigger = result.scalar_one_or_none()

        if not trigger:
            raise NotFoundError(f"Trigger with ID {trigger_id} not found")

        # Convert to response model
        trigger_response = TriggerResponse.model_validate(trigger)

        # Add execution statistics if requested
        if include_executions and trigger.executions:
            trigger_response.execution_stats = {
                "total_executions": len(trigger.executions),
                "recent_executions": [
                    TriggerExecutionResponse.model_validate(exec)
                    for exec in trigger.executions[:execution_limit]
                ],
            }

        logger.info(f"Retrieved trigger {trigger_id} for user {current_user}")

        return SuccessResponse.create(
            data=trigger_response,
            message="Trigger retrieved successfully",
        )

    except NotFoundError as e:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=ErrorResponse.create(message=str(e)).model_dump(),
        )
    except Exception as e:
        logger.error(f"Failed to get trigger {trigger_id}: {e}", exc_info=True)
        triggers_router.handle_service_error(e)


@router.put(
    "/{trigger_id}",
    response_model=SuccessResponse[TriggerResponse],
    summary="Update trigger",
    description="""
    Update an existing trigger with comprehensive validation and reconfiguration.

    **Features:**
    - Partial updates (only specified fields are changed)
    - Automatic reconfiguration when trigger type or config changes
    - Proper handling of activation/deactivation
    - Validation of new configuration against adapter requirements
    - Atomic updates with rollback on failure
    """,
    responses={
        200: {
            "description": "Trigger updated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "data": {
                            "id": "123e4567-e89b-12d3-a456-************",
                            "name": "Updated Meeting Alerts",
                            "description": "Updated trigger description",
                            "trigger_type": "google_calendar",
                            "workflow_id": "wf_123",
                            "event_types": ["created", "updated", "deleted"],
                            "config": {"calendar_id": "primary", "webhook_ttl": 7200},
                            "status": "active",
                            "is_active": True,
                            "user_id": "user_123",
                            "created_at": "2024-01-01T10:00:00Z",
                            "updated_at": "2024-01-01T12:00:00Z",
                        },
                        "message": "Trigger updated and reconfigured successfully",
                    }
                }
            },
        },
        400: {"description": "Invalid update data"},
        404: {"description": "Trigger not found"},
        409: {"description": "Name conflict with existing trigger"},
    },
)
async def update_trigger(
    trigger_id: UUID = Path(
        ...,
        description="Unique identifier of the trigger to update",
        example="123e4567-e89b-12d3-a456-************",
    ),
    request: UpdateTriggerRequest = Body(
        ...,
        description="Trigger update data",
        examples={
            "partial_update": {
                "summary": "Partial update example",
                "value": {
                    "name": "Updated Meeting Notifications",
                    "description": "Updated description for meeting notifications",
                    "is_active": True,
                },
            },
            "config_update": {
                "summary": "Configuration update example",
                "value": {
                    "config": {
                        "calendar_id": "primary",
                        "webhook_ttl": 7200,
                        "event_filters": {
                            "attendee_count_min": 3,
                            "duration_min": 30,
                        },
                    },
                    "event_types": ["created", "updated", "deleted"],
                },
            },
            "deactivation": {
                "summary": "Deactivate trigger example",
                "value": {"is_active": False},
            },
        },
    ),
    session: AsyncSession = Depends(get_async_session),
    trigger_manager: TriggerManager = Depends(get_trigger_manager_dependency),
    current_user: str = Depends(get_authenticated_user),
) -> SuccessResponse[TriggerResponse]:
    """
    Update an existing trigger with comprehensive validation.

    This endpoint handles partial updates to triggers with:
    1. Validation of the existing trigger
    2. Conflict checking for name changes
    3. Configuration validation for changes
    4. Automatic reconfiguration when needed
    5. Proper activation/deactivation handling

    Only the fields provided in the request will be updated.
    Configuration changes may require reconfiguration with external services.
    """
    try:
        # Retrieve existing trigger
        query = select(Trigger).where(Trigger.id == trigger_id)
        result = await session.execute(query)
        trigger = result.scalar_one_or_none()

        if not trigger:
            raise NotFoundError(f"Trigger with ID {trigger_id} not found")

        # Track what changes are being made
        changes_made = []
        requires_reconfiguration = False

        # Check for name conflicts if name is being changed
        if request.name and request.name != trigger.name:
            existing_trigger_query = select(Trigger).where(
                and_(
                    Trigger.user_id == trigger.user_id,
                    Trigger.name == request.name,
                    Trigger.id != trigger_id,
                )
            )
            existing_trigger = await session.scalar(existing_trigger_query)

            if existing_trigger:
                raise ConflictError(
                    f"Trigger with name '{request.name}' already exists for this user"
                )

        # Update basic fields
        if request.name:
            trigger.name = request.name
            changes_made.append("name")

        if request.description is not None:
            trigger.description = request.description
            changes_made.append("description")

        if request.workflow_id:
            trigger.workflow_id = request.workflow_id
            changes_made.append("workflow_id")

        # Handle event types changes
        if request.event_types:
            # Validate event types for the adapter
            adapter = trigger_manager.get_adapter(trigger.trigger_type)
            if adapter:
                supported_event_types = adapter.get_supported_event_types()
                for event_type in request.event_types:
                    try:
                        event_enum = TriggerEventType(event_type)
                        if event_enum not in supported_event_types:
                            raise ValidationError(
                                f"Event type '{event_type}' not supported by {trigger.trigger_type} adapter"
                            )
                    except ValueError:
                        raise ValidationError(f"Invalid event type: {event_type}")

            if set(request.event_types) != set(trigger.event_types):
                trigger.event_types = request.event_types
                changes_made.append("event_types")
                requires_reconfiguration = True

        # Handle configuration changes
        if request.config:
            new_config = request.config.model_dump()
            if new_config != trigger.config:
                trigger.config = new_config
                changes_made.append("config")
                requires_reconfiguration = True

        # Handle activation state changes
        activation_changed = False
        if request.is_active is not None and request.is_active != trigger.is_active:
            activation_changed = True
            changes_made.append("is_active")

        # If no changes were made, return early
        if not changes_made:
            trigger_response = TriggerResponse.model_validate(trigger)
            return SuccessResponse.create(
                data=trigger_response,
                message="No changes detected - trigger unchanged",
            )

        # Handle reconfiguration if needed
        reconfiguration_success = True
        reconfiguration_error = None

        if requires_reconfiguration or activation_changed:
            try:
                # Remove existing configuration first
                if trigger.is_active:
                    await trigger_manager.remove_trigger(trigger.id, session)

                # Set up new configuration if activating
                if (
                    request.is_active is not False
                ):  # True or None (keeping current state)
                    from src.adapters.base import TriggerConfiguration

                    trigger_config = TriggerConfiguration(
                        trigger_id=trigger.id,
                        user_id=trigger.user_id,
                        trigger_type=trigger.trigger_type,
                        event_types=[
                            TriggerEventType(et) for et in trigger.event_types
                        ],
                        config=trigger.config,
                    )

                    reconfiguration_success = await trigger_manager.setup_trigger(
                        trigger_config, session
                    )

                    if reconfiguration_success:
                        trigger.status = TriggerStatus.ACTIVE
                        trigger.is_active = True
                        logger.info(f"Successfully reconfigured trigger {trigger_id}")
                    else:
                        trigger.status = TriggerStatus.ERROR
                        trigger.is_active = False
                        reconfiguration_error = "Failed to reconfigure trigger"
                        logger.error(f"Failed to reconfigure trigger {trigger_id}")
                else:
                    # Deactivating trigger
                    trigger.status = TriggerStatus.INACTIVE
                    trigger.is_active = False

            except Exception as e:
                reconfiguration_success = False
                reconfiguration_error = f"Reconfiguration error: {str(e)}"
                trigger.status = TriggerStatus.ERROR
                trigger.is_active = False
                logger.error(
                    f"Exception during trigger reconfiguration: {e}", exc_info=True
                )
        else:
            # Just update the activation state if no reconfiguration needed
            if request.is_active is not None:
                trigger.is_active = request.is_active
                if request.is_active:
                    trigger.status = TriggerStatus.ACTIVE
                else:
                    trigger.status = TriggerStatus.INACTIVE

        # Commit the transaction
        await session.commit()
        await session.refresh(trigger)

        # Convert to response model
        trigger_response = TriggerResponse.model_validate(trigger)

        # Create appropriate response message
        if reconfiguration_success or not (
            requires_reconfiguration or activation_changed
        ):
            if requires_reconfiguration:
                message = f"Trigger updated and reconfigured successfully. Changes: {', '.join(changes_made)}"
            else:
                message = (
                    f"Trigger updated successfully. Changes: {', '.join(changes_made)}"
                )
        else:
            message = f"Trigger updated but reconfiguration failed: {reconfiguration_error}. Changes: {', '.join(changes_made)}"

        logger.info(f"Updated trigger {trigger_id}: {message}")

        return SuccessResponse.create(
            data=trigger_response,
            message=message,
        )

    except NotFoundError as e:
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=ErrorResponse.create(message=str(e)).model_dump(),
        )
    except ConflictError as e:
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=ErrorResponse.create(message=str(e)).model_dump(),
        )
    except ValidationError as e:
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=ErrorResponse.create(message=str(e)).model_dump(),
        )
    except Exception as e:
        await session.rollback()
        logger.error(f"Failed to update trigger {trigger_id}: {e}", exc_info=True)
        triggers_router.handle_service_error(e)


@router.delete(
    "/{trigger_id}",
    response_model=SuccessResponse[Dict[str, Any]],
    summary="Delete trigger",
    description="""
    Delete a trigger and clean up all associated resources.

    **Features:**
    - Removes trigger from database
    - Cleans up external service integrations (webhooks, etc.)
    - Removes associated execution history
    - Atomic operation with rollback on failure
    - Optional force deletion for stuck triggers
    """,
    responses={
        200: {
            "description": "Trigger deleted successfully",
            "content": {
                "application/json": {
                    "example": {
                        "success": True,
                        "data": {
                            "trigger_id": "123e4567-e89b-12d3-a456-************",
                            "cleanup_performed": True,
                            "executions_removed": 15,
                        },
                        "message": "Trigger deleted and all resources cleaned up successfully",
                    }
                }
            },
        },
        404: {"description": "Trigger not found"},
        409: {
            "description": "Cannot delete active trigger (use force=true to override)"
        },
    },
)
async def delete_trigger(
    trigger_id: UUID = Path(
        ...,
        description="Unique identifier of the trigger to delete",
        example="123e4567-e89b-12d3-a456-************",
    ),
    force: bool = Query(
        False,
        description="Force deletion even if trigger is active or cleanup fails",
    ),
    cleanup_executions: bool = Query(
        True,
        description="Whether to remove associated execution history",
    ),
    session: AsyncSession = Depends(get_async_session),
    trigger_manager: TriggerManager = Depends(get_trigger_manager_dependency),
    current_user: str = Depends(get_authenticated_user),
) -> SuccessResponse[Dict[str, Any]]:
    """
    Delete a trigger and clean up all associated resources.

    This endpoint performs a comprehensive deletion process:
    1. Validates the trigger exists and user has permission
    2. Deactivates the trigger and cleans up external integrations
    3. Optionally removes execution history
    4. Removes the trigger from the database

    Use the `force` parameter to delete triggers even if cleanup fails.
    """
    try:
        # Retrieve the trigger with execution count
        query = select(Trigger).where(Trigger.id == trigger_id)
        result = await session.execute(query)
        trigger = result.scalar_one_or_none()

        if not trigger:
            raise NotFoundError(f"Trigger with ID {trigger_id} not found")

        # Check if trigger is active and force is not specified
        if trigger.is_active and not force:
            raise ConflictError(
                "Cannot delete active trigger. Deactivate first or use force=true parameter."
            )

        # Track cleanup operations
        cleanup_performed = False
        cleanup_error = None
        executions_removed = 0

        # Clean up external integrations if trigger is active
        if trigger.is_active:
            try:
                cleanup_success = await trigger_manager.remove_trigger(
                    trigger.id, session
                )
                cleanup_performed = cleanup_success

                if not cleanup_success and not force:
                    raise ExternalServiceError(
                        "Failed to clean up external service integrations. Use force=true to delete anyway."
                    )
                elif not cleanup_success:
                    cleanup_error = (
                        "External service cleanup failed but deletion forced"
                    )
                    logger.warning(
                        f"Forced deletion of trigger {trigger_id} despite cleanup failure"
                    )
                else:
                    logger.info(
                        f"Successfully cleaned up external integrations for trigger {trigger_id}"
                    )

            except Exception as e:
                cleanup_error = f"Cleanup error: {str(e)}"
                if not force:
                    raise ExternalServiceError(
                        f"Failed to clean up external integrations: {str(e)}. Use force=true to delete anyway."
                    )
                logger.warning(
                    f"Forced deletion of trigger {trigger_id} despite cleanup exception: {e}"
                )

        # Remove execution history if requested
        if cleanup_executions:
            try:
                # Count executions before deletion
                execution_count_query = select(func.count()).where(
                    TriggerExecution.trigger_id == trigger_id
                )
                executions_removed = await session.scalar(execution_count_query) or 0

                # Delete executions
                delete_executions_query = delete(TriggerExecution).where(
                    TriggerExecution.trigger_id == trigger_id
                )
                await session.execute(delete_executions_query)

                logger.info(
                    f"Removed {executions_removed} execution records for trigger {trigger_id}"
                )

            except Exception as e:
                if not force:
                    raise ExternalServiceError(
                        f"Failed to remove execution history: {str(e)}. Use force=true to delete anyway."
                    )
                logger.warning(
                    f"Failed to remove execution history for trigger {trigger_id}: {e}"
                )
                executions_removed = 0

        # Delete the trigger itself
        delete_trigger_query = delete(Trigger).where(Trigger.id == trigger_id)
        result = await session.execute(delete_trigger_query)

        if result.rowcount == 0:
            raise NotFoundError(f"Trigger {trigger_id} was not found during deletion")

        # Commit the transaction
        await session.commit()

        # Prepare response data
        response_data = {
            "trigger_id": str(trigger_id),
            "cleanup_performed": cleanup_performed,
            "executions_removed": executions_removed,
        }

        if cleanup_error:
            response_data["cleanup_warning"] = cleanup_error

        # Create appropriate response message
        message_parts = ["Trigger deleted"]
        if cleanup_performed:
            message_parts.append("and all resources cleaned up successfully")
        elif cleanup_error:
            message_parts.append(f"but with cleanup issues: {cleanup_error}")
        else:
            message_parts.append("successfully")

        if executions_removed > 0:
            message_parts.append(f"({executions_removed} execution records removed)")

        message = " ".join(message_parts)

        logger.info(f"Deleted trigger {trigger_id} for user {current_user}: {message}")

        return SuccessResponse.create(
            data=response_data,
            message=message,
        )

    except NotFoundError as e:
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail=ErrorResponse.create(message=str(e)).model_dump(),
        )
    except ConflictError as e:
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=ErrorResponse.create(message=str(e)).model_dump(),
        )
    except ExternalServiceError as e:
        await session.rollback()
        raise HTTPException(
            status_code=status.HTTP_409_CONFLICT,
            detail=ErrorResponse.create(message=str(e)).model_dump(),
        )
    except Exception as e:
        await session.rollback()
        logger.error(f"Failed to delete trigger {trigger_id}: {e}", exc_info=True)
        triggers_router.handle_service_error(e)


# Additional utility endpoints for trigger management


@router.post(
    "/{trigger_id}/activate",
    response_model=SuccessResponse[TriggerResponse],
    summary="Activate trigger",
    description="Activate a trigger and set up external integrations.",
)
async def activate_trigger(
    trigger_id: UUID = Path(..., description="Trigger ID to activate"),
    session: AsyncSession = Depends(get_async_session),
    trigger_manager: TriggerManager = Depends(get_trigger_manager_dependency),
    current_user: str = Depends(get_authenticated_user),
) -> SuccessResponse[TriggerResponse]:
    """Activate a trigger by setting up external integrations."""
    # Implementation would be similar to the activation logic in update_trigger
    # This is a convenience endpoint for activation without full update
    pass


@router.post(
    "/{trigger_id}/deactivate",
    response_model=SuccessResponse[TriggerResponse],
    summary="Deactivate trigger",
    description="Deactivate a trigger and clean up external integrations.",
)
async def deactivate_trigger(
    trigger_id: UUID = Path(..., description="Trigger ID to deactivate"),
    session: AsyncSession = Depends(get_async_session),
    trigger_manager: TriggerManager = Depends(get_trigger_manager_dependency),
    current_user: str = Depends(get_authenticated_user),
) -> SuccessResponse[TriggerResponse]:
    """Deactivate a trigger by cleaning up external integrations."""
    # Implementation would be similar to the deactivation logic in update_trigger
    # This is a convenience endpoint for deactivation without full update
    pass


@router.get(
    "/{trigger_id}/executions",
    response_model=PaginatedResponse[TriggerExecutionResponse],
    summary="Get trigger execution history",
    description="Retrieve paginated execution history for a specific trigger.",
)
async def get_trigger_executions(
    trigger_id: UUID = Path(..., description="Trigger ID"),
    pagination: PaginationParams = Depends(get_pagination_params),
    session: AsyncSession = Depends(get_async_session),
    current_user: str = Depends(get_authenticated_user),
) -> PaginatedResponse[TriggerExecutionResponse]:
    """Get execution history for a specific trigger."""
    # Implementation would query TriggerExecution table with pagination
    # Similar to the list_triggers endpoint but for executions
    pass
