"""
Triggers API routes with comprehensive CRUD operations.

This module provides professional-grade API endpoints for trigger management
with proper validation, error handling, pagination, filtering, and documentation.
"""

import logging
from typing import List, Optional, Dict, Any
from uuid import UUID

from fastapi import APIRouter, Depends, HTTPException, status, Query, Path, Body
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete, func, and_, or_
from sqlalchemy.orm import selectinload

from src.api.models import (
    CreateTriggerRequest,
    UpdateTriggerRequest,
    TriggerResponse,
    TriggerExecutionResponse,
    TriggerConfigModel,
)
from src.database.connection import get_async_session
from src.database.models import Trigger, TriggerExecution, TriggerStatus
from src.adapters.base import TriggerEventType
from src.core.dependencies import get_trigger_manager_dependency
from src.core.trigger_manager import TriggerManager
from src.api.middleware.auth import get_authenticated_user
from src.utils.logger import get_logger
from src.core.exceptions import (
    ValidationError,
    TriggerNotFoundError,
    TriggerConfigurationError,
    ExternalServiceError,
)

logger = get_logger(__name__)

# Create router with comprehensive configuration
router = APIRouter(
    prefix="/api/v1/triggers",
    tags=["triggers"],
    responses={
        400: {"description": "Bad Request - Invalid input data"},
        401: {"description": "Unauthorized - Authentication required"},
        403: {"description": "Forbidden - Insufficient permissions"},
        404: {"description": "Not Found - Resource not found"},
        409: {"description": "Conflict - Resource already exists"},
        422: {"description": "Unprocessable Entity - Validation error"},
        500: {"description": "Internal Server Error"},
    },
)


# Simple response models for basic operations
class SuccessResponse:
    @staticmethod
    def create(data: Any, message: str = "Operation successful"):
        return {"success": True, "data": data, "message": message}


class ErrorResponse:
    @staticmethod
    def create(message: str, code: str = "ERROR"):
        return {"success": False, "error": {"code": code, "message": message}}


class PaginationParams:
    def __init__(self, page: int = 1, page_size: int = 20):
        self.page = max(1, page)
        self.page_size = min(max(1, page_size), 100)
        self.offset = (self.page - 1) * self.page_size
        self.limit = self.page_size


def get_pagination_params(
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
) -> PaginationParams:
    return PaginationParams(page, page_size)


@router.get(
    "/",
    summary="List all triggers",
    description="Retrieve a paginated list of all triggers with optional filtering and sorting.",
)
async def list_triggers(
    pagination: PaginationParams = Depends(get_pagination_params),
    search: Optional[str] = Query(
        None, description="Search triggers by name or description"
    ),
    trigger_type: Optional[str] = Query(None, description="Filter by trigger type"),
    status: Optional[str] = Query(None, description="Filter by trigger status"),
    user_id: Optional[str] = Query(None, description="Filter by user ID"),
    session: AsyncSession = Depends(get_async_session),
    current_user: str = Depends(get_authenticated_user),
):
    """List triggers with comprehensive filtering and pagination."""
    try:
        # Build base query
        query = select(Trigger)

        # Apply search filter
        if search:
            search_filter = or_(
                Trigger.name.ilike(f"%{search}%"),
                Trigger.description.ilike(f"%{search}%"),
            )
            query = query.where(search_filter)

        # Apply filters
        if trigger_type:
            query = query.where(Trigger.trigger_type == trigger_type)

        if status:
            try:
                status_enum = TriggerStatus(status)
                query = query.where(Trigger.status == status_enum)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid status: {status}",
                )

        if user_id:
            query = query.where(Trigger.user_id == user_id)

        # Get total count for pagination
        count_query = select(func.count()).select_from(query.subquery())
        total_count = await session.scalar(count_query)

        # Apply pagination and ordering
        query = query.order_by(Trigger.created_at.desc())
        query = query.offset(pagination.offset).limit(pagination.limit)

        # Execute query
        result = await session.execute(query)
        triggers = result.scalars().all()

        # Convert to response format
        trigger_data = []
        for trigger in triggers:
            trigger_dict = {
                "id": str(trigger.id),
                "name": trigger.name,
                "description": trigger.description,
                "trigger_type": trigger.trigger_type,
                "workflow_id": trigger.workflow_id,
                "event_types": trigger.event_types,
                "config": trigger.config,
                "status": trigger.status.value if trigger.status else "unknown",
                "is_active": trigger.is_active,
                "user_id": trigger.user_id,
                "created_at": (
                    trigger.created_at.isoformat() if trigger.created_at else None
                ),
                "updated_at": (
                    trigger.updated_at.isoformat() if trigger.updated_at else None
                ),
            }
            trigger_data.append(trigger_dict)

        return SuccessResponse.create(
            data={
                "triggers": trigger_data,
                "pagination": {
                    "page": pagination.page,
                    "page_size": pagination.page_size,
                    "total": total_count or 0,
                    "pages": ((total_count or 0) + pagination.page_size - 1)
                    // pagination.page_size,
                },
            },
            message=f"Retrieved {len(trigger_data)} triggers",
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to list triggers: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ErrorResponse.create(f"Internal server error: {str(e)}"),
        )


@router.post(
    "/",
    status_code=status.HTTP_201_CREATED,
    summary="Create a new trigger",
    description="Create a new trigger with comprehensive validation and setup.",
)
async def create_trigger(
    request: CreateTriggerRequest = Body(..., description="Trigger creation data"),
    user_id: str = Query(..., description="User ID for the trigger owner"),
    session: AsyncSession = Depends(get_async_session),
    trigger_manager: TriggerManager = Depends(get_trigger_manager_dependency),
    current_user: str = Depends(get_authenticated_user),
):
    """Create a new trigger with comprehensive validation and setup."""
    try:
        # Check for naming conflicts
        existing_trigger_query = select(Trigger).where(
            and_(Trigger.user_id == user_id, Trigger.name == request.name)
        )
        existing_trigger = await session.scalar(existing_trigger_query)

        if existing_trigger:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=ErrorResponse.create(
                    f"Trigger with name '{request.name}' already exists for user {user_id}"
                ),
            )

        # Validate trigger type is supported
        available_adapters = trigger_manager.list_available_adapters()
        if request.trigger_type not in available_adapters:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=ErrorResponse.create(
                    f"Unsupported trigger type: {request.trigger_type}. "
                    f"Available types: {', '.join(available_adapters)}"
                ),
            )

        # Create trigger instance
        trigger = Trigger(
            user_id=user_id,
            name=request.name,
            description=request.description,
            trigger_type=request.trigger_type,
            workflow_id=request.workflow_id,
            event_types=request.event_types,
            config=request.config.model_dump() if request.config else {},
            is_active=request.is_active,
            status=TriggerStatus.PENDING,
        )

        # Add to session and flush to get ID
        session.add(trigger)
        await session.flush()

        # Set up trigger with adapter if active
        setup_success = True
        setup_error = None

        if request.is_active:
            try:
                from src.adapters.base import TriggerConfiguration

                trigger_config = TriggerConfiguration(
                    trigger_id=trigger.id,
                    user_id=user_id,
                    trigger_type=request.trigger_type,
                    event_types=[TriggerEventType(et) for et in request.event_types],
                    config=request.config.model_dump() if request.config else {},
                )

                setup_success = await trigger_manager.setup_trigger(
                    trigger_config, session
                )

                if setup_success:
                    trigger.status = TriggerStatus.ACTIVE
                else:
                    trigger.status = TriggerStatus.ERROR
                    trigger.is_active = False
                    setup_error = "Failed to set up trigger with external service"

            except Exception as e:
                setup_success = False
                setup_error = f"Setup error: {str(e)}"
                trigger.status = TriggerStatus.ERROR
                trigger.is_active = False
                logger.error(f"Exception during trigger setup: {e}", exc_info=True)
        else:
            trigger.status = TriggerStatus.INACTIVE

        # Commit the transaction
        await session.commit()
        await session.refresh(trigger)

        # Convert to response format
        trigger_data = {
            "id": str(trigger.id),
            "name": trigger.name,
            "description": trigger.description,
            "trigger_type": trigger.trigger_type,
            "workflow_id": trigger.workflow_id,
            "event_types": trigger.event_types,
            "config": trigger.config,
            "status": trigger.status.value if trigger.status else "unknown",
            "is_active": trigger.is_active,
            "user_id": trigger.user_id,
            "created_at": (
                trigger.created_at.isoformat() if trigger.created_at else None
            ),
            "updated_at": (
                trigger.updated_at.isoformat() if trigger.updated_at else None
            ),
        }

        # Create appropriate response message
        if setup_success and request.is_active:
            message = "Trigger created and activated successfully"
        elif not request.is_active:
            message = "Trigger created successfully (inactive)"
        else:
            message = f"Trigger created but activation failed: {setup_error}"

        logger.info(f"Created trigger {trigger.id} for user {user_id}: {message}")

        return SuccessResponse.create(data=trigger_data, message=message)

    except HTTPException:
        await session.rollback()
        raise
    except Exception as e:
        await session.rollback()
        logger.error(f"Failed to create trigger: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ErrorResponse.create(f"Internal server error: {str(e)}"),
        )


@router.get(
    "/{trigger_id}",
    summary="Get trigger by ID",
    description="Retrieve a specific trigger by its ID with optional execution history.",
)
async def get_trigger(
    trigger_id: UUID = Path(..., description="Unique identifier of the trigger"),
    include_executions: bool = Query(
        False, description="Include recent execution history"
    ),
    session: AsyncSession = Depends(get_async_session),
    current_user: str = Depends(get_authenticated_user),
):
    """Retrieve a specific trigger by ID."""
    try:
        # Build query
        query = select(Trigger).where(Trigger.id == trigger_id)

        # Execute query
        result = await session.execute(query)
        trigger = result.scalar_one_or_none()

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ErrorResponse.create(f"Trigger with ID {trigger_id} not found"),
            )

        # Convert to response format
        trigger_data = {
            "id": str(trigger.id),
            "name": trigger.name,
            "description": trigger.description,
            "trigger_type": trigger.trigger_type,
            "workflow_id": trigger.workflow_id,
            "event_types": trigger.event_types,
            "config": trigger.config,
            "status": trigger.status.value if trigger.status else "unknown",
            "is_active": trigger.is_active,
            "user_id": trigger.user_id,
            "created_at": (
                trigger.created_at.isoformat() if trigger.created_at else None
            ),
            "updated_at": (
                trigger.updated_at.isoformat() if trigger.updated_at else None
            ),
        }

        # Add execution statistics if requested
        if include_executions:
            execution_query = (
                select(TriggerExecution)
                .where(TriggerExecution.trigger_id == trigger_id)
                .order_by(TriggerExecution.created_at.desc())
                .limit(10)
            )

            execution_result = await session.execute(execution_query)
            executions = execution_result.scalars().all()

            trigger_data["recent_executions"] = [
                {
                    "id": str(exec.id),
                    "status": exec.status.value if exec.status else "unknown",
                    "started_at": (
                        exec.started_at.isoformat() if exec.started_at else None
                    ),
                    "completed_at": (
                        exec.completed_at.isoformat() if exec.completed_at else None
                    ),
                    "error_message": exec.error_message,
                }
                for exec in executions
            ]

        logger.info(f"Retrieved trigger {trigger_id} for user {current_user}")

        return SuccessResponse.create(
            data=trigger_data, message="Trigger retrieved successfully"
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Failed to get trigger {trigger_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ErrorResponse.create(f"Internal server error: {str(e)}"),
        )


@router.put(
    "/{trigger_id}",
    summary="Update trigger",
    description="Update an existing trigger with comprehensive validation and reconfiguration.",
)
async def update_trigger(
    trigger_id: UUID = Path(
        ..., description="Unique identifier of the trigger to update"
    ),
    request: UpdateTriggerRequest = Body(..., description="Trigger update data"),
    session: AsyncSession = Depends(get_async_session),
    trigger_manager: TriggerManager = Depends(get_trigger_manager_dependency),
    current_user: str = Depends(get_authenticated_user),
):
    """Update an existing trigger with comprehensive validation."""
    try:
        # Retrieve existing trigger
        query = select(Trigger).where(Trigger.id == trigger_id)
        result = await session.execute(query)
        trigger = result.scalar_one_or_none()

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ErrorResponse.create(f"Trigger with ID {trigger_id} not found"),
            )

        # Track what changes are being made
        changes_made = []

        # Update basic fields
        if request.name and request.name != trigger.name:
            # Check for name conflicts
            existing_trigger_query = select(Trigger).where(
                and_(
                    Trigger.user_id == trigger.user_id,
                    Trigger.name == request.name,
                    Trigger.id != trigger_id,
                )
            )
            existing_trigger = await session.scalar(existing_trigger_query)

            if existing_trigger:
                raise HTTPException(
                    status_code=status.HTTP_409_CONFLICT,
                    detail=ErrorResponse.create(
                        f"Trigger with name '{request.name}' already exists for this user"
                    ),
                )

            trigger.name = request.name
            changes_made.append("name")

        if request.description is not None:
            trigger.description = request.description
            changes_made.append("description")

        if request.workflow_id:
            trigger.workflow_id = request.workflow_id
            changes_made.append("workflow_id")

        if request.event_types:
            trigger.event_types = request.event_types
            changes_made.append("event_types")

        if request.config:
            trigger.config = request.config.model_dump()
            changes_made.append("config")

        if request.is_active is not None:
            trigger.is_active = request.is_active
            changes_made.append("is_active")

        # If no changes were made, return early
        if not changes_made:
            trigger_data = {
                "id": str(trigger.id),
                "name": trigger.name,
                "description": trigger.description,
                "trigger_type": trigger.trigger_type,
                "workflow_id": trigger.workflow_id,
                "event_types": trigger.event_types,
                "config": trigger.config,
                "status": trigger.status.value if trigger.status else "unknown",
                "is_active": trigger.is_active,
                "user_id": trigger.user_id,
                "created_at": (
                    trigger.created_at.isoformat() if trigger.created_at else None
                ),
                "updated_at": (
                    trigger.updated_at.isoformat() if trigger.updated_at else None
                ),
            }
            return SuccessResponse.create(
                data=trigger_data, message="No changes detected - trigger unchanged"
            )

        # Update status based on active state
        if request.is_active:
            trigger.status = TriggerStatus.ACTIVE
        else:
            trigger.status = TriggerStatus.INACTIVE

        # Commit the transaction
        await session.commit()
        await session.refresh(trigger)

        # Convert to response format
        trigger_data = {
            "id": str(trigger.id),
            "name": trigger.name,
            "description": trigger.description,
            "trigger_type": trigger.trigger_type,
            "workflow_id": trigger.workflow_id,
            "event_types": trigger.event_types,
            "config": trigger.config,
            "status": trigger.status.value if trigger.status else "unknown",
            "is_active": trigger.is_active,
            "user_id": trigger.user_id,
            "created_at": (
                trigger.created_at.isoformat() if trigger.created_at else None
            ),
            "updated_at": (
                trigger.updated_at.isoformat() if trigger.updated_at else None
            ),
        }

        message = f"Trigger updated successfully. Changes: {', '.join(changes_made)}"
        logger.info(f"Updated trigger {trigger_id}: {message}")

        return SuccessResponse.create(data=trigger_data, message=message)

    except HTTPException:
        await session.rollback()
        raise
    except Exception as e:
        await session.rollback()
        logger.error(f"Failed to update trigger {trigger_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ErrorResponse.create(f"Internal server error: {str(e)}"),
        )


@router.delete(
    "/{trigger_id}",
    summary="Delete trigger",
    description="Delete a trigger and clean up all associated resources.",
)
async def delete_trigger(
    trigger_id: UUID = Path(
        ..., description="Unique identifier of the trigger to delete"
    ),
    force: bool = Query(False, description="Force deletion even if trigger is active"),
    cleanup_executions: bool = Query(
        True, description="Whether to remove associated execution history"
    ),
    session: AsyncSession = Depends(get_async_session),
    trigger_manager: TriggerManager = Depends(get_trigger_manager_dependency),
    current_user: str = Depends(get_authenticated_user),
):
    """Delete a trigger and clean up all associated resources."""
    try:
        # Retrieve the trigger
        query = select(Trigger).where(Trigger.id == trigger_id)
        result = await session.execute(query)
        trigger = result.scalar_one_or_none()

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ErrorResponse.create(f"Trigger with ID {trigger_id} not found"),
            )

        # Check if trigger is active and force is not specified
        if trigger.is_active and not force:
            raise HTTPException(
                status_code=status.HTTP_409_CONFLICT,
                detail=ErrorResponse.create(
                    "Cannot delete active trigger. Deactivate first or use force=true parameter."
                ),
            )

        # Track cleanup operations
        cleanup_performed = False
        executions_removed = 0

        # Clean up external integrations if trigger is active
        if trigger.is_active:
            try:
                cleanup_success = await trigger_manager.remove_trigger(
                    trigger.id, session
                )
                cleanup_performed = cleanup_success

                if not cleanup_success and not force:
                    raise HTTPException(
                        status_code=status.HTTP_409_CONFLICT,
                        detail=ErrorResponse.create(
                            "Failed to clean up external service integrations. Use force=true to delete anyway."
                        ),
                    )

            except Exception as e:
                if not force:
                    raise HTTPException(
                        status_code=status.HTTP_409_CONFLICT,
                        detail=ErrorResponse.create(
                            f"Failed to clean up external integrations: {str(e)}. Use force=true to delete anyway."
                        ),
                    )
                logger.warning(
                    f"Forced deletion of trigger {trigger_id} despite cleanup exception: {e}"
                )

        # Remove execution history if requested
        if cleanup_executions:
            try:
                # Count executions before deletion
                execution_count_query = select(func.count()).where(
                    TriggerExecution.trigger_id == trigger_id
                )
                executions_removed = await session.scalar(execution_count_query) or 0

                # Delete executions
                delete_executions_query = delete(TriggerExecution).where(
                    TriggerExecution.trigger_id == trigger_id
                )
                await session.execute(delete_executions_query)

            except Exception as e:
                if not force:
                    raise HTTPException(
                        status_code=status.HTTP_409_CONFLICT,
                        detail=ErrorResponse.create(
                            f"Failed to remove execution history: {str(e)}. Use force=true to delete anyway."
                        ),
                    )
                logger.warning(
                    f"Failed to remove execution history for trigger {trigger_id}: {e}"
                )
                executions_removed = 0

        # Delete the trigger itself
        delete_trigger_query = delete(Trigger).where(Trigger.id == trigger_id)
        result = await session.execute(delete_trigger_query)

        if result.rowcount == 0:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=ErrorResponse.create(
                    f"Trigger {trigger_id} was not found during deletion"
                ),
            )

        # Commit the transaction
        await session.commit()

        # Prepare response data
        response_data = {
            "trigger_id": str(trigger_id),
            "cleanup_performed": cleanup_performed,
            "executions_removed": executions_removed,
        }

        # Create appropriate response message
        message_parts = ["Trigger deleted"]
        if cleanup_performed:
            message_parts.append("and all resources cleaned up successfully")
        else:
            message_parts.append("successfully")

        if executions_removed > 0:
            message_parts.append(f"({executions_removed} execution records removed)")

        message = " ".join(message_parts)

        logger.info(f"Deleted trigger {trigger_id} for user {current_user}: {message}")

        return SuccessResponse.create(data=response_data, message=message)

    except HTTPException:
        await session.rollback()
        raise
    except Exception as e:
        await session.rollback()
        logger.error(f"Failed to delete trigger {trigger_id}: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=ErrorResponse.create(f"Internal server error: {str(e)}"),
        )
