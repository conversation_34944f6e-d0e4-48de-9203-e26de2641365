"""
Trigger management API endpoints.

This module provides REST API endpoints for managing triggers including
CRUD operations and trigger lifecycle management.
"""

from fastapi import APIRouter, HTTPException, Depends, Query, Request, status
from typing import List, Optional, Dict, Any
from uuid import UUID

from src.schemas.trigger import (
    TriggerCreate,
    TriggerUpdate,
    TriggerResponse,
    TriggerListResponse,
    TriggerToggleRequest,
    TriggerFilterRequest,
    TriggerStatsResponse,
    TriggerExecutionResponse,
    TriggerTypesResponse,
    TriggerTypeInfo,
)
from src.core.trigger_manager import TriggerManager
from src.api.middleware.auth import get_authenticated_user
from src.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/triggers", tags=["Triggers"])


# Dependency to get trigger manager instance
def get_trigger_manager() -> TriggerManager:
    """Get trigger manager instance."""
    # In a real application, this would be injected via dependency injection
    # For now, we'll create a singleton instance
    if not hasattr(get_trigger_manager, "_instance"):
        from src.adapters import GoogleCalendarAdapter

        get_trigger_manager._instance = TriggerManager()

        # Register available adapters
        google_calendar_adapter = GoogleCalendarAdapter()
        get_trigger_manager._instance.register_adapter(google_calendar_adapter)

    return get_trigger_manager._instance


@router.get(
    "/types",
    response_model=TriggerTypesResponse,
    summary="Get available trigger types",
    description="""
    Retrieve information about all available trigger types and their configurations.

    **Public Endpoint**: This endpoint does not require authentication and can be accessed by anyone.

    ## Purpose
    This endpoint helps developers understand:
    - What trigger types are available in the system
    - Configuration options for each trigger type
    - Supported event types for each trigger
    - Sample event data structures
    - Available fields for field mapping

    ## Use Cases
    - Building trigger creation forms in frontend applications
    - Understanding trigger capabilities before implementation
    - Validating trigger configurations
    - Exploring available integration options

    ## Response Structure
    Returns detailed information for each trigger type including:
    - **trigger_type**: Unique identifier for the trigger type
    - **name**: Human-readable name
    - **description**: Detailed description of functionality
    - **supported_event_types**: List of events this trigger can monitor
    - **configuration_schema**: JSON schema for configuration validation
    - **sample_event_data**: Example of event data structure
    - **available_fields**: Fields available for mapping to workflows
    """,
    responses={
        200: {
            "description": "Available trigger types retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "trigger_types": [
                            {
                                "trigger_type": "google_calendar",
                                "name": "Google Calendar",
                                "description": "Monitor Google Calendar events and execute workflows when events are created, updated, deleted, or reminders are triggered.",
                                "supported_event_types": [
                                    "created",
                                    "updated",
                                    "deleted",
                                    "reminder",
                                ],
                                "configuration_schema": {
                                    "type": "object",
                                    "properties": {
                                        "calendar_id": {
                                            "type": "string",
                                            "default": "primary",
                                        },
                                        "use_polling": {
                                            "type": "boolean",
                                            "default": False,
                                        },
                                    },
                                },
                                "sample_event_data": {
                                    "event_type": "created",
                                    "calendar_event": {
                                        "summary": "Team Meeting",
                                        "start": {"dateTime": "2025-01-02T10:00:00Z"},
                                    },
                                },
                                "available_fields": [
                                    "summary",
                                    "start.dateTime",
                                    "end.dateTime",
                                    "attendees",
                                ],
                            }
                        ],
                        "total_count": 1,
                    }
                }
            },
        },
        500: {
            "description": "Internal server error",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Internal server error while retrieving trigger types"
                    }
                }
            },
        },
    },
)
async def get_trigger_types(
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerTypesResponse:
    """
    Get information about all available trigger types.

    This endpoint is public and does not require authentication.
    It returns information about all supported trigger types including
    their configuration schemas and sample event data.

    Returns:
        TriggerTypesResponse: Information about all available trigger types

    Raises:
        HTTPException: If there's an error retrieving trigger types
    """
    try:
        # Get all registered adapter names from the trigger manager
        adapter_names = trigger_manager.list_adapters()

        trigger_types = []
        for adapter_name in adapter_names:
            # Get the adapter instance
            adapter = trigger_manager.get_adapter(adapter_name)
            if not adapter:
                continue

            # Get adapter information
            adapter_info = adapter.get_adapter_info()

            # Create trigger type info
            trigger_type = TriggerTypeInfo(
                trigger_type=adapter_info.get("trigger_type", adapter_name),
                name=adapter_info.get("name", adapter_name.replace("_", " ").title()),
                description=adapter_info.get(
                    "description", f"{adapter_name} trigger adapter"
                ),
                supported_event_types=adapter_info.get("supported_event_types", []),
                configuration_schema=adapter_info.get("configuration_schema", {}),
                sample_event_data=adapter_info.get("sample_event_data", {}),
                available_fields=adapter_info.get("available_fields", []),
            )
            trigger_types.append(trigger_type)

        return TriggerTypesResponse(
            trigger_types=trigger_types, total_count=len(trigger_types)
        )

    except Exception as e:
        logger.error("Error retrieving trigger types", error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving trigger types",
        )


@router.post(
    "/",
    response_model=TriggerResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new trigger",
    description="""
    Create a new trigger for workflow automation.

    **Authentication Required**: This endpoint requires authentication using either:
    - Bearer token in Authorization header: `Authorization: Bearer <your-token>`
    - API key in X-API-Key header: `X-API-Key: <your-api-key>`

    ## Supported Trigger Types

    ### Google Calendar (`google_calendar`)
    Monitor Google Calendar events and execute workflows when events are created, updated, deleted, or reminders are triggered.

    **Supported Event Types**: `created`, `updated`, `deleted`, `reminder`

    **Configuration Fields**:
    - `calendar_id` (string, optional): Calendar ID to monitor (default: "primary")
    - `use_polling` (boolean, optional): Force polling mode instead of webhooks (default: false)
    - `poll_interval_seconds` (integer, optional): Polling interval 15-3600 seconds (default: 60)
    - `webhook_ttl` (integer, optional): Webhook subscription TTL 3600-2592000 seconds (default: 604800)
    - `event_filters` (object, optional): Event filtering criteria
    - `selected_event_fields` (array, optional): Map specific calendar fields to workflow fields

    ### Google Drive Service Account (`google_drive_service_account`)
    Monitor Google Drive files using service account credentials for organization-wide monitoring.

    **Supported Event Types**: `file_created`, `file_updated`, `file_deleted`, `file_moved`

    **Configuration Fields**:
    - `organization_id` (string, required): Organization ID for service account credentials
    - `folder_ids` (array, optional): Specific folder IDs to monitor
    - `file_types` (array, optional): MIME types to monitor (e.g., ["application/pdf"])
    - `use_polling` (boolean, optional): Force polling mode (default: false)
    - `poll_interval_seconds` (integer, optional): Polling interval 60-3600 seconds (default: 300)
    - `webhook_ttl` (integer, optional): Webhook subscription TTL (default: 604800)
    - `event_filters` (object, optional): Event filtering criteria
    - `selected_file_fields` (array, optional): Map specific drive fields to workflow fields

    ## Example Requests

    **Google Calendar Trigger**:
    ```json
    {
      "workflow_id": "wf_meeting_workflow",
      "trigger_type": "google_calendar",
      "trigger_name": "Meeting Notifications",
      "trigger_config": {
        "calendar_id": "primary",
        "use_polling": false,
        "webhook_ttl": 604800,
        "event_filters": {
          "title_contains": ["meeting", "call"],
          "attendee_count_min": 2
        },
        "selected_event_fields": [
          {
            "calendar_field": "summary",
            "workflow_field": "event_title",
            "field_type": "string"
          },
          {
            "calendar_field": "start.dateTime",
            "workflow_field": "start_time",
            "field_type": "string"
          }
        ]
      },
      "event_types": ["created", "updated"]
    }
    ```

    **Google Drive Service Account Trigger**:
    ```json
    {
      "workflow_id": "wf_document_processor",
      "trigger_type": "google_drive_service_account",
      "trigger_name": "Document Processing",
      "trigger_config": {
        "organization_id": "org_123456",
        "folder_ids": ["1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"],
        "file_types": ["application/pdf", "application/vnd.google-apps.document"],
        "selected_file_fields": [
          {
            "drive_field": "name",
            "workflow_field": "file_name",
            "field_type": "string"
          },
          {
            "drive_field": "mimeType",
            "workflow_field": "file_type",
            "field_type": "string"
          }
        ]
      },
      "event_types": ["file_created", "file_updated"]
    }
    ```
    """,
    responses={
        201: {
            "description": "Trigger created successfully",
            "content": {
                "application/json": {
                    "example": {
                        "id": "550e8400-e29b-41d4-a716-************",
                        "user_id": "user_123",
                        "workflow_id": "wf_meeting_workflow",
                        "trigger_type": "google_calendar",
                        "trigger_name": "Meeting Notifications",
                        "trigger_config": {
                            "calendar_id": "primary",
                            "use_polling": False,
                            "webhook_ttl": 604800,
                        },
                        "event_types": ["created", "updated"],
                        "is_active": True,
                        "created_at": "2025-01-01T10:30:00Z",
                        "updated_at": "2025-01-01T10:30:00Z",
                        "last_triggered_at": None,
                    }
                }
            },
        },
        400: {
            "description": "Invalid request data - validation errors",
            "content": {
                "application/json": {
                    "example": {
                        "detail": [
                            {
                                "loc": ["body", "workflow_id"],
                                "msg": "field required",
                                "type": "value_error.missing",
                            }
                        ]
                    }
                }
            },
        },
        401: {
            "description": "Authentication required",
            "content": {
                "application/json": {"example": {"detail": "Authentication required"}}
            },
        },
        500: {
            "description": "Internal server error",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Internal server error while creating trigger"
                    }
                }
            },
        },
    },
)
async def create_trigger(
    trigger_data: TriggerCreate,
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerResponse:
    """
    Create a new trigger.

    Args:
        trigger_data: Trigger creation data
        current_user: Authenticated user information from bearer token
        trigger_manager: Trigger manager instance

    Returns:
        TriggerResponse: Created trigger details

    Raises:
        HTTPException: If trigger creation fails
    """

    # Extract user ID from authenticated user
    user_id = current_user.get("id")
    if not user_id:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="User ID not available from authentication",
        )

    try:

        trigger_id = await trigger_manager.create_trigger(
            user_id=user_id,
            workflow_id=trigger_data.workflow_id,
            trigger_type=trigger_data.trigger_type,
            trigger_name=trigger_data.trigger_name,
            trigger_config=trigger_data.trigger_config,
            event_types=trigger_data.event_types,
        )

        if not trigger_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to create trigger",
            )

        # Get the created trigger details
        triggers = await trigger_manager.get_triggers_for_user(user_id)
        created_trigger = next((t for t in triggers if t.id == trigger_id), None)

        if not created_trigger:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Trigger created but could not retrieve details",
            )

        return TriggerResponse.from_orm(created_trigger)

    except HTTPException:
        raise
    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while creating trigger",
        )


@router.get(
    "/",
    response_model=TriggerListResponse,
    summary="List triggers with filtering and pagination",
    description="""
    Retrieve a paginated list of triggers with optional filtering capabilities.

    **Authentication Required**: This endpoint requires authentication using either:
    - Bearer token in Authorization header: `Authorization: Bearer <your-token>`
    - API key in X-API-Key header: `X-API-Key: <your-api-key>`

    ## Query Parameters

    ### Filtering Options
    - `user_id` (string, optional): Filter by specific user ID (admin only, defaults to authenticated user)
    - `workflow_id` (string, optional): Filter by specific workflow ID
    - `trigger_type` (string, optional): Filter by trigger type (`google_calendar`, `google_drive_service_account`, etc.)
    - `is_active` (boolean, optional): Filter by active status (`true` for active, `false` for inactive)

    ### Pagination Options
    - `page` (integer, optional): Page number starting from 1 (default: 1, minimum: 1)
    - `page_size` (integer, optional): Number of items per page (default: 20, minimum: 1, maximum: 100)

    ## Example Requests

    **Get all triggers (first page)**:
    ```
    GET /api/v1/triggers/
    ```

    **Get active Google Calendar triggers**:
    ```
    GET /api/v1/triggers/?trigger_type=google_calendar&is_active=true
    ```

    **Get triggers for specific workflow with pagination**:
    ```
    GET /api/v1/triggers/?workflow_id=wf_123&page=2&page_size=10
    ```

    **Get inactive triggers**:
    ```
    GET /api/v1/triggers/?is_active=false
    ```

    ## Response Format
    The response includes the list of triggers along with pagination metadata to help with navigation.
    """,
    responses={
        200: {
            "description": "List of triggers retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "triggers": [
                            {
                                "id": "550e8400-e29b-41d4-a716-************",
                                "user_id": "user_123",
                                "workflow_id": "wf_meeting_workflow",
                                "trigger_type": "google_calendar",
                                "trigger_name": "Meeting Notifications",
                                "trigger_config": {
                                    "calendar_id": "primary",
                                    "use_polling": False,
                                },
                                "event_types": ["created", "updated"],
                                "is_active": True,
                                "created_at": "2025-01-01T10:30:00Z",
                                "updated_at": "2025-01-01T10:30:00Z",
                                "last_triggered_at": "2025-01-01T15:45:00Z",
                            }
                        ],
                        "total": 25,
                        "page": 1,
                        "page_size": 20,
                    }
                }
            },
        },
        401: {
            "description": "Authentication required",
            "content": {
                "application/json": {"example": {"detail": "Authentication required"}}
            },
        },
        500: {
            "description": "Internal server error",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Internal server error while listing triggers"
                    }
                }
            },
        },
    },
)
async def list_triggers(
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    user_id: Optional[str] = Query(None, description="Filter by user ID (admin only)"),
    workflow_id: Optional[str] = Query(None, description="Filter by workflow ID"),
    trigger_type: Optional[str] = Query(None, description="Filter by trigger type"),
    is_active: Optional[bool] = Query(None, description="Filter by active status"),
    page: int = Query(1, ge=1, description="Page number"),
    page_size: int = Query(20, ge=1, le=100, description="Items per page"),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerListResponse:
    """
    List triggers with optional filtering and pagination.

    Args:
        current_user: Authenticated user information from bearer token
        user_id: Optional user ID filter (admin only, defaults to current user)
        workflow_id: Optional workflow ID filter
        trigger_type: Optional trigger type filter
        is_active: Optional active status filter
        page: Page number for pagination
        page_size: Number of items per page
        trigger_manager: Trigger manager instance

    Returns:
        TriggerListResponse: Paginated list of triggers
    """

    try:
        # Extract authenticated user ID
        authenticated_user_id = current_user.get("id")
        if not authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User ID not available from authentication",
            )

        # For security, only allow users to see their own triggers unless they're admin
        # For now, we'll restrict to the authenticated user's triggers only
        # TODO: Add admin role checking if needed
        filter_user_id = authenticated_user_id

        # Use the new list_triggers method with database-level pagination
        triggers, total_count = await trigger_manager.list_triggers(
            user_id=filter_user_id,
            workflow_id=workflow_id,
            trigger_type=trigger_type,
            is_active=is_active,
            page=page,
            page_size=page_size,
        )

        # Convert to response models
        trigger_responses = [TriggerResponse.from_orm(t) for t in triggers]

        # Calculate total pages
        total_pages = (total_count + page_size - 1) // page_size

        return TriggerListResponse(
            triggers=trigger_responses,
            total=total_count,
            page=page,
            page_size=page_size,
            total_pages=total_pages,
        )

    except HTTPException:
        raise
    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while listing triggers",
        )


@router.get(
    "/{trigger_id}",
    response_model=TriggerResponse,
    summary="Get trigger by ID",
    description="""
    Retrieve detailed information about a specific trigger by its ID.

    **Authentication Required**: This endpoint requires authentication using either:
    - Bearer token in Authorization header: `Authorization: Bearer <your-token>`
    - API key in X-API-Key header: `X-API-Key: <your-api-key>`

    ## Path Parameters
    - `trigger_id` (UUID, required): The unique identifier of the trigger to retrieve

    ## Security
    Users can only access triggers that belong to them. Attempting to access another user's trigger will result in a 403 Forbidden error.

    ## Example Request
    ```
    GET /api/v1/triggers/550e8400-e29b-41d4-a716-************
    ```
    """,
    responses={
        200: {
            "description": "Trigger details retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "id": "550e8400-e29b-41d4-a716-************",
                        "user_id": "user_123",
                        "workflow_id": "wf_meeting_workflow",
                        "trigger_type": "google_calendar",
                        "trigger_name": "Meeting Notifications",
                        "trigger_config": {
                            "calendar_id": "primary",
                            "use_polling": False,
                            "webhook_ttl": 604800,
                            "event_filters": {"title_contains": ["meeting", "call"]},
                        },
                        "event_types": ["created", "updated"],
                        "is_active": True,
                        "created_at": "2025-01-01T10:30:00Z",
                        "updated_at": "2025-01-01T10:30:00Z",
                        "last_triggered_at": "2025-01-01T15:45:00Z",
                    }
                }
            },
        },
        401: {
            "description": "Authentication required",
            "content": {
                "application/json": {"example": {"detail": "Authentication required"}}
            },
        },
        403: {
            "description": "Access denied - trigger belongs to another user",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Access denied: You can only access your own triggers"
                    }
                }
            },
        },
        404: {
            "description": "Trigger not found",
            "content": {
                "application/json": {"example": {"detail": "Trigger not found"}}
            },
        },
        500: {
            "description": "Internal server error",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Internal server error while retrieving trigger"
                    }
                }
            },
        },
    },
)
async def get_trigger(
    trigger_id: UUID,
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerResponse:
    """
    Get a specific trigger by ID.

    Args:
        trigger_id: Trigger ID to retrieve
        current_user: Authenticated user information from bearer token
        trigger_manager: Trigger manager instance

    Returns:
        TriggerResponse: Trigger details

    Raises:
        HTTPException: If trigger not found or access denied
    """

    try:
        # Extract authenticated user ID
        authenticated_user_id = current_user.get("id")
        if not authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User ID not available from authentication",
            )

        # Get trigger by ID directly
        trigger = await trigger_manager.get_trigger_by_id(str(trigger_id))

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Trigger not found"
            )

        # Check if the trigger belongs to the authenticated user
        if trigger.user_id != authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only access your own triggers",
            )

        return TriggerResponse.from_orm(trigger)

    except HTTPException:
        raise
    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving trigger",
        )


@router.put(
    "/{trigger_id}",
    response_model=TriggerResponse,
    summary="Update trigger",
    description="""
    Update an existing trigger's configuration and settings.

    **Authentication Required**: This endpoint requires authentication using either:
    - Bearer token in Authorization header: `Authorization: Bearer <your-token>`
    - API key in X-API-Key header: `X-API-Key: <your-api-key>`

    ## Path Parameters
    - `trigger_id` (UUID, required): The unique identifier of the trigger to update

    ## Request Body
    The request body should contain the updated trigger data following the TriggerUpdate schema.

    ## Security
    Users can only update triggers that belong to them. Attempting to update another user's trigger will result in a 403 Forbidden error.

    ## Current Status
    **Note**: This endpoint is currently not implemented and will return a 501 Not Implemented status.
    """,
    responses={
        200: {
            "description": "Trigger updated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "id": "550e8400-e29b-41d4-a716-************",
                        "user_id": "user_123",
                        "workflow_id": "wf_meeting_workflow",
                        "trigger_type": "google_calendar",
                        "trigger_name": "Updated Meeting Notifications",
                        "trigger_config": {
                            "calendar_id": "primary",
                            "use_polling": False,
                        },
                        "event_types": ["created", "updated", "deleted"],
                        "is_active": True,
                        "created_at": "2025-01-01T10:30:00Z",
                        "updated_at": "2025-01-01T16:45:00Z",
                        "last_triggered_at": "2025-01-01T15:45:00Z",
                    }
                }
            },
        },
        401: {
            "description": "Authentication required",
            "content": {
                "application/json": {"example": {"detail": "Authentication required"}}
            },
        },
        403: {
            "description": "Access denied - trigger belongs to another user",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Access denied: You can only update your own triggers"
                    }
                }
            },
        },
        404: {
            "description": "Trigger not found",
            "content": {
                "application/json": {"example": {"detail": "Trigger not found"}}
            },
        },
        501: {
            "description": "Not implemented",
            "content": {
                "application/json": {
                    "example": {"detail": "Trigger update not yet implemented"}
                }
            },
        },
        500: {
            "description": "Internal server error",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Internal server error while updating trigger"
                    }
                }
            },
        },
    },
)
async def update_trigger(
    trigger_id: UUID,
    trigger_data: TriggerUpdate,
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerResponse:
    """
    Update a trigger.

    Args:
        trigger_id: Trigger ID to update
        trigger_data: Updated trigger data
        current_user: Authenticated user information from bearer token
        trigger_manager: Trigger manager instance

    Returns:
        TriggerResponse: Updated trigger details

    Raises:
        HTTPException: If trigger not found or update fails
    """

    try:
        # Extract authenticated user ID
        authenticated_user_id = current_user.get("id")
        if not authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User ID not available from authentication",
            )

        # Get trigger by ID directly
        trigger = await trigger_manager.get_trigger_by_id(str(trigger_id))

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Trigger not found"
            )

        # Check if the trigger belongs to the authenticated user
        if trigger.user_id != authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only update your own triggers",
            )

        # For now, we'll implement a simple update by recreating the trigger
        # In a full implementation, you'd have an update method in TriggerManager

        # This is a placeholder - implement actual update logic
        raise HTTPException(
            status_code=status.HTTP_501_NOT_IMPLEMENTED,
            detail="Trigger update not yet implemented",
        )

    except HTTPException:
        raise
    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while updating trigger",
        )


@router.delete(
    "/{trigger_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete trigger",
    description="""
    Permanently delete a trigger and stop all associated monitoring.

    **Authentication Required**: This endpoint requires authentication using either:
    - Bearer token in Authorization header: `Authorization: Bearer <your-token>`
    - API key in X-API-Key header: `X-API-Key: <your-api-key>`

    ## Path Parameters
    - `trigger_id` (UUID, required): The unique identifier of the trigger to delete

    ## Security
    Users can only delete triggers that belong to them. Attempting to delete another user's trigger will result in a 403 Forbidden error.

    ## Important Notes
    - This action is **irreversible**
    - All webhook subscriptions and monitoring will be stopped
    - Execution history will be preserved for audit purposes
    - Returns 204 No Content on successful deletion

    ## Example Request
    ```
    DELETE /api/v1/triggers/550e8400-e29b-41d4-a716-************
    ```
    """,
    responses={
        204: {"description": "Trigger deleted successfully (no content returned)"},
        401: {
            "description": "Authentication required",
            "content": {
                "application/json": {"example": {"detail": "Authentication required"}}
            },
        },
        403: {
            "description": "Access denied - trigger belongs to another user",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Access denied: You can only delete your own triggers"
                    }
                }
            },
        },
        404: {
            "description": "Trigger not found",
            "content": {
                "application/json": {"example": {"detail": "Trigger not found"}}
            },
        },
        500: {
            "description": "Internal server error",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Internal server error while deleting trigger"
                    }
                }
            },
        },
    },
)
async def delete_trigger(
    trigger_id: UUID,
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> None:
    """
    Delete a trigger.

    Args:
        trigger_id: Trigger ID to delete
        current_user: Authenticated user information from bearer token
        trigger_manager: Trigger manager instance

    Raises:
        HTTPException: If trigger not found or deletion fails
    """

    try:
        # Extract authenticated user ID
        authenticated_user_id = current_user.get("id")
        if not authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User ID not available from authentication",
            )

        # Get trigger by ID directly
        trigger = await trigger_manager.get_trigger_by_id(str(trigger_id))

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Trigger not found"
            )

        # Check if the trigger belongs to the authenticated user
        if trigger.user_id != authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only delete your own triggers",
            )

        # Delete the trigger
        success = await trigger_manager.remove_trigger(trigger_id)
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete trigger",
            )

    except HTTPException:
        raise
    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while deleting trigger",
        )


@router.post(
    "/{trigger_id}/toggle",
    response_model=TriggerResponse,
    summary="Toggle trigger active status",
    description="""
    Enable or disable a trigger to start or stop monitoring.

    **Authentication Required**: This endpoint requires authentication using either:
    - Bearer token in Authorization header: `Authorization: Bearer <your-token>`
    - API key in X-API-Key header: `X-API-Key: <your-api-key>`

    ## Path Parameters
    - `trigger_id` (UUID, required): The unique identifier of the trigger to toggle

    ## Request Body
    - `is_active` (boolean, required): Set to `true` to enable the trigger, `false` to disable it

    ## Security
    Users can only toggle triggers that belong to them. Attempting to toggle another user's trigger will result in a 403 Forbidden error.

    ## Behavior
    - **Enable (is_active: true)**: Starts monitoring and webhook subscriptions
    - **Disable (is_active: false)**: Stops monitoring and cancels webhook subscriptions

    ## Example Request
    ```json
    {
      "is_active": true
    }
    ```
    """,
    responses={
        200: {
            "description": "Trigger status toggled successfully",
            "content": {
                "application/json": {
                    "example": {
                        "id": "550e8400-e29b-41d4-a716-************",
                        "user_id": "user_123",
                        "workflow_id": "wf_meeting_workflow",
                        "trigger_type": "google_calendar",
                        "trigger_name": "Meeting Notifications",
                        "trigger_config": {
                            "calendar_id": "primary",
                            "use_polling": False,
                        },
                        "event_types": ["created", "updated"],
                        "is_active": True,
                        "created_at": "2025-01-01T10:30:00Z",
                        "updated_at": "2025-01-01T16:45:00Z",
                        "last_triggered_at": "2025-01-01T15:45:00Z",
                    }
                }
            },
        },
        401: {
            "description": "Authentication required",
            "content": {
                "application/json": {"example": {"detail": "Authentication required"}}
            },
        },
        403: {
            "description": "Access denied - trigger belongs to another user",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Access denied: You can only toggle your own triggers"
                    }
                }
            },
        },
        404: {
            "description": "Trigger not found",
            "content": {
                "application/json": {"example": {"detail": "Trigger not found"}}
            },
        },
        500: {
            "description": "Internal server error",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Internal server error while toggling trigger"
                    }
                }
            },
        },
    },
)
async def toggle_trigger(
    trigger_id: UUID,
    toggle_data: TriggerToggleRequest,
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> TriggerResponse:
    """
    Enable or disable a trigger.

    Args:
        trigger_id: Trigger ID to toggle
        toggle_data: Toggle request data
        current_user: Authenticated user information from bearer token
        trigger_manager: Trigger manager instance

    Returns:
        TriggerResponse: Updated trigger details

    Raises:
        HTTPException: If trigger not found or toggle fails
    """

    try:
        # Extract authenticated user ID
        authenticated_user_id = current_user.get("id")
        if not authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User ID not available from authentication",
            )

        # Get trigger by ID directly
        trigger = await trigger_manager.get_trigger_by_id(str(trigger_id))

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Trigger not found"
            )

        # Check if the trigger belongs to the authenticated user
        if trigger.user_id != authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only toggle your own triggers",
            )

        # Toggle the trigger
        success = await trigger_manager.toggle_trigger(
            trigger_id, toggle_data.is_active
        )
        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to toggle trigger",
            )

        # Get updated trigger details
        updated_trigger = await trigger_manager.get_trigger_by_id(str(trigger_id))

        if not updated_trigger:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Trigger toggled but could not retrieve updated details",
            )

        action = "enabled" if toggle_data.is_active else "disabled"

        return TriggerResponse.from_orm(updated_trigger)

    except HTTPException:
        raise
    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while toggling trigger",
        )


@router.get(
    "/{trigger_id}/executions",
    response_model=List[TriggerExecutionResponse],
    summary="Get trigger execution history",
    description="""
    Retrieve the execution history for a specific trigger, showing when it was triggered and the results.

    **Authentication Required**: This endpoint requires authentication using either:
    - Bearer token in Authorization header: `Authorization: Bearer <your-token>`
    - API key in X-API-Key header: `X-API-Key: <your-api-key>`

    ## Path Parameters
    - `trigger_id` (UUID, required): The unique identifier of the trigger

    ## Query Parameters
    - `limit` (integer, optional): Maximum number of executions to return (default: 100, minimum: 1, maximum: 1000)

    ## Security
    Users can only view execution history for triggers that belong to them. Attempting to access another user's trigger executions will result in a 403 Forbidden error.

    ## Response
    Returns a list of trigger executions ordered by most recent first, including:
    - Execution timestamp
    - Success/failure status
    - Event data that triggered the execution
    - Error details (if execution failed)
    - Workflow execution results

    ## Example Request
    ```
    GET /api/v1/triggers/550e8400-e29b-41d4-a716-************/executions?limit=50
    ```
    """,
    responses={
        200: {
            "description": "Trigger execution history retrieved successfully",
            "content": {
                "application/json": {
                    "example": [
                        {
                            "id": "exec_123456",
                            "trigger_id": "550e8400-e29b-41d4-a716-************",
                            "executed_at": "2025-01-01T15:45:00Z",
                            "status": "success",
                            "event_data": {
                                "event_type": "created",
                                "calendar_event": {
                                    "summary": "Team Meeting",
                                    "start": "2025-01-02T10:00:00Z",
                                },
                            },
                            "workflow_execution_id": "wf_exec_789",
                            "error_message": None,
                        }
                    ]
                }
            },
        },
        401: {
            "description": "Authentication required",
            "content": {
                "application/json": {"example": {"detail": "Authentication required"}}
            },
        },
        403: {
            "description": "Access denied - trigger belongs to another user",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Access denied: You can only view executions for your own triggers"
                    }
                }
            },
        },
        404: {
            "description": "Trigger not found",
            "content": {
                "application/json": {"example": {"detail": "Trigger not found"}}
            },
        },
        500: {
            "description": "Internal server error",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Internal server error while retrieving trigger executions"
                    }
                }
            },
        },
    },
)
async def get_trigger_executions(
    trigger_id: UUID,
    current_user: Dict[str, Any] = Depends(get_authenticated_user),
    limit: int = Query(
        100, ge=1, le=1000, description="Maximum number of executions to return"
    ),
    trigger_manager: TriggerManager = Depends(get_trigger_manager),
) -> List[TriggerExecutionResponse]:
    """
    Get execution history for a trigger.

    Args:
        trigger_id: Trigger ID to get executions for
        current_user: Authenticated user information from bearer token
        limit: Maximum number of executions to return
        trigger_manager: Trigger manager instance

    Returns:
        List[TriggerExecutionResponse]: List of trigger executions

    Raises:
        HTTPException: If trigger not found or access denied
    """

    try:
        # Extract authenticated user ID
        authenticated_user_id = current_user.get("id")
        if not authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="User ID not available from authentication",
            )

        # Get trigger by ID directly
        trigger = await trigger_manager.get_trigger_by_id(str(trigger_id))

        if not trigger:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Trigger not found"
            )

        # Check if the trigger belongs to the authenticated user
        if trigger.user_id != authenticated_user_id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Access denied: You can only view executions for your own triggers",
            )

        # Get execution history
        executions = await trigger_manager.get_execution_history(trigger_id, limit)

        return [
            TriggerExecutionResponse.from_orm(execution) for execution in executions
        ]

    except HTTPException:
        raise
    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while retrieving trigger executions",
        )


# @router.get("/stats", response_model=TriggerStatsResponse)
# async def get_trigger_stats(
#     request: Request, trigger_manager: TriggerManager = Depends(get_trigger_manager)
# ) -> TriggerStatsResponse:
#     """
#     Get trigger statistics for the current user.

#     Args:
#         request: HTTP request for authentication
#         trigger_manager: Trigger manager instance

#     Returns:
#         TriggerStatsResponse: Trigger statistics
#     """
#     try:
#         # Get user's triggers
#         triggers = await trigger_manager.get_triggers_for_user(current_user)

#         # Calculate statistics
#         total_triggers = len(triggers)
#         active_triggers = len([t for t in triggers if t.is_active])
#         inactive_triggers = total_triggers - active_triggers

#         # Count by type
#         triggers_by_type = {}
#         for trigger in triggers:
#             trigger_type = trigger.trigger_type
#             triggers_by_type[trigger_type] = triggers_by_type.get(trigger_type, 0) + 1

#         # For now, return placeholder values for execution stats
#         # In a full implementation, you'd query the execution history
#         recent_executions = 0
#         success_rate = 100.0

#         return TriggerStatsResponse(
#             total_triggers=total_triggers,
#             active_triggers=active_triggers,
#             inactive_triggers=inactive_triggers,
#             triggers_by_type=triggers_by_type,
#             recent_executions=recent_executions,
#             success_rate=success_rate,
#         )

#     except HTTPException:
#         raise
#     except Exception as e:
#         logger.error(
#             f"Failed to get trigger stats for user {current_user}", error=str(e)
#         )
#         raise HTTPException(
#             status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
#             detail="Internal server error while retrieving trigger statistics",
#         )
