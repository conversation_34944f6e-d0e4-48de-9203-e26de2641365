"""
Health check endpoints for the Trigger Service.

This module provides comprehensive health check endpoints with
standardized response formats and detailed monitoring capabilities.
"""

import time
from datetime import datetime, timezone
from typing import Dict, Any

from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text

from src.api.base import BaseAPIRouter, SuccessResponse
from src.api.models import HealthCheckResponse
from src.database.connection import get_async_session, get_db_manager
from src.core.dependencies import get_adapter_factory_dependency
from src.adapters.factory import AdapterFactory
from src.utils.config import get_settings, is_feature_enabled
from src.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/health", tags=["health"])

# Application start time for uptime calculation
_start_time = time.time()


class HealthAPIRouter(BaseAPIRouter):
    """Health check API router with standardized patterns."""
    
    def __init__(self):
        super().__init__("health")


# Router instance
health_router = HealthAPIRouter()


@router.get(
    "/",
    response_model=SuccessResponse[HealthCheckResponse],
    summary="Comprehensive health check",
    description="Get comprehensive health status of the service and all dependencies"
)
async def health_check(
    session: AsyncSession = Depends(get_async_session),
    adapter_factory: AdapterFactory = Depends(get_adapter_factory_dependency),
) -> SuccessResponse[HealthCheckResponse]:
    """
    Comprehensive health check endpoint.
    
    This endpoint checks the health of all service components including:
    - Database connectivity
    - Adapter health
    - External service connectivity
    - Configuration validity
    
    Returns:
        SuccessResponse[HealthCheckResponse]: Comprehensive health status
    """
    try:
        current_time = datetime.now(timezone.utc)
        uptime = time.time() - _start_time
        
        # Initialize health check results
        checks = {}
        overall_healthy = True
        
        # Check database connectivity
        try:
            await session.execute(text("SELECT 1"))
            checks["database"] = {
                "status": "healthy",
                "message": "Database connection successful",
                "response_time_ms": None,
                "details": {
                    "connection_pool_size": "unknown",
                    "active_connections": "unknown"
                }
            }
        except Exception as e:
            overall_healthy = False
            checks["database"] = {
                "status": "unhealthy",
                "message": f"Database connection failed: {str(e)}",
                "response_time_ms": None,
                "details": {"error": str(e)}
            }
        
        # Check adapter health
        try:
            adapter_names = adapter_factory.list_adapters()
            adapter_checks = {}
            
            for adapter_name in adapter_names:
                try:
                    adapter = adapter_factory.get_adapter(adapter_name)
                    if adapter and hasattr(adapter, '_perform_health_check'):
                        start_time = time.time()
                        is_healthy = await adapter._perform_health_check()
                        response_time = (time.time() - start_time) * 1000
                        
                        adapter_checks[adapter_name] = {
                            "status": "healthy" if is_healthy else "unhealthy",
                            "message": "Adapter health check passed" if is_healthy else "Adapter health check failed",
                            "response_time_ms": round(response_time, 2),
                            "details": {
                                "adapter_type": adapter_name,
                                "instance_created": adapter is not None
                            }
                        }
                        
                        if not is_healthy:
                            overall_healthy = False
                    else:
                        adapter_checks[adapter_name] = {
                            "status": "unknown",
                            "message": "Health check not implemented",
                            "response_time_ms": None,
                            "details": {"adapter_type": adapter_name}
                        }
                        
                except Exception as e:
                    overall_healthy = False
                    adapter_checks[adapter_name] = {
                        "status": "unhealthy",
                        "message": f"Adapter health check failed: {str(e)}",
                        "response_time_ms": None,
                        "details": {"error": str(e)}
                    }
            
            checks["adapters"] = {
                "status": "healthy" if all(
                    check["status"] == "healthy" 
                    for check in adapter_checks.values()
                ) else "unhealthy",
                "message": f"Checked {len(adapter_checks)} adapters",
                "response_time_ms": None,
                "details": adapter_checks
            }
            
        except Exception as e:
            overall_healthy = False
            checks["adapters"] = {
                "status": "unhealthy",
                "message": f"Adapter health check failed: {str(e)}",
                "response_time_ms": None,
                "details": {"error": str(e)}
            }
        
        # Check configuration
        try:
            settings = get_settings()
            checks["configuration"] = {
                "status": "healthy",
                "message": "Configuration loaded successfully",
                "response_time_ms": None,
                "details": {
                    "debug_mode": settings.debug,
                    "environment": "development" if settings.debug else "production",
                    "features": {
                        "redis_enabled": is_feature_enabled("redis_enabled"),
                        "metrics_enabled": is_feature_enabled("metrics_enabled"),
                        "health_checks_enabled": is_feature_enabled("health_checks_enabled"),
                    }
                }
            }
        except Exception as e:
            overall_healthy = False
            checks["configuration"] = {
                "status": "unhealthy",
                "message": f"Configuration check failed: {str(e)}",
                "response_time_ms": None,
                "details": {"error": str(e)}
            }
        
        # Create health check response
        health_response = HealthCheckResponse(
            status="healthy" if overall_healthy else "unhealthy",
            timestamp=current_time,
            version="1.0.0",  # This should come from configuration
            uptime=uptime,
            checks=checks
        )
        
        return SuccessResponse.create(
            data=health_response,
            message="Health check completed"
        )
        
    except Exception as e:
        health_router.handle_service_error(e)


@router.get(
    "/basic",
    summary="Basic health check",
    description="Simple health check for load balancers and monitoring systems"
)
async def basic_health_check() -> Dict[str, Any]:
    """
    Basic health check endpoint for load balancers.
    
    This endpoint provides a simple health status without dependencies
    and is suitable for load balancer health checks.
    
    Returns:
        Dict[str, Any]: Basic health status
    """
    return {
        "status": "healthy",
        "timestamp": datetime.now(timezone.utc).isoformat(),
        "service": "trigger-service",
        "version": "1.0.0",
        "uptime": time.time() - _start_time
    }


@router.get(
    "/database",
    summary="Database health check",
    description="Check database connectivity and status"
)
async def database_health_check(
    session: AsyncSession = Depends(get_async_session),
) -> SuccessResponse[Dict[str, Any]]:
    """
    Database-specific health check.
    
    Args:
        session: Database session
        
    Returns:
        SuccessResponse: Database health status
    """
    try:
        start_time = time.time()
        
        # Test basic connectivity
        await session.execute(text("SELECT 1"))
        
        # Test a more complex query
        result = await session.execute(text("SELECT COUNT(*) FROM triggers"))
        trigger_count = result.scalar()
        
        response_time = (time.time() - start_time) * 1000
        
        health_data = {
            "status": "healthy",
            "response_time_ms": round(response_time, 2),
            "trigger_count": trigger_count,
            "connection_info": {
                "pool_size": "unknown",  # Could be enhanced with actual pool info
                "active_connections": "unknown"
            }
        }
        
        return SuccessResponse.create(
            data=health_data,
            message="Database health check passed"
        )
        
    except Exception as e:
        health_router.handle_service_error(e)
