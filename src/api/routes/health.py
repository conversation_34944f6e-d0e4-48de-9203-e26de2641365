"""
Health check API routes.

This module contains health check endpoints for monitoring service status
and dependencies.
"""

from fastapi import APIRouter, Depends, HTTPException, status, Request
from typing import Dict, Any, List
import datetime

from src.core.trigger_manager import TriggerManager
from src.database.connection import db_manager
from src.schemas.trigger import AdapterHealthResponse

from src.utils.config import get_settings


router = APIRouter(prefix="/api/v1/health", tags=["health"])


# Dependency to get trigger manager instance
def get_trigger_manager() -> TriggerManager:
    """Get trigger manager instance."""
    if not hasattr(get_trigger_manager, "_instance"):
        from src.adapters import GoogleCalendarAdapter

        get_trigger_manager._instance = TriggerManager()

        # Register available adapters
        google_calendar_adapter = GoogleCalendarAdapter()
        get_trigger_manager._instance.register_adapter(google_calendar_adapter)

    return get_trigger_manager._instance


@router.get(
    "/",
    summary="Basic health check",
    description="""
    Basic health check endpoint for monitoring service availability.

    **Public Endpoint**: This endpoint does not require authentication and is designed for:
    - Load balancer health checks
    - Service monitoring systems
    - Basic availability verification

    ## Response
    Returns basic service information including:
    - Service status (healthy/unhealthy)
    - Current timestamp
    - Service name and version
    - No external dependencies checked

    ## Use Cases
    - Load balancer health probes
    - Container orchestration health checks
    - Basic service availability monitoring
    """,
    responses={
        200: {
            "description": "Service is healthy and responding",
            "content": {
                "application/json": {
                    "example": {
                        "status": "healthy",
                        "timestamp": "2025-01-01T10:30:00.123456",
                        "service": "trigger-service",
                        "version": "1.0.0",
                    }
                }
            },
        }
    },
)
async def basic_health_check() -> Dict[str, Any]:
    """
    Basic health check endpoint.

    This endpoint provides a simple health status without authentication
    and is suitable for load balancer health checks.

    Returns:
        Dict[str, Any]: Basic health status
    """
    return {
        "status": "healthy",
        "timestamp": datetime.datetime.now().isoformat(),
        "service": "trigger-service",
        "version": "1.0.0",
    }


@router.get(
    "/adapters",
    response_model=List[AdapterHealthResponse],
    summary="Check adapter health status",
    description="""
    Retrieve detailed health status for all registered trigger adapters.

    **Public Endpoint**: This endpoint does not require authentication and provides health information for:
    - Google Calendar adapter
    - Google Drive adapter
    - Other registered trigger adapters

    ## Response Information
    For each adapter, returns:
    - **adapter_name**: Name of the adapter (e.g., "google_calendar")
    - **is_healthy**: Boolean indicating if the adapter is functioning properly
    - **last_check**: Timestamp of the last health check
    - **error_message**: Details of any errors (null if healthy)
    - **active_triggers**: Number of currently active triggers using this adapter
    - **external_service_status**: Status of the external service connection

    ## Use Cases
    - Monitoring adapter connectivity to external services
    - Debugging trigger issues
    - System health dashboards
    - Operational monitoring

    ## Health Check Process
    Each adapter performs checks on:
    - External service connectivity (Google APIs, etc.)
    - Authentication status
    - Rate limiting status
    - Configuration validity
    """,
    responses={
        200: {
            "description": "Adapter health status retrieved successfully",
            "content": {
                "application/json": {
                    "example": [
                        {
                            "adapter_name": "google_calendar",
                            "is_healthy": True,
                            "last_check": "2025-01-01T10:30:00.123456Z",
                            "error_message": None,
                            "active_triggers": 5,
                            "external_service_status": "connected",
                        },
                        {
                            "adapter_name": "google_drive_service_account",
                            "is_healthy": False,
                            "last_check": "2025-01-01T10:29:45.678901Z",
                            "error_message": "Authentication failed: Invalid service account credentials",
                            "active_triggers": 2,
                            "external_service_status": "authentication_error",
                        },
                    ]
                }
            },
        },
        500: {
            "description": "Internal server error during health check",
            "content": {
                "application/json": {
                    "example": {"detail": "Failed to retrieve adapter health status"}
                }
            },
        },
    },
)
async def adapter_health_check(
    request: Request, trigger_manager: TriggerManager = Depends(get_trigger_manager)
) -> List[AdapterHealthResponse]:
    """
    Get health status for all registered adapters.

    Args:
        request: HTTP request for authentication
        trigger_manager: Trigger manager instance

    Returns:
        List[AdapterHealthResponse]: List of adapter health statuses

    Raises:
        HTTPException: If authentication fails or health check fails
    """

    try:
        adapter_health = await trigger_manager.health_check()

        health_responses = []
        for adapter_name, health_status in adapter_health.items():
            # Get adapter statistics
            adapter = trigger_manager.get_adapter(adapter_name)
            active_triggers = adapter.get_trigger_count() if adapter else 0

            health_response = AdapterHealthResponse(
                adapter_name=adapter_name,
                is_healthy=health_status.is_healthy,
                last_check=health_status.last_check,
                error_message=health_status.error_message,
                active_triggers=active_triggers,
                external_service_status=health_status.external_service_status,
            )
            health_responses.append(health_response)

        return health_responses

    except Exception as e:

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve adapter health status",
        )
