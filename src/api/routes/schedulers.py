from typing import List
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from src.core.scheduler_manager import SchedulerManager
from src.database.connection import get_async_session
from src.schemas.scheduler import (
    SimplifiedSchedulerCreate,
    SimplifiedSchedulerUpdate,
    SimplifiedSchedulerResponse,
)
from src.api.middleware.auth import get_authenticated_user
from src.schemas.workflow import WorkflowResponse
from src.utils.logger import get_logger

logger = get_logger(__name__)

router = APIRouter(prefix="/api/v1/schedulers", tags=["schedulers"])


@router.post(
    "/schedulers",
    response_model=SimplifiedSchedulerResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Create a new scheduler",
    description="""
    Create a new scheduler to execute workflows on a recurring basis.

    **Authentication Required**: This endpoint requires authentication using either:
    - Bearer token in Authorization header: `Authorization: Bearer <your-token>`
    - API key in X-API-Key header: `X-API-Key: <your-api-key>`

    ## Supported Frequency Types

    ### Simple Frequencies
    - **`every_minute`**: Execute every minute (no additional fields required)
    - **`hourly`**: Execute every hour (no additional fields required)

    ### Time-based Frequencies
    - **`daily`**: Execute daily at specified time
      - **Required**: `time` (HH:mm format, e.g., "09:00")
    - **`weekly`**: Execute weekly on specified days and time
      - **Required**: `time` (HH:mm format) and `days_of_week` (array of weekday names)
    - **`monthly`**: Execute monthly on specified days and time
      - **Required**: `time` (HH:mm format) and `days_of_month` (array of 1-31)

    ### Advanced Frequency
    - **`custom`**: Execute using custom cron expression
      - **Required**: `cron_expression` (5-part cron format)

    ## Required Fields
    - `name` (string): Human-readable name for the scheduler
    - `workflow_id` (string): ID of the workflow to execute
    - `frequency` (string): One of the supported frequency types
    - `timezone` (string): Timezone for scheduling (e.g., "UTC", "America/New_York")
    - `is_active` (boolean): Whether the scheduler is active

    ## Optional Fields
    - `scheduler_metadata` (object): Additional metadata for the scheduler
    """,
    responses={
        201: {
            "description": "Scheduler created successfully",
            "content": {
                "application/json": {
                    "example": {
                        "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
                        "user_id": "user_123",
                        "name": "Daily Report Scheduler",
                        "workflow_id": "wkf_34567",
                        "frequency": "daily",
                        "time": "09:00",
                        "days_of_week": None,
                        "days_of_month": None,
                        "cron_expression": None,
                        "timezone": "America/New_York",
                        "is_active": True,
                        "scheduler_metadata": {
                            "department": "finance",
                            "report_type": "daily_summary",
                        },
                        "created_at": "2025-01-17T10:00:00.000Z",
                        "updated_at": "2025-01-17T10:00:00.000Z",
                        "last_run_at": None,
                        "next_run_at": "2025-01-18T09:00:00.000Z",
                    }
                }
            },
        },
        400: {
            "description": "Invalid request data",
            "content": {
                "application/json": {
                    "example": {
                        "detail": "Failed to create scheduler: Invalid frequency type"
                    }
                }
            },
        },
        401: {
            "description": "Authentication required",
            "content": {
                "application/json": {
                    "example": {"detail": "User not authenticated or user ID missing."}
                }
            },
        },
    },
)
async def create_scheduler(
    scheduler_create: SimplifiedSchedulerCreate,
    db: AsyncSession = Depends(get_async_session),
    user: dict = Depends(get_authenticated_user),
):
    """
    Create a new scheduler using simplified schema.

    **Frequency Types:**
    - `every_minute`: Execute every minute
    - `hourly`: Execute every hour
    - `daily`: Execute daily at specified time (requires `time`)
    - `weekly`: Execute weekly on specified days and time (requires `time` and `days_of_week`)
    - `monthly`: Execute monthly on specified days and time (requires `time` and `days_of_month`)
    - `custom`: Execute using custom cron expression (requires `cron_expression`)

    **Field Requirements by Frequency:**
    - `every_minute`, `hourly`: Only `name`, `workflow_id`, `frequency`, `timezone`, `is_active`
    - `daily`: Requires `time` (HH:mm format)
    - `weekly`: Requires `time` and `days_of_week` (array of weekday names)
    - `monthly`: Requires `time` and `days_of_month` (array of 1-31)
    - `custom`: Requires `cron_expression` (5-part cron format)
    """
    try:
        # Validate user authentication and extract user ID
        if not user or "id" not in user or not user["id"]:
            logger.error("User authentication failed", user_data=user)
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="User not authenticated or user ID missing.",
            )

        user_id = user["id"]
        logger.info("Creating scheduler", user_id=user_id, name=scheduler_create.name)

        scheduler_manager = SchedulerManager(db)
        scheduler = await scheduler_manager.create(scheduler_create, user_id)
        return scheduler
    except HTTPException:
        raise  # Re-raise HTTPException directly
    except Exception as e:
        logger.error(f"Error creating scheduler: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create scheduler: {e}",
        )


@router.get(
    "/schedulers",
    response_model=List[SimplifiedSchedulerResponse],
    summary="List all schedulers",
    description="""
    Retrieve a list of all schedulers for the authenticated user.

    **Authentication Required**: This endpoint requires authentication using either:
    - Bearer token in Authorization header: `Authorization: Bearer <your-token>`
    - API key in X-API-Key header: `X-API-Key: <your-api-key>`

    ## Response
    Returns an array of scheduler objects, each containing:
    - **Basic Information**: ID, name, user ID, workflow ID
    - **Schedule Configuration**: Frequency type and related timing fields
    - **Status**: Active/inactive status and execution timestamps
    - **Metadata**: Optional custom metadata

    ## Field Behavior by Frequency Type
    - **every_minute/hourly**: Only basic fields populated, timing fields are null
    - **daily**: `time` field populated, other timing fields are null
    - **weekly**: `time` and `days_of_week` populated, others null
    - **monthly**: `time` and `days_of_month` populated, others null
    - **custom**: `cron_expression` populated, other timing fields are null

    ## Execution Information
    - `last_run_at`: Timestamp of the last execution (null if never executed)
    - `next_run_at`: Timestamp of the next scheduled execution
    - `is_active`: Whether the scheduler is currently active
    """,
    responses={
        200: {
            "description": "List of schedulers retrieved successfully",
            "content": {
                "application/json": {
                    "example": [
                        {
                            "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
                            "user_id": "user_123",
                            "name": "Daily Report Scheduler",
                            "workflow_id": "wkf_23456",
                            "frequency": "daily",
                            "time": "09:00",
                            "days_of_week": None,
                            "days_of_month": None,
                            "cron_expression": None,
                            "timezone": "America/New_York",
                            "is_active": True,
                            "scheduler_metadata": {
                                "department": "finance",
                                "report_type": "daily_summary",
                            },
                            "created_at": "2025-01-17T08:00:00.000Z",
                            "updated_at": "2025-01-17T08:00:00.000Z",
                            "last_run_at": "2025-01-17T09:00:00.000Z",
                            "next_run_at": "2025-01-18T09:00:00.000Z",
                        }
                    ]
                }
            },
        },
        401: {
            "description": "Authentication required",
            "content": {
                "application/json": {"example": {"detail": "User not authenticated"}}
            },
        },
    },
)
async def list_schedulers(
    db: AsyncSession = Depends(get_async_session),
    user: dict = Depends(get_authenticated_user),
):
    """
    List all schedulers for the authenticated user using simplified schema.
    """
    logger.info("Listing schedulers", user_id=user["id"])
    scheduler_manager = SchedulerManager(db)
    user_schedulers = await scheduler_manager.list(user["id"])
    return user_schedulers


@router.get(
    "/schedulers/{scheduler_id}",
    response_model=SimplifiedSchedulerResponse,
    summary="Get scheduler by ID",
    description="""
    Retrieve detailed information about a specific scheduler by its ID.

    **Authentication Required**: This endpoint requires authentication using either:
    - Bearer token in Authorization header: `Authorization: Bearer <your-token>`
    - API key in X-API-Key header: `X-API-Key: <your-api-key>`

    ## Path Parameters
    - `scheduler_id` (string, required): The unique identifier of the scheduler

    ## Security
    Users can only access schedulers that belong to them. Attempting to access another user's scheduler will result in a 404 Not Found error.

    ## Response Fields
    Only fields relevant to the frequency type will have values:
    - **every_minute/hourly**: Basic fields only, timing fields are null
    - **daily**: `time` populated, other timing fields null
    - **weekly**: `time` and `days_of_week` populated
    - **monthly**: `time` and `days_of_month` populated
    - **custom**: `cron_expression` populated

    ## Example Request
    ```
    GET /api/v1/schedulers/schedulers/c3d4e5f6-g7h8-9012-3456-789012cdefgh
    ```
    """,
    responses={
        200: {
            "description": "Scheduler details retrieved successfully",
            "content": {
                "application/json": {
                    "example": {
                        "id": "c3d4e5f6-g7h8-9012-3456-789012cdefgh",
                        "user_id": "user_123",
                        "name": "Weekly Report Scheduler",
                        "workflow_id": "wkf_34567",
                        "frequency": "weekly",
                        "time": "10:00",
                        "days_of_week": ["Monday", "Friday"],
                        "days_of_month": None,
                        "cron_expression": None,
                        "timezone": "UTC",
                        "is_active": True,
                        "scheduler_metadata": {
                            "report_type": "weekly_summary",
                            "recipients": ["<EMAIL>"],
                        },
                        "created_at": "2025-01-17T07:00:00.000Z",
                        "updated_at": "2025-01-17T07:00:00.000Z",
                        "last_run_at": "2025-01-17T10:00:00.000Z",
                        "next_run_at": "2025-01-20T10:00:00.000Z",
                    }
                }
            },
        },
        401: {
            "description": "Authentication required",
            "content": {
                "application/json": {"example": {"detail": "User not authenticated"}}
            },
        },
        404: {
            "description": "Scheduler not found",
            "content": {
                "application/json": {"example": {"detail": "Scheduler not found"}}
            },
        },
    },
)
async def get_scheduler(
    scheduler_id: str,
    db: AsyncSession = Depends(get_async_session),
    user: dict = Depends(get_authenticated_user),
):
    """
    Get a specific scheduler by ID using simplified schema.

    **Note:** Only fields relevant to the frequency type will have values.
    For example, a daily scheduler will have `time` but `days_of_week`,
    `days_of_month`, and `cron_expression` will be null.
    """
    logger.info("Getting scheduler", user_id=user["id"], scheduler_id=scheduler_id)
    scheduler_manager = SchedulerManager(db)
    scheduler = await scheduler_manager.get(scheduler_id, user["id"])
    if not scheduler:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Scheduler not found"
        )
    return scheduler


@router.put(
    "/schedulers/{scheduler_id}",
    response_model=SimplifiedSchedulerResponse,
    summary="Update scheduler",
    description="""
    Update an existing scheduler's configuration and settings.

    **Authentication Required**: This endpoint requires authentication using either:
    - Bearer token in Authorization header: `Authorization: Bearer <your-token>`
    - API key in X-API-Key header: `X-API-Key: <your-api-key>`

    ## Path Parameters
    - `scheduler_id` (string, required): The unique identifier of the scheduler to update

    ## Request Body
    All fields are optional in update requests. Only provided fields will be updated.

    ## Frequency Change Requirements
    When changing the frequency type, ensure you provide the required fields:
    - **daily**: requires `time` (HH:mm format)
    - **weekly**: requires `time` and `days_of_week` (array of weekday names)
    - **monthly**: requires `time` and `days_of_month` (array of 1-31)
    - **custom**: requires `cron_expression` (5-part cron format)

    ## Security
    Users can only update schedulers that belong to them. Attempting to update another user's scheduler will result in a 404 Not Found error.
    """,
    responses={
        200: {
            "description": "Scheduler updated successfully",
            "content": {
                "application/json": {
                    "example": {
                        "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
                        "user_id": "user_123",
                        "name": "Updated Daily Report Scheduler",
                        "workflow_id": "wkf_12345",
                        "frequency": "daily",
                        "time": "10:00",
                        "days_of_week": None,
                        "days_of_month": None,
                        "cron_expression": None,
                        "timezone": "America/New_York",
                        "is_active": False,
                        "scheduler_metadata": {
                            "department": "finance",
                            "report_type": "daily_summary",
                        },
                        "created_at": "2025-01-17T10:00:00.000Z",
                        "updated_at": "2025-01-17T11:00:00.000Z",
                        "last_run_at": "2025-01-17T09:00:00.000Z",
                        "next_run_at": "2025-01-18T10:00:00.000Z",
                    }
                }
            },
        },
        400: {
            "description": "Invalid request data",
            "content": {
                "application/json": {
                    "example": {"detail": "Invalid frequency configuration"}
                }
            },
        },
        401: {
            "description": "Authentication required",
            "content": {
                "application/json": {"example": {"detail": "User not authenticated"}}
            },
        },
        404: {
            "description": "Scheduler not found",
            "content": {
                "application/json": {"example": {"detail": "Scheduler not found"}}
            },
        },
    },
)
async def update_scheduler(
    scheduler_id: str,
    scheduler_update: SimplifiedSchedulerUpdate,
    db: AsyncSession = Depends(get_async_session),
    user: dict = Depends(get_authenticated_user),
):
    """
    Update an existing scheduler using simplified schema.

    **Note:** All fields are optional in update requests. Only provided fields will be updated.

    **Example Request Body (Update Time and Status):**
    ```json
    {
      "name": "Updated Daily Report Scheduler",
      "is_active": false,
      "time": "10:00",
      "timezone": "America/New_York"
    }
    ```

    **Example Request Body (Change Frequency from Daily to Weekly):**
    ```json
    {
      "frequency": "weekly",
      "time": "09:00",
      "days_of_week": ["Monday", "Wednesday", "Friday"]
    }
    ```

    **Example Request Body (Update Monthly Days):**
    ```json
    {
      "days_of_month": [1, 10, 20],
      "time": "08:30"
    }
    ```

    **Example Response:**
    ```json
    {
      "id": "a1b2c3d4-e5f6-7890-1234-567890abcdef",
      "user_id": "user_123",
      "name": "Updated Daily Report Scheduler",
      "workflow_id": "wkf_12345",
      "frequency": "daily",
      "time": "10:00",
      "days_of_week": null,
      "days_of_month": null,
      "cron_expression": null,
      "timezone": "America/New_York",
      "is_active": false,
      "scheduler_metadata": {
        "department": "finance",
        "report_type": "daily_summary"
      },
      "created_at": "2025-01-17T10:00:00.000Z",
      "updated_at": "2025-01-17T11:00:00.000Z",
      "last_run_at": "2025-01-17T09:00:00.000Z",
      "next_run_at": "2025-01-18T10:00:00.000Z"
    }
    ```

    **Important:** When changing frequency type, ensure you provide the required fields:
    - `daily`: requires `time`
    - `weekly`: requires `time` and `days_of_week`
    - `monthly`: requires `time` and `days_of_month`
    - `custom`: requires `cron_expression`
    """
    logger.info("Updating scheduler", user_id=user["id"], scheduler_id=scheduler_id)
    scheduler_manager = SchedulerManager(db)

    try:
        updated_scheduler = await scheduler_manager.update(
            scheduler_id, scheduler_update, user["id"]
        )
        if not updated_scheduler:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND, detail="Scheduler not found"
            )
        return updated_scheduler
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(e))


@router.delete(
    "/schedulers/{scheduler_id}",
    status_code=status.HTTP_204_NO_CONTENT,
    summary="Delete scheduler",
    description="""
    Permanently delete a scheduler and stop all scheduled executions.

    **Authentication Required**: This endpoint requires authentication using either:
    - Bearer token in Authorization header: `Authorization: Bearer <your-token>`
    - API key in X-API-Key header: `X-API-Key: <your-api-key>`

    ## Path Parameters
    - `scheduler_id` (string, required): The unique identifier of the scheduler to delete

    ## Security
    Users can only delete schedulers that belong to them. Attempting to delete another user's scheduler will result in a 404 Not Found error.

    ## Important Notes
    - This operation is **irreversible**
    - The scheduler will be permanently removed from the system
    - All scheduled executions will be stopped immediately
    - Execution history will be permanently deleted
    - Returns 204 No Content on successful deletion

    ## Example Request
    ```
    DELETE /api/v1/schedulers/schedulers/a1b2c3d4-e5f6-7890-1234-567890abcdef
    ```
    """,
    responses={
        204: {"description": "Scheduler deleted successfully (no content returned)"},
        401: {
            "description": "Authentication required",
            "content": {
                "application/json": {"example": {"detail": "User not authenticated"}}
            },
        },
        404: {
            "description": "Scheduler not found",
            "content": {
                "application/json": {"example": {"detail": "Scheduler not found"}}
            },
        },
    },
)
async def delete_scheduler(
    scheduler_id: str,
    db: AsyncSession = Depends(get_async_session),
    user: dict = Depends(get_authenticated_user),
):
    """
    Delete a scheduler.

    **Note:** This operation is irreversible. The scheduler and all its execution history will be permanently deleted.
    """
    logger.info("Deleting scheduler", user_id=user["id"], scheduler_id=scheduler_id)
    scheduler_manager = SchedulerManager(db)

    success = await scheduler_manager.delete(scheduler_id, user["id"])
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND, detail="Scheduler not found"
        )
    return {"message": "Scheduler deleted successfully"}
