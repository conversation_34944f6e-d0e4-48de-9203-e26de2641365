"""
Global error handler middleware for the Trigger Service.

This module provides centralized error handling and response formatting
for all API endpoints.
"""

import traceback
from typing import Dict, Any, Optional
from fastapi import Request, HTTPException, status
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response
from pydantic import ValidationError

from src.core.exceptions import (
    TriggerServiceError,
    ValidationError as ServiceValidationError,
    AuthenticationError,
    AuthorizationError,
    DatabaseError,
    AdapterError,
    WebhookError,
    WorkflowError,
    TriggerError,
    SchedulerError,
    ExternalServiceError,
    create_error_response,
    get_error_category,
)
from src.utils.logger import get_logger

logger = get_logger(__name__)


class ErrorHandlerMiddleware(BaseHTTPMiddleware):
    """
    Global error handler middleware for consistent error responses.

    This middleware catches all unhandled exceptions and formats them
    into consistent JSON responses with proper logging.
    """

    async def dispatch(self, request: Request, call_next) -> Response:
        """
        Process the request and handle any exceptions.

        Args:
            request: Incoming HTTP request
            call_next: Next middleware or endpoint handler

        Returns:
            Response: HTTP response with error handling
        """
        try:
            response = await call_next(request)
            return response

        except HTTPException as e:
            # FastAPI HTTPExceptions are already properly formatted
            logger.warning(
                "HTTP exception occurred",
                status_code=e.status_code,
                detail=e.detail,
                path=request.url.path,
                method=request.method,
            )

            return JSONResponse(
                status_code=e.status_code,
                content=self._format_error_response(
                    error_type="HTTPException",
                    message=e.detail,
                    status_code=e.status_code,
                ),
            )

        except TriggerServiceError as e:
            # Our custom service exceptions
            status_code = self._get_status_code_for_service_error(e)

            logger.error(
                "Service error occurred",
                error_type=e.__class__.__name__,
                error_code=e.error_code,
                message=e.message,
                category=get_error_category(e),
                path=request.url.path,
                method=request.method,
                details=e.details,
            )

            return JSONResponse(
                status_code=status_code,
                content=create_error_response(e),
            )

        except ValidationError as e:
            # Pydantic validation errors
            logger.warning(
                "Validation error occurred",
                path=request.url.path,
                method=request.method,
                errors=e.errors(),
            )

            return JSONResponse(
                status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
                content=self._format_validation_error_response(e),
            )

        except Exception as e:
            # Unhandled exceptions
            error_id = self._log_unhandled_exception(e, request)

            return JSONResponse(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                content=self._format_error_response(
                    error_type="InternalServerError",
                    message="An internal server error occurred",
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    error_id=error_id,
                ),
            )

    def _get_status_code_for_service_error(self, error: TriggerServiceError) -> int:
        """
        Get appropriate HTTP status code for service error.

        Args:
            error: Service error

        Returns:
            int: HTTP status code
        """
        if isinstance(error, AuthenticationError):
            return status.HTTP_401_UNAUTHORIZED
        elif isinstance(error, AuthorizationError):
            return status.HTTP_403_FORBIDDEN
        elif isinstance(error, (ServiceValidationError, ValidationError)):
            return status.HTTP_422_UNPROCESSABLE_ENTITY
        elif isinstance(error, (TriggerError, WorkflowError, SchedulerError)):
            if "not found" in error.message.lower():
                return status.HTTP_404_NOT_FOUND
            return status.HTTP_400_BAD_REQUEST
        elif isinstance(error, AdapterError):
            if isinstance(error, AuthenticationError):
                return status.HTTP_401_UNAUTHORIZED
            return status.HTTP_502_BAD_GATEWAY
        elif isinstance(error, ExternalServiceError):
            return status.HTTP_503_SERVICE_UNAVAILABLE
        elif isinstance(error, DatabaseError):
            return status.HTTP_500_INTERNAL_SERVER_ERROR
        else:
            return status.HTTP_500_INTERNAL_SERVER_ERROR

    def _format_error_response(
        self,
        error_type: str,
        message: str,
        status_code: int,
        error_id: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
    ) -> Dict[str, Any]:
        """
        Format a consistent error response.

        Args:
            error_type: Type of error
            message: Error message
            status_code: HTTP status code
            error_id: Unique error identifier for tracking
            details: Additional error details

        Returns:
            Dict[str, Any]: Formatted error response
        """
        response = {
            "error": {
                "type": error_type,
                "message": message,
                "status_code": status_code,
            }
        }

        if error_id:
            response["error"]["error_id"] = error_id

        if details:
            response["error"]["details"] = details

        return response

    def _log_unhandled_exception(self, error: Exception, request: Request) -> str:
        """
        Log an unhandled exception and return error ID.

        Args:
            error: Exception that occurred
            request: HTTP request

        Returns:
            str: Unique error ID for tracking
        """
        import uuid

        error_id = str(uuid.uuid4())

        logger.error(
            "Unhandled exception occurred",
            error_id=error_id,
            error_type=error.__class__.__name__,
            error_message=str(error),
            path=request.url.path,
            method=request.method,
            traceback=traceback.format_exc(),
        )

        return error_id

    def _format_validation_error_response(
        self, validation_error: ValidationError
    ) -> Dict[str, Any]:
        """
        Format validation error response with field details.

        Args:
            validation_error: Pydantic validation error

        Returns:
            Dict[str, Any]: Formatted validation error response
        """
        errors = []
        missing_fields = []
        invalid_fields = []
        
        for error in validation_error.errors():
            field_path = ".".join(str(loc) for loc in error["loc"])
            error_detail = {
                "field": field_path,
                "message": error["msg"],
                "type": error["type"],
            }
            
            # Add input value if available for better debugging
            if "input" in error:
                error_detail["received_value"] = error["input"]
            
            errors.append(error_detail)
            
            # Categorize errors for better user guidance
            if error["type"] == "missing":
                missing_fields.append(field_path)
            else:
                invalid_fields.append(field_path)

        # Create helpful summary message
        summary_parts = []
        if missing_fields:
            summary_parts.append(f"Missing required fields: {', '.join(missing_fields)}")
        if invalid_fields:
            summary_parts.append(f"Invalid fields: {', '.join(invalid_fields)}")
        
        summary_message = "; ".join(summary_parts) if summary_parts else "Request validation failed"

        return self._format_error_response(
            error_type="ValidationError",
            message=summary_message,
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            details={
                "validation_errors": errors,
                "missing_required_fields": missing_fields,
                "invalid_fields": invalid_fields,
                "help": "Please check the API documentation for required field formats and examples"
            },
        )

    def _log_unhandled_exception(self, exception: Exception, request: Request) -> str:
        """
        Log unhandled exception with full context.

        Args:
            exception: The unhandled exception
            request: HTTP request that caused the exception

        Returns:
            str: Unique error ID for tracking
        """
        import uuid

        error_id = str(uuid.uuid4())

        return error_id
