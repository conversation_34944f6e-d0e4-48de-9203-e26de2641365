"""
Monitoring middleware for the Trigger Service.

This middleware integrates logging and metrics collection for
comprehensive monitoring of API requests and application performance.
"""

import time
from typing import Callable, Optional
from uuid import uuid4

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import Response as StarletteResponse

from src.utils.logger import get_logger, set_correlation_id
from src.utils.metrics import record_api_request, increment_counter, set_gauge
from src.utils.config import is_feature_enabled

logger = get_logger(__name__)


class MonitoringMiddleware(BaseHTTPMiddleware):
    """
    Middleware for monitoring API requests with logging and metrics.
    
    This middleware automatically:
    - Generates correlation IDs for request tracing
    - Logs API requests with structured data
    - Collects metrics for monitoring and alerting
    - Tracks response times and status codes
    """
    
    def __init__(self, app, enable_detailed_logging: bool = True):
        """
        Initialize the monitoring middleware.
        
        Args:
            app: FastAPI application instance
            enable_detailed_logging: Whether to enable detailed request logging
        """
        super().__init__(app)
        self.enable_detailed_logging = enable_detailed_logging
        self._active_requests = 0
    
    async def dispatch(self, request: Request, call_next: Callable) -> StarletteResponse:
        """
        Process request with monitoring.
        
        Args:
            request: Incoming HTTP request
            call_next: Next middleware or endpoint handler
            
        Returns:
            StarletteResponse: HTTP response with monitoring data
        """
        # Generate correlation ID
        correlation_id = str(uuid4())
        
        # Set correlation ID in context
        set_correlation_id(correlation_id)
        
        # Add correlation ID to request headers for downstream services
        request.state.correlation_id = correlation_id
        
        # Track active requests
        self._active_requests += 1
        if is_feature_enabled("metrics_enabled"):
            set_gauge("active_requests", self._active_requests)
        
        # Start timing
        start_time = time.time()
        
        # Extract request info
        method = request.method
        path = request.url.path
        user_agent = request.headers.get("user-agent", "unknown")
        client_ip = self._get_client_ip(request)
        
        # Get user ID if available
        user_id = getattr(request.state, "user_id", None)
        
        # Log request start
        if self.enable_detailed_logging:
            logger.info(
                "Request started",
                method=method,
                path=path,
                user_agent=user_agent,
                client_ip=client_ip,
                user_id=user_id,
                correlation_id=correlation_id
            )
        
        try:
            # Process request
            response = await call_next(request)
            
            # Calculate response time
            response_time = time.time() - start_time
            response_time_ms = response_time * 1000
            
            # Get response info
            status_code = response.status_code
            content_length = response.headers.get("content-length", "0")
            
            # Add correlation ID to response headers
            response.headers["X-Correlation-ID"] = correlation_id
            
            # Log request completion
            if self.enable_detailed_logging:
                logger.info(
                    "Request completed",
                    method=method,
                    path=path,
                    status_code=status_code,
                    response_time_ms=round(response_time_ms, 2),
                    content_length=content_length,
                    user_id=user_id,
                    correlation_id=correlation_id
                )
            
            # Record metrics
            if is_feature_enabled("metrics_enabled"):
                record_api_request(
                    method=method,
                    endpoint=self._normalize_endpoint(path),
                    status_code=status_code,
                    duration_ms=response_time_ms,
                    user_id=user_id
                )
                
                # Record status code metrics
                increment_counter(
                    "http_responses_total",
                    method=method,
                    status_code=str(status_code),
                    endpoint=self._normalize_endpoint(path)
                )
                
                # Record response size if available
                if content_length.isdigit():
                    from src.utils.metrics import observe_histogram
                    observe_histogram(
                        "http_response_size_bytes",
                        float(content_length),
                        method=method,
                        endpoint=self._normalize_endpoint(path)
                    )
            
            return response
            
        except Exception as e:
            # Calculate response time for errors
            response_time = time.time() - start_time
            response_time_ms = response_time * 1000
            
            # Log error
            logger.error(
                "Request failed",
                method=method,
                path=path,
                error_type=type(e).__name__,
                error_message=str(e),
                response_time_ms=round(response_time_ms, 2),
                user_id=user_id,
                correlation_id=correlation_id,
                exc_info=True
            )
            
            # Record error metrics
            if is_feature_enabled("metrics_enabled"):
                increment_counter(
                    "http_errors_total",
                    method=method,
                    endpoint=self._normalize_endpoint(path),
                    error_type=type(e).__name__
                )
            
            # Re-raise the exception
            raise
            
        finally:
            # Update active requests count
            self._active_requests -= 1
            if is_feature_enabled("metrics_enabled"):
                set_gauge("active_requests", self._active_requests)
    
    def _get_client_ip(self, request: Request) -> str:
        """
        Extract client IP address from request.
        
        Args:
            request: HTTP request
            
        Returns:
            str: Client IP address
        """
        # Check for forwarded headers (load balancer/proxy)
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            # Take the first IP in the chain
            return forwarded_for.split(",")[0].strip()
        
        # Check for real IP header
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fall back to direct client IP
        if hasattr(request.client, "host"):
            return request.client.host
        
        return "unknown"
    
    def _normalize_endpoint(self, path: str) -> str:
        """
        Normalize endpoint path for metrics.
        
        This replaces path parameters with placeholders to avoid
        high cardinality in metrics.
        
        Args:
            path: Request path
            
        Returns:
            str: Normalized endpoint path
        """
        # Common patterns to normalize
        import re
        
        # Replace UUIDs with placeholder
        path = re.sub(
            r'/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}',
            '/{id}',
            path,
            flags=re.IGNORECASE
        )
        
        # Replace numeric IDs with placeholder
        path = re.sub(r'/\d+', '/{id}', path)
        
        # Replace user IDs (assuming they're alphanumeric)
        path = re.sub(r'/users/[a-zA-Z0-9_-]+', '/users/{user_id}', path)
        
        return path


class HealthMetricsCollector:
    """
    Collector for application health metrics.
    
    This class periodically collects and updates health-related metrics
    for monitoring application status.
    """
    
    def __init__(self):
        """Initialize the health metrics collector."""
        self.logger = get_logger(__name__)
    
    async def collect_system_metrics(self) -> None:
        """Collect system-level metrics."""
        if not is_feature_enabled("metrics_enabled"):
            return
        
        try:
            import psutil
            
            # CPU usage
            cpu_percent = psutil.cpu_percent(interval=1)
            set_gauge("system_cpu_usage_percent", cpu_percent)
            
            # Memory usage
            memory = psutil.virtual_memory()
            set_gauge("system_memory_usage_percent", memory.percent)
            set_gauge("system_memory_available_bytes", memory.available)
            
            # Disk usage
            disk = psutil.disk_usage('/')
            set_gauge("system_disk_usage_percent", disk.percent)
            set_gauge("system_disk_free_bytes", disk.free)
            
        except ImportError:
            # psutil not available, skip system metrics
            pass
        except Exception as e:
            self.logger.warning(f"Failed to collect system metrics: {e}")
    
    async def collect_application_metrics(self) -> None:
        """Collect application-specific metrics."""
        if not is_feature_enabled("metrics_enabled"):
            return
        
        try:
            from src.database.connection import get_db_manager
            from sqlalchemy import text
            
            # Database metrics
            db_manager = get_db_manager()
            async with db_manager.get_async_session() as session:
                # Count active triggers
                result = await session.execute(
                    text("SELECT COUNT(*) FROM triggers WHERE is_active = true")
                )
                active_triggers = result.scalar() or 0
                set_gauge("active_triggers_total", active_triggers)
                
                # Count total triggers
                result = await session.execute(text("SELECT COUNT(*) FROM triggers"))
                total_triggers = result.scalar() or 0
                set_gauge("triggers_total", total_triggers)
                
                # Count active schedulers
                result = await session.execute(
                    text("SELECT COUNT(*) FROM schedulers WHERE is_active = true")
                )
                active_schedulers = result.scalar() or 0
                set_gauge("active_schedulers_total", active_schedulers)
                
        except Exception as e:
            self.logger.warning(f"Failed to collect application metrics: {e}")
    
    async def collect_adapter_metrics(self) -> None:
        """Collect adapter health metrics."""
        if not is_feature_enabled("metrics_enabled"):
            return
        
        try:
            from src.core.dependencies import get_adapter_factory
            
            adapter_factory = get_adapter_factory()
            adapter_names = adapter_factory.list_adapters()
            
            for adapter_name in adapter_names:
                try:
                    adapter = adapter_factory.get_adapter(adapter_name)
                    if adapter and hasattr(adapter, '_perform_health_check'):
                        is_healthy = await adapter._perform_health_check()
                        from src.utils.metrics import record_adapter_health
                        record_adapter_health(adapter_name, is_healthy)
                except Exception as e:
                    self.logger.warning(f"Failed to check health for adapter {adapter_name}: {e}")
                    from src.utils.metrics import record_adapter_health
                    record_adapter_health(adapter_name, False)
                    
        except Exception as e:
            self.logger.warning(f"Failed to collect adapter metrics: {e}")


# Global health metrics collector
_health_collector: Optional[HealthMetricsCollector] = None


def get_health_metrics_collector() -> HealthMetricsCollector:
    """
    Get the global health metrics collector.
    
    Returns:
        HealthMetricsCollector: Health metrics collector instance
    """
    global _health_collector
    if _health_collector is None:
        _health_collector = HealthMetricsCollector()
    return _health_collector
