"""
Rate limiting middleware for the Trigger Service.

This module provides comprehensive rate limiting capabilities
to protect against abuse and ensure fair usage.
"""

import time
from typing import Dict, Optional, Tuple
from collections import defaultdict, deque

from fastapi import Request, Response, HTTPException, status
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

from src.utils.logger import get_logger
from src.utils.config import get_settings, is_feature_enabled

logger = get_logger(__name__)


class TokenBucket:
    """
    Token bucket implementation for rate limiting.
    """
    
    def __init__(self, capacity: int, refill_rate: float):
        """
        Initialize token bucket.
        
        Args:
            capacity: Maximum number of tokens
            refill_rate: Tokens per second refill rate
        """
        self.capacity = capacity
        self.refill_rate = refill_rate
        self.tokens = capacity
        self.last_refill = time.time()
    
    def consume(self, tokens: int = 1) -> bool:
        """
        Try to consume tokens from the bucket.
        
        Args:
            tokens: Number of tokens to consume
            
        Returns:
            bool: True if tokens were consumed
        """
        now = time.time()
        
        # Refill tokens based on time passed
        time_passed = now - self.last_refill
        self.tokens = min(
            self.capacity,
            self.tokens + time_passed * self.refill_rate
        )
        self.last_refill = now
        
        # Check if we have enough tokens
        if self.tokens >= tokens:
            self.tokens -= tokens
            return True
        
        return False
    
    def time_until_available(self, tokens: int = 1) -> float:
        """
        Calculate time until tokens are available.
        
        Args:
            tokens: Number of tokens needed
            
        Returns:
            float: Time in seconds until tokens available
        """
        if self.tokens >= tokens:
            return 0.0
        
        needed_tokens = tokens - self.tokens
        return needed_tokens / self.refill_rate


class SlidingWindowCounter:
    """
    Sliding window counter for rate limiting.
    """
    
    def __init__(self, window_size: int, max_requests: int):
        """
        Initialize sliding window counter.
        
        Args:
            window_size: Window size in seconds
            max_requests: Maximum requests per window
        """
        self.window_size = window_size
        self.max_requests = max_requests
        self.requests = deque()
    
    def is_allowed(self) -> bool:
        """
        Check if request is allowed.
        
        Returns:
            bool: True if request is allowed
        """
        now = time.time()
        
        # Remove old requests outside the window
        while self.requests and self.requests[0] <= now - self.window_size:
            self.requests.popleft()
        
        # Check if we're under the limit
        if len(self.requests) < self.max_requests:
            self.requests.append(now)
            return True
        
        return False
    
    def time_until_available(self) -> float:
        """
        Calculate time until next request is allowed.
        
        Returns:
            float: Time in seconds until next request allowed
        """
        if len(self.requests) < self.max_requests:
            return 0.0
        
        # Time until oldest request falls out of window
        oldest_request = self.requests[0]
        return oldest_request + self.window_size - time.time()


class RateLimitConfig:
    """Configuration for rate limiting rules."""
    
    def __init__(
        self,
        requests_per_minute: int = 60,
        requests_per_hour: int = 1000,
        burst_limit: int = 10,
        enable_per_user: bool = True,
        enable_per_ip: bool = True,
        whitelist_ips: Optional[list] = None,
        custom_limits: Optional[Dict[str, Dict[str, int]]] = None
    ):
        """
        Initialize rate limit configuration.
        
        Args:
            requests_per_minute: Requests per minute limit
            requests_per_hour: Requests per hour limit
            burst_limit: Burst limit for token bucket
            enable_per_user: Enable per-user rate limiting
            enable_per_ip: Enable per-IP rate limiting
            whitelist_ips: List of whitelisted IP addresses
            custom_limits: Custom limits for specific endpoints
        """
        self.requests_per_minute = requests_per_minute
        self.requests_per_hour = requests_per_hour
        self.burst_limit = burst_limit
        self.enable_per_user = enable_per_user
        self.enable_per_ip = enable_per_ip
        self.whitelist_ips = whitelist_ips or []
        self.custom_limits = custom_limits or {}


class RateLimitingMiddleware(BaseHTTPMiddleware):
    """
    Rate limiting middleware with multiple strategies.
    """
    
    def __init__(self, app, config: Optional[RateLimitConfig] = None):
        """
        Initialize rate limiting middleware.
        
        Args:
            app: FastAPI application
            config: Rate limiting configuration
        """
        super().__init__(app)
        self.config = config or RateLimitConfig()
        
        # Storage for rate limiters
        self.ip_buckets: Dict[str, TokenBucket] = {}
        self.user_buckets: Dict[str, TokenBucket] = {}
        self.ip_windows: Dict[str, SlidingWindowCounter] = {}
        self.user_windows: Dict[str, SlidingWindowCounter] = {}
        
        # Cleanup tracking
        self.last_cleanup = time.time()
        self.cleanup_interval = 300  # 5 minutes
        
        logger.info("Rate limiting middleware initialized")
    
    async def dispatch(self, request: Request, call_next):
        """
        Process request with rate limiting.
        
        Args:
            request: HTTP request
            call_next: Next middleware or endpoint
            
        Returns:
            Response: HTTP response
        """
        # Skip rate limiting if disabled
        if not is_feature_enabled("rate_limiting_enabled"):
            return await call_next(request)
        
        # Cleanup old entries periodically
        await self._cleanup_old_entries()
        
        # Get client information
        client_ip = self._get_client_ip(request)
        user_id = getattr(request.state, "user_id", None)
        endpoint = self._normalize_endpoint(request.url.path)
        
        # Check if IP is whitelisted
        if client_ip in self.config.whitelist_ips:
            return await call_next(request)
        
        # Check rate limits
        rate_limit_result = await self._check_rate_limits(
            client_ip, user_id, endpoint, request.method
        )
        
        if not rate_limit_result["allowed"]:
            return self._create_rate_limit_response(rate_limit_result)
        
        # Process request
        response = await call_next(request)
        
        # Add rate limit headers
        self._add_rate_limit_headers(response, rate_limit_result)
        
        return response
    
    async def _check_rate_limits(
        self,
        client_ip: str,
        user_id: Optional[str],
        endpoint: str,
        method: str
    ) -> Dict[str, any]:
        """
        Check all applicable rate limits.
        
        Args:
            client_ip: Client IP address
            user_id: User ID if authenticated
            endpoint: Normalized endpoint path
            method: HTTP method
            
        Returns:
            Dict: Rate limit check result
        """
        result = {
            "allowed": True,
            "limit_type": None,
            "limit_value": None,
            "remaining": None,
            "reset_time": None,
        }
        
        # Get custom limits for endpoint
        endpoint_limits = self.config.custom_limits.get(
            f"{method} {endpoint}",
            {}
        )
        
        # Check IP-based rate limits
        if self.config.enable_per_ip:
            ip_result = await self._check_ip_rate_limit(
                client_ip, endpoint_limits
            )
            if not ip_result["allowed"]:
                result.update(ip_result)
                result["limit_type"] = "ip"
                return result
        
        # Check user-based rate limits
        if self.config.enable_per_user and user_id:
            user_result = await self._check_user_rate_limit(
                user_id, endpoint_limits
            )
            if not user_result["allowed"]:
                result.update(user_result)
                result["limit_type"] = "user"
                return result
        
        return result
    
    async def _check_ip_rate_limit(
        self,
        client_ip: str,
        endpoint_limits: Dict[str, int]
    ) -> Dict[str, any]:
        """Check IP-based rate limits."""
        # Get or create token bucket for burst protection
        if client_ip not in self.ip_buckets:
            burst_limit = endpoint_limits.get("burst_limit", self.config.burst_limit)
            self.ip_buckets[client_ip] = TokenBucket(
                capacity=burst_limit,
                refill_rate=burst_limit / 60.0  # Refill over 1 minute
            )
        
        bucket = self.ip_buckets[client_ip]
        
        # Check burst limit
        if not bucket.consume():
            return {
                "allowed": False,
                "limit_value": bucket.capacity,
                "remaining": int(bucket.tokens),
                "reset_time": time.time() + bucket.time_until_available(),
            }
        
        # Get or create sliding window for sustained rate
        if client_ip not in self.ip_windows:
            requests_per_hour = endpoint_limits.get(
                "requests_per_hour",
                self.config.requests_per_hour
            )
            self.ip_windows[client_ip] = SlidingWindowCounter(
                window_size=3600,  # 1 hour
                max_requests=requests_per_hour
            )
        
        window = self.ip_windows[client_ip]
        
        # Check sustained rate limit
        if not window.is_allowed():
            return {
                "allowed": False,
                "limit_value": window.max_requests,
                "remaining": window.max_requests - len(window.requests),
                "reset_time": time.time() + window.time_until_available(),
            }
        
        return {"allowed": True}
    
    async def _check_user_rate_limit(
        self,
        user_id: str,
        endpoint_limits: Dict[str, int]
    ) -> Dict[str, any]:
        """Check user-based rate limits."""
        # Similar implementation to IP rate limiting but for users
        # Get or create token bucket for burst protection
        if user_id not in self.user_buckets:
            burst_limit = endpoint_limits.get("burst_limit", self.config.burst_limit)
            self.user_buckets[user_id] = TokenBucket(
                capacity=burst_limit,
                refill_rate=burst_limit / 60.0
            )
        
        bucket = self.user_buckets[user_id]
        
        # Check burst limit
        if not bucket.consume():
            return {
                "allowed": False,
                "limit_value": bucket.capacity,
                "remaining": int(bucket.tokens),
                "reset_time": time.time() + bucket.time_until_available(),
            }
        
        # Get or create sliding window for sustained rate
        if user_id not in self.user_windows:
            requests_per_hour = endpoint_limits.get(
                "requests_per_hour",
                self.config.requests_per_hour
            )
            self.user_windows[user_id] = SlidingWindowCounter(
                window_size=3600,
                max_requests=requests_per_hour
            )
        
        window = self.user_windows[user_id]
        
        # Check sustained rate limit
        if not window.is_allowed():
            return {
                "allowed": False,
                "limit_value": window.max_requests,
                "remaining": window.max_requests - len(window.requests),
                "reset_time": time.time() + window.time_until_available(),
            }
        
        return {"allowed": True}
    
    def _create_rate_limit_response(self, result: Dict[str, any]) -> JSONResponse:
        """Create rate limit exceeded response."""
        retry_after = int(result.get("reset_time", time.time()) - time.time())
        
        return JSONResponse(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            content={
                "error": "Rate limit exceeded",
                "limit_type": result.get("limit_type"),
                "limit": result.get("limit_value"),
                "remaining": result.get("remaining", 0),
                "retry_after": max(1, retry_after),
            },
            headers={
                "Retry-After": str(max(1, retry_after)),
                "X-RateLimit-Limit": str(result.get("limit_value", 0)),
                "X-RateLimit-Remaining": str(result.get("remaining", 0)),
                "X-RateLimit-Reset": str(int(result.get("reset_time", time.time()))),
            }
        )
    
    def _add_rate_limit_headers(self, response: Response, result: Dict[str, any]):
        """Add rate limit headers to response."""
        if result.get("limit_value"):
            response.headers["X-RateLimit-Limit"] = str(result["limit_value"])
        if result.get("remaining") is not None:
            response.headers["X-RateLimit-Remaining"] = str(result["remaining"])
        if result.get("reset_time"):
            response.headers["X-RateLimit-Reset"] = str(int(result["reset_time"]))
    
    def _get_client_ip(self, request: Request) -> str:
        """Extract client IP from request."""
        # Check for forwarded headers
        forwarded_for = request.headers.get("x-forwarded-for")
        if forwarded_for:
            return forwarded_for.split(",")[0].strip()
        
        real_ip = request.headers.get("x-real-ip")
        if real_ip:
            return real_ip
        
        # Fall back to direct client IP
        if hasattr(request.client, "host"):
            return request.client.host
        
        return "unknown"
    
    def _normalize_endpoint(self, path: str) -> str:
        """Normalize endpoint path for rate limiting."""
        import re
        
        # Replace UUIDs with placeholder
        path = re.sub(
            r'/[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}',
            '/{id}',
            path,
            flags=re.IGNORECASE
        )
        
        # Replace numeric IDs
        path = re.sub(r'/\d+', '/{id}', path)
        
        # Replace user IDs
        path = re.sub(r'/users/[a-zA-Z0-9_-]+', '/users/{user_id}', path)
        
        return path
    
    async def _cleanup_old_entries(self):
        """Clean up old rate limiting entries."""
        now = time.time()
        
        if now - self.last_cleanup < self.cleanup_interval:
            return
        
        # Clean up token buckets (remove inactive ones)
        inactive_threshold = 3600  # 1 hour
        
        for ip_list in [self.ip_buckets, self.user_buckets]:
            to_remove = []
            for key, bucket in ip_list.items():
                if now - bucket.last_refill > inactive_threshold:
                    to_remove.append(key)
            
            for key in to_remove:
                del ip_list[key]
        
        # Clean up sliding windows (they clean themselves)
        self.last_cleanup = now
        
        logger.debug("Rate limiting cleanup completed")
