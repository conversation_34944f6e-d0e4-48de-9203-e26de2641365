"""
Standardized API request and response models.

This module contains Pydantic models for API requests and responses
with consistent validation and serialization.
"""

from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from uuid import UUID
from enum import Enum

from pydantic import BaseModel, Field, ConfigDict, validator

from src.adapters.base import TriggerEventType, TriggerStatus


class TriggerStatusEnum(str, Enum):
    """Trigger status enumeration for API."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    PENDING = "pending"


class ExecutionStatusEnum(str, Enum):
    """Execution status enumeration for API."""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class ScheduleFrequencyEnum(str, Enum):
    """Schedule frequency enumeration for API."""

    ONCE = "once"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    YEARLY = "yearly"
    CUSTOM = "custom"


# Base models
class BaseAPIModel(BaseModel):
    """Base model for all API models."""

    model_config = ConfigDict(
        from_attributes=True,
        json_encoders={
            datetime: lambda v: v.isoformat(),
            UUID: str,
        },
        str_strip_whitespace=True,
        validate_assignment=True,
    )


class TimestampMixin(BaseModel):
    """Mixin for models with timestamps."""

    created_at: datetime = Field(description="Creation timestamp")
    updated_at: datetime = Field(description="Last update timestamp")


class UserMixin(BaseModel):
    """Mixin for models with user association."""

    user_id: str = Field(description="User ID")


# Trigger models
class TriggerConfigModel(BaseAPIModel):
    """Trigger configuration model."""

    calendar_id: Optional[str] = Field(
        default=None, description="Google Calendar ID for calendar triggers"
    )

    webhook_ttl: Optional[int] = Field(
        default=3600, ge=300, le=86400, description="Webhook TTL in seconds"
    )

    # Additional configuration fields can be added here
    additional_config: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Additional adapter-specific configuration"
    )


class CreateTriggerRequest(BaseAPIModel):
    """Request model for creating a trigger."""

    name: str = Field(min_length=1, max_length=255, description="Trigger name")

    description: Optional[str] = Field(
        default=None, max_length=1000, description="Trigger description"
    )

    trigger_type: str = Field(
        min_length=1,
        max_length=50,
        description="Type of trigger (e.g., 'google_calendar')",
    )

    workflow_id: str = Field(
        min_length=1, max_length=255, description="Workflow ID to execute"
    )

    event_types: List[str] = Field(
        min_items=1, description="List of event types to monitor"
    )

    config: TriggerConfigModel = Field(
        default_factory=TriggerConfigModel, description="Trigger configuration"
    )

    is_active: bool = Field(default=True, description="Whether the trigger is active")

    @validator("event_types")
    def validate_event_types(cls, v):
        """Validate event types."""
        valid_types = {e.value for e in TriggerEventType}
        for event_type in v:
            if event_type not in valid_types:
                raise ValueError(f"Invalid event type: {event_type}")
        return v


class UpdateTriggerRequest(BaseAPIModel):
    """Request model for updating a trigger."""

    name: Optional[str] = Field(
        default=None, min_length=1, max_length=255, description="Trigger name"
    )

    description: Optional[str] = Field(
        default=None, max_length=1000, description="Trigger description"
    )

    event_types: Optional[List[str]] = Field(
        default=None, min_items=1, description="List of event types to monitor"
    )

    config: Optional[TriggerConfigModel] = Field(
        default=None, description="Trigger configuration"
    )

    is_active: Optional[bool] = Field(
        default=None, description="Whether the trigger is active"
    )

    @validator("event_types")
    def validate_event_types(cls, v):
        """Validate event types."""
        if v is not None:
            valid_types = {e.value for e in TriggerEventType}
            for event_type in v:
                if event_type not in valid_types:
                    raise ValueError(f"Invalid event type: {event_type}")
        return v


class TriggerResponse(BaseAPIModel, TimestampMixin, UserMixin):
    """Response model for trigger data."""

    id: UUID = Field(description="Trigger ID")
    name: str = Field(description="Trigger name")
    description: Optional[str] = Field(description="Trigger description")
    trigger_type: str = Field(description="Trigger type")
    workflow_id: str = Field(description="Workflow ID")
    event_types: List[str] = Field(description="Event types")
    config: Dict[str, Any] = Field(description="Trigger configuration")
    status: TriggerStatusEnum = Field(description="Trigger status")
    is_active: bool = Field(description="Whether trigger is active")
    external_id: Optional[str] = Field(description="External service ID")
    last_triggered_at: Optional[datetime] = Field(description="Last trigger time")
    error_count: int = Field(description="Error count")
    last_error: Optional[str] = Field(description="Last error message")
    last_error_at: Optional[datetime] = Field(description="Last error time")


# Scheduler models
class SchedulerConfigModel(BaseAPIModel):
    """Scheduler configuration model."""

    frequency: ScheduleFrequencyEnum = Field(description="Schedule frequency")

    cron_expression: Optional[str] = Field(
        default=None, description="Cron expression for custom schedules"
    )

    timezone: str = Field(default="UTC", description="Timezone for schedule execution")

    time: Optional[str] = Field(
        default=None,
        pattern=r"^([01]?[0-9]|2[0-3]):[0-5][0-9]$",
        description="Time of day (HH:MM format)",
    )

    days_of_week: Optional[List[int]] = Field(
        default=None, description="Days of week (0=Monday, 6=Sunday)"
    )

    days_of_month: Optional[List[int]] = Field(
        default=None, description="Days of month (1-31)"
    )

    @validator("days_of_week")
    def validate_days_of_week(cls, v):
        """Validate days of week."""
        if v is not None:
            for day in v:
                if not isinstance(day, int) or day < 0 or day > 6:
                    raise ValueError("Days of week must be integers between 0 and 6")
        return v

    @validator("days_of_month")
    def validate_days_of_month(cls, v):
        """Validate days of month."""
        if v is not None:
            for day in v:
                if not isinstance(day, int) or day < 1 or day > 31:
                    raise ValueError("Days of month must be integers between 1 and 31")
        return v


class CreateSchedulerRequest(BaseAPIModel):
    """Request model for creating a scheduler."""

    name: str = Field(min_length=1, max_length=255, description="Scheduler name")

    description: Optional[str] = Field(
        default=None, max_length=1000, description="Scheduler description"
    )

    workflow_id: str = Field(
        min_length=1, max_length=255, description="Workflow ID to execute"
    )

    schedule: SchedulerConfigModel = Field(description="Schedule configuration")

    input_values: Optional[Dict[str, Any]] = Field(
        default_factory=dict, description="Input values for workflow execution"
    )

    is_active: bool = Field(default=True, description="Whether the scheduler is active")


class UpdateSchedulerRequest(BaseAPIModel):
    """Request model for updating a scheduler."""

    name: Optional[str] = Field(
        default=None, min_length=1, max_length=255, description="Scheduler name"
    )

    description: Optional[str] = Field(
        default=None, max_length=1000, description="Scheduler description"
    )

    schedule: Optional[SchedulerConfigModel] = Field(
        default=None, description="Schedule configuration"
    )

    input_values: Optional[Dict[str, Any]] = Field(
        default=None, description="Input values for workflow execution"
    )

    is_active: Optional[bool] = Field(
        default=None, description="Whether the scheduler is active"
    )


class SchedulerResponse(BaseAPIModel, TimestampMixin, UserMixin):
    """Response model for scheduler data."""

    id: UUID = Field(description="Scheduler ID")
    name: str = Field(description="Scheduler name")
    description: Optional[str] = Field(description="Scheduler description")
    workflow_id: str = Field(description="Workflow ID")
    schedule: Dict[str, Any] = Field(description="Schedule configuration")
    input_values: Optional[Dict[str, Any]] = Field(description="Input values")
    is_active: bool = Field(description="Whether scheduler is active")
    next_run_at: Optional[datetime] = Field(description="Next run time")
    last_run_at: Optional[datetime] = Field(description="Last run time")
    error_count: int = Field(description="Error count")
    last_error: Optional[str] = Field(description="Last error message")
    last_error_at: Optional[datetime] = Field(description="Last error time")


# Execution models
class ExecutionResponse(BaseAPIModel, TimestampMixin):
    """Response model for execution data."""

    id: UUID = Field(description="Execution ID")
    status: ExecutionStatusEnum = Field(description="Execution status")
    workflow_correlation_id: Optional[str] = Field(
        description="Workflow correlation ID"
    )
    started_at: Optional[datetime] = Field(description="Start time")
    completed_at: Optional[datetime] = Field(description="Completion time")
    error_message: Optional[str] = Field(description="Error message")
    retry_count: int = Field(description="Retry count")


class TriggerExecutionResponse(ExecutionResponse):
    """Response model for trigger execution data."""

    trigger_id: UUID = Field(description="Trigger ID")
    event_data: Dict[str, Any] = Field(description="Event data")


class SchedulerExecutionResponse(ExecutionResponse):
    """Response model for scheduler execution data."""

    scheduler_id: UUID = Field(description="Scheduler ID")
    scheduled_for: datetime = Field(description="Scheduled execution time")


# Health check models
class HealthCheckResponse(BaseAPIModel):
    """Response model for health check."""

    status: str = Field(description="Overall health status")
    timestamp: datetime = Field(description="Check timestamp")
    version: str = Field(description="Application version")
    uptime: float = Field(description="Uptime in seconds")
    checks: Dict[str, Dict[str, Any]] = Field(description="Individual health checks")


# Webhook models
class WebhookEventRequest(BaseAPIModel):
    """Request model for webhook events."""

    headers: Dict[str, str] = Field(description="Webhook headers")
    body: Optional[Union[Dict[str, Any], str]] = Field(description="Webhook body")
    query_params: Optional[Dict[str, str]] = Field(description="Query parameters")


class WebhookEventResponse(BaseAPIModel):
    """Response model for webhook event processing."""

    processed: bool = Field(description="Whether event was processed")
    trigger_id: Optional[UUID] = Field(description="Associated trigger ID")
    execution_id: Optional[UUID] = Field(description="Created execution ID")
    message: str = Field(description="Processing message")
