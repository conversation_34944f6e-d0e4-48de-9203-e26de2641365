"""
Common utilities and shared functions for the Trigger Service.

This module consolidates commonly used functions and utilities
to eliminate code duplication across the codebase.
"""

import asyncio
import hashlib
import hmac
import json
import time
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Union
from uuid import UUID, uuid4

from src.utils.logger import get_logger

logger = get_logger(__name__)


def generate_correlation_id() -> str:
    """
    Generate a unique correlation ID for request tracing.
    
    Returns:
        str: Unique correlation ID
    """
    return str(uuid4())


def generate_webhook_id(prefix: str = "webhook") -> str:
    """
    Generate a unique webhook ID.
    
    Args:
        prefix: Prefix for the webhook ID
        
    Returns:
        str: Unique webhook ID
    """
    return f"{prefix}-{uuid4()}"


def validate_uuid(uuid_string: str) -> bool:
    """
    Validate if a string is a valid UUID.
    
    Args:
        uuid_string: String to validate
        
    Returns:
        bool: True if valid UUID
    """
    try:
        UUID(uuid_string)
        return True
    except ValueError:
        return False


def safe_json_loads(json_string: str, default: Any = None) -> Any:
    """
    Safely parse JSON string with fallback.
    
    Args:
        json_string: JSON string to parse
        default: Default value if parsing fails
        
    Returns:
        Any: Parsed JSON or default value
    """
    try:
        return json.loads(json_string)
    except (json.JSONDecodeError, TypeError):
        return default


def safe_json_dumps(obj: Any, default: str = "{}") -> str:
    """
    Safely serialize object to JSON with fallback.
    
    Args:
        obj: Object to serialize
        default: Default JSON string if serialization fails
        
    Returns:
        str: JSON string or default
    """
    try:
        return json.dumps(obj, default=str, ensure_ascii=False)
    except (TypeError, ValueError):
        return default


def normalize_datetime(dt: Union[datetime, str, None]) -> Optional[datetime]:
    """
    Normalize datetime to UTC timezone.
    
    Args:
        dt: Datetime object, ISO string, or None
        
    Returns:
        Optional[datetime]: Normalized datetime or None
    """
    if dt is None:
        return None
    
    if isinstance(dt, str):
        try:
            dt = datetime.fromisoformat(dt.replace('Z', '+00:00'))
        except ValueError:
            return None
    
    if isinstance(dt, datetime):
        if dt.tzinfo is None:
            dt = dt.replace(tzinfo=timezone.utc)
        else:
            dt = dt.astimezone(timezone.utc)
        return dt
    
    return None


def get_current_utc_datetime() -> datetime:
    """
    Get current UTC datetime.
    
    Returns:
        datetime: Current UTC datetime
    """
    return datetime.now(timezone.utc)


def calculate_exponential_backoff(
    attempt: int,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    jitter: bool = True
) -> float:
    """
    Calculate exponential backoff delay.
    
    Args:
        attempt: Attempt number (0-based)
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds
        jitter: Whether to add random jitter
        
    Returns:
        float: Delay in seconds
    """
    delay = min(base_delay * (2 ** attempt), max_delay)
    
    if jitter:
        import random
        delay *= (0.5 + random.random() * 0.5)  # Add 0-50% jitter
    
    return delay


def validate_webhook_signature(
    payload: bytes,
    signature: str,
    secret: str,
    algorithm: str = "sha256"
) -> bool:
    """
    Validate webhook signature using HMAC.
    
    Args:
        payload: Request payload
        signature: Signature to validate
        secret: Webhook secret
        algorithm: Hash algorithm
        
    Returns:
        bool: True if signature is valid
    """
    try:
        expected_signature = hmac.new(
            secret.encode(),
            payload,
            getattr(hashlib, algorithm)
        ).hexdigest()
        
        # Remove algorithm prefix if present (e.g., "sha256=")
        if "=" in signature:
            signature = signature.split("=", 1)[1]
        
        return hmac.compare_digest(expected_signature, signature)
        
    except Exception as e:
        logger.error(f"Error validating webhook signature: {e}")
        return False


def sanitize_string(
    value: str,
    max_length: int = 255,
    allowed_chars: Optional[str] = None
) -> str:
    """
    Sanitize string input for security.
    
    Args:
        value: String to sanitize
        max_length: Maximum allowed length
        allowed_chars: Regex pattern for allowed characters
        
    Returns:
        str: Sanitized string
    """
    if not isinstance(value, str):
        value = str(value)
    
    # Truncate to max length
    value = value[:max_length]
    
    # Remove null bytes and control characters
    value = ''.join(char for char in value if ord(char) >= 32 or char in '\t\n\r')
    
    # Apply character filter if provided
    if allowed_chars:
        import re
        value = re.sub(f'[^{allowed_chars}]', '', value)
    
    return value.strip()


def deep_merge_dicts(dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
    """
    Deep merge two dictionaries.
    
    Args:
        dict1: First dictionary
        dict2: Second dictionary (takes precedence)
        
    Returns:
        Dict[str, Any]: Merged dictionary
    """
    result = dict1.copy()
    
    for key, value in dict2.items():
        if key in result and isinstance(result[key], dict) and isinstance(value, dict):
            result[key] = deep_merge_dicts(result[key], value)
        else:
            result[key] = value
    
    return result


def extract_error_details(error: Exception) -> Dict[str, Any]:
    """
    Extract structured error details from exception.
    
    Args:
        error: Exception to analyze
        
    Returns:
        Dict[str, Any]: Error details
    """
    return {
        "error_type": type(error).__name__,
        "error_message": str(error),
        "error_module": getattr(error, "__module__", None),
        "error_args": getattr(error, "args", []),
    }


def format_duration(seconds: float) -> str:
    """
    Format duration in human-readable format.
    
    Args:
        seconds: Duration in seconds
        
    Returns:
        str: Formatted duration
    """
    if seconds < 1:
        return f"{seconds * 1000:.1f}ms"
    elif seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h"


def chunk_list(lst: List[Any], chunk_size: int) -> List[List[Any]]:
    """
    Split list into chunks of specified size.
    
    Args:
        lst: List to chunk
        chunk_size: Size of each chunk
        
    Returns:
        List[List[Any]]: List of chunks
    """
    return [lst[i:i + chunk_size] for i in range(0, len(lst), chunk_size)]


def retry_async(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exceptions: tuple = (Exception,)
):
    """
    Decorator for async function retry with exponential backoff.
    
    Args:
        max_attempts: Maximum number of attempts
        base_delay: Base delay between attempts
        max_delay: Maximum delay between attempts
        exceptions: Tuple of exceptions to catch
        
    Returns:
        Decorator function
    """
    def decorator(func):
        async def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    return await func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_attempts - 1:
                        break
                    
                    delay = calculate_exponential_backoff(
                        attempt, base_delay, max_delay
                    )
                    
                    logger.warning(
                        f"Attempt {attempt + 1} failed for {func.__name__}: {e}. "
                        f"Retrying in {delay:.2f}s"
                    )
                    
                    await asyncio.sleep(delay)
            
            # Re-raise the last exception
            raise last_exception
        
        return wrapper
    return decorator


class RateLimiter:
    """
    Simple rate limiter using token bucket algorithm.
    """
    
    def __init__(self, max_tokens: int, refill_rate: float):
        """
        Initialize rate limiter.
        
        Args:
            max_tokens: Maximum number of tokens
            refill_rate: Tokens per second refill rate
        """
        self.max_tokens = max_tokens
        self.refill_rate = refill_rate
        self.tokens = max_tokens
        self.last_refill = time.time()
    
    def acquire(self, tokens: int = 1) -> bool:
        """
        Try to acquire tokens.
        
        Args:
            tokens: Number of tokens to acquire
            
        Returns:
            bool: True if tokens acquired
        """
        now = time.time()
        
        # Refill tokens
        time_passed = now - self.last_refill
        self.tokens = min(
            self.max_tokens,
            self.tokens + time_passed * self.refill_rate
        )
        self.last_refill = now
        
        # Check if we have enough tokens
        if self.tokens >= tokens:
            self.tokens -= tokens
            return True
        
        return False
    
    def time_until_available(self, tokens: int = 1) -> float:
        """
        Calculate time until tokens are available.
        
        Args:
            tokens: Number of tokens needed
            
        Returns:
            float: Time in seconds until tokens available
        """
        if self.tokens >= tokens:
            return 0.0
        
        needed_tokens = tokens - self.tokens
        return needed_tokens / self.refill_rate


def create_rate_limiter(requests_per_minute: int) -> RateLimiter:
    """
    Create a rate limiter for requests per minute.
    
    Args:
        requests_per_minute: Maximum requests per minute
        
    Returns:
        RateLimiter: Configured rate limiter
    """
    return RateLimiter(
        max_tokens=requests_per_minute,
        refill_rate=requests_per_minute / 60.0
    )
