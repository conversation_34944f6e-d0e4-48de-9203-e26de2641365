"""
Metrics collection and monitoring for the Trigger Service.

This module provides a comprehensive metrics collection system
for monitoring application performance and health.
"""

import time
from typing import Dict, Any, Optional, List, Union
from datetime import datetime, timezone
from collections import defaultdict, deque
from threading import Lock
from dataclasses import dataclass, field
from enum import Enum

from src.utils.config import is_feature_enabled
from src.utils.logger import get_logger

logger = get_logger(__name__)


class MetricType(str, Enum):
    """Metric type enumeration."""
    COUNTER = "counter"
    GAUGE = "gauge"
    HISTOGRAM = "histogram"
    TIMER = "timer"


@dataclass
class MetricValue:
    """Represents a metric value with metadata."""
    
    value: Union[int, float]
    timestamp: datetime = field(default_factory=lambda: datetime.now(timezone.utc))
    labels: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "value": self.value,
            "timestamp": self.timestamp.isoformat(),
            "labels": self.labels
        }


@dataclass
class HistogramBucket:
    """Histogram bucket for latency measurements."""
    
    upper_bound: float
    count: int = 0
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary representation."""
        return {
            "upper_bound": self.upper_bound,
            "count": self.count
        }


class MetricsCollector:
    """
    Thread-safe metrics collector for application monitoring.
    
    This class provides methods to collect various types of metrics
    including counters, gauges, histograms, and timers.
    """
    
    def __init__(self, max_history: int = 1000):
        """
        Initialize the metrics collector.
        
        Args:
            max_history: Maximum number of historical values to keep
        """
        self.max_history = max_history
        self._lock = Lock()
        
        # Metric storage
        self._counters: Dict[str, int] = defaultdict(int)
        self._gauges: Dict[str, float] = {}
        self._histograms: Dict[str, List[HistogramBucket]] = {}
        self._timers: Dict[str, deque] = defaultdict(lambda: deque(maxlen=max_history))
        
        # Metric metadata
        self._metric_metadata: Dict[str, Dict[str, Any]] = {}
        
        # Initialize default histogram buckets (in milliseconds)
        self._default_buckets = [1, 5, 10, 25, 50, 100, 250, 500, 1000, 2500, 5000, 10000]
        
        logger.info("Metrics collector initialized")
    
    def increment_counter(
        self, 
        name: str, 
        value: int = 1, 
        labels: Optional[Dict[str, str]] = None
    ) -> None:
        """
        Increment a counter metric.
        
        Args:
            name: Metric name
            value: Value to increment by
            labels: Optional labels for the metric
        """
        if not is_feature_enabled("metrics_enabled"):
            return
        
        metric_key = self._get_metric_key(name, labels)
        
        with self._lock:
            self._counters[metric_key] += value
            self._update_metadata(name, MetricType.COUNTER, labels)
    
    def set_gauge(
        self, 
        name: str, 
        value: float, 
        labels: Optional[Dict[str, str]] = None
    ) -> None:
        """
        Set a gauge metric value.
        
        Args:
            name: Metric name
            value: Gauge value
            labels: Optional labels for the metric
        """
        if not is_feature_enabled("metrics_enabled"):
            return
        
        metric_key = self._get_metric_key(name, labels)
        
        with self._lock:
            self._gauges[metric_key] = value
            self._update_metadata(name, MetricType.GAUGE, labels)
    
    def observe_histogram(
        self, 
        name: str, 
        value: float, 
        labels: Optional[Dict[str, str]] = None,
        buckets: Optional[List[float]] = None
    ) -> None:
        """
        Observe a value in a histogram metric.
        
        Args:
            name: Metric name
            value: Observed value
            labels: Optional labels for the metric
            buckets: Optional custom bucket boundaries
        """
        if not is_feature_enabled("metrics_enabled"):
            return
        
        metric_key = self._get_metric_key(name, labels)
        bucket_list = buckets or self._default_buckets
        
        with self._lock:
            if metric_key not in self._histograms:
                self._histograms[metric_key] = [
                    HistogramBucket(upper_bound=bound) for bound in bucket_list
                ]
            
            # Update bucket counts
            for bucket in self._histograms[metric_key]:
                if value <= bucket.upper_bound:
                    bucket.count += 1
            
            self._update_metadata(name, MetricType.HISTOGRAM, labels)
    
    def record_timer(
        self, 
        name: str, 
        duration_ms: float, 
        labels: Optional[Dict[str, str]] = None
    ) -> None:
        """
        Record a timer metric.
        
        Args:
            name: Metric name
            duration_ms: Duration in milliseconds
            labels: Optional labels for the metric
        """
        if not is_feature_enabled("metrics_enabled"):
            return
        
        metric_key = self._get_metric_key(name, labels)
        
        with self._lock:
            self._timers[metric_key].append(MetricValue(
                value=duration_ms,
                labels=labels or {}
            ))
            self._update_metadata(name, MetricType.TIMER, labels)
    
    def get_metrics(self) -> Dict[str, Any]:
        """
        Get all collected metrics.
        
        Returns:
            Dict[str, Any]: All metrics data
        """
        with self._lock:
            return {
                "counters": dict(self._counters),
                "gauges": dict(self._gauges),
                "histograms": {
                    key: [bucket.to_dict() for bucket in buckets]
                    for key, buckets in self._histograms.items()
                },
                "timers": {
                    key: [value.to_dict() for value in values]
                    for key, values in self._timers.items()
                },
                "metadata": dict(self._metric_metadata)
            }
    
    def get_metric_summary(self) -> Dict[str, Any]:
        """
        Get a summary of all metrics.
        
        Returns:
            Dict[str, Any]: Metrics summary
        """
        with self._lock:
            timer_stats = {}
            for key, values in self._timers.items():
                if values:
                    durations = [v.value for v in values]
                    timer_stats[key] = {
                        "count": len(durations),
                        "min": min(durations),
                        "max": max(durations),
                        "avg": sum(durations) / len(durations),
                        "recent": durations[-10:] if len(durations) >= 10 else durations
                    }
            
            return {
                "summary": {
                    "total_counters": len(self._counters),
                    "total_gauges": len(self._gauges),
                    "total_histograms": len(self._histograms),
                    "total_timers": len(self._timers),
                },
                "counters": dict(self._counters),
                "gauges": dict(self._gauges),
                "timer_stats": timer_stats,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
    
    def reset_metrics(self) -> None:
        """Reset all metrics."""
        with self._lock:
            self._counters.clear()
            self._gauges.clear()
            self._histograms.clear()
            self._timers.clear()
            self._metric_metadata.clear()
        
        logger.info("All metrics reset")
    
    def _get_metric_key(self, name: str, labels: Optional[Dict[str, str]]) -> str:
        """Generate a unique key for a metric with labels."""
        if not labels:
            return name
        
        label_str = ",".join(f"{k}={v}" for k, v in sorted(labels.items()))
        return f"{name}{{{label_str}}}"
    
    def _update_metadata(
        self, 
        name: str, 
        metric_type: MetricType, 
        labels: Optional[Dict[str, str]]
    ) -> None:
        """Update metric metadata."""
        if name not in self._metric_metadata:
            self._metric_metadata[name] = {
                "type": metric_type.value,
                "labels": set(),
                "created_at": datetime.now(timezone.utc).isoformat()
            }
        
        if labels:
            self._metric_metadata[name]["labels"].update(labels.keys())


# Global metrics collector instance
_metrics_collector: Optional[MetricsCollector] = None


def get_metrics_collector() -> MetricsCollector:
    """
    Get the global metrics collector instance.
    
    Returns:
        MetricsCollector: Global metrics collector
    """
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = MetricsCollector()
    return _metrics_collector


# Convenience functions for common metrics
def increment_counter(name: str, value: int = 1, **labels) -> None:
    """Increment a counter metric."""
    get_metrics_collector().increment_counter(name, value, labels)


def set_gauge(name: str, value: float, **labels) -> None:
    """Set a gauge metric."""
    get_metrics_collector().set_gauge(name, value, labels)


def observe_histogram(name: str, value: float, **labels) -> None:
    """Observe a histogram metric."""
    get_metrics_collector().observe_histogram(name, value, labels)


def record_timer(name: str, duration_ms: float, **labels) -> None:
    """Record a timer metric."""
    get_metrics_collector().record_timer(name, duration_ms, labels)


# Context manager for timing operations
class Timer:
    """
    Context manager for timing operations.
    
    Usage:
        with Timer("operation_name", user_id="123"):
            # Operation to time
            pass
    """
    
    def __init__(self, metric_name: str, **labels):
        """
        Initialize the timer.
        
        Args:
            metric_name: Name of the metric to record
            **labels: Labels for the metric
        """
        self.metric_name = metric_name
        self.labels = labels
        self.start_time = None
    
    def __enter__(self):
        """Start timing."""
        self.start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Stop timing and record metric."""
        if self.start_time is not None:
            duration_ms = (time.time() - self.start_time) * 1000
            record_timer(self.metric_name, duration_ms, **self.labels)


# Decorator for timing functions
def timed(metric_name: Optional[str] = None, **labels):
    """
    Decorator for timing function execution.
    
    Args:
        metric_name: Optional metric name (defaults to function name)
        **labels: Labels for the metric
    
    Usage:
        @timed("my_function_duration")
        def my_function():
            pass
    """
    def decorator(func):
        def wrapper(*args, **kwargs):
            name = metric_name or f"{func.__module__}.{func.__name__}"
            with Timer(name, **labels):
                return func(*args, **kwargs)
        return wrapper
    return decorator


# Application-specific metrics functions
def record_api_request(
    method: str,
    endpoint: str,
    status_code: int,
    duration_ms: float,
    user_id: Optional[str] = None
) -> None:
    """
    Record API request metrics.
    
    Args:
        method: HTTP method
        endpoint: API endpoint
        status_code: Response status code
        duration_ms: Request duration in milliseconds
        user_id: Optional user ID
    """
    labels = {
        "method": method,
        "endpoint": endpoint,
        "status_code": str(status_code)
    }
    
    if user_id:
        labels["user_id"] = user_id
    
    increment_counter("api_requests_total", **labels)
    record_timer("api_request_duration_ms", duration_ms, **labels)


def record_trigger_execution(
    trigger_type: str,
    status: str,
    duration_ms: float,
    user_id: Optional[str] = None
) -> None:
    """
    Record trigger execution metrics.
    
    Args:
        trigger_type: Type of trigger
        status: Execution status
        duration_ms: Execution duration in milliseconds
        user_id: Optional user ID
    """
    labels = {
        "trigger_type": trigger_type,
        "status": status
    }
    
    if user_id:
        labels["user_id"] = user_id
    
    increment_counter("trigger_executions_total", **labels)
    record_timer("trigger_execution_duration_ms", duration_ms, **labels)


def record_adapter_health(adapter_name: str, is_healthy: bool) -> None:
    """
    Record adapter health metrics.
    
    Args:
        adapter_name: Name of the adapter
        is_healthy: Whether the adapter is healthy
    """
    set_gauge(
        "adapter_health_status",
        1.0 if is_healthy else 0.0,
        adapter=adapter_name
    )


def get_application_metrics() -> Dict[str, Any]:
    """
    Get application-specific metrics summary.
    
    Returns:
        Dict[str, Any]: Application metrics
    """
    collector = get_metrics_collector()
    return collector.get_metric_summary()
