"""
Configuration management for the Trigger Service.

This module provides centralized configuration management using Pydantic
BaseSettings for environment variable validation and type conversion.
"""

from typing import Optional

from pydantic import Field, field_validator
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    """
    Application settings loaded from environment variables.

    This class uses Pydantic BaseSettings to automatically load and validate
    configuration from environment variables with proper type conversion.
    """

    # Application Configuration
    debug: bool = Field(default=False, alias="DEBUG", description="Enable debug mode")
    host: str = Field(alias="HOST", description="Host to bind the server")
    port: int = Field(alias="PORT", description="Port to bind the server")
    log_level: str = Field(
        default="INFO", alias="LOG_LEVEL", description="Logging level"
    )
    log_format: str = Field(
        default="json", alias="LOG_FORMAT", description="Logging format (json or text)"
    )

    # # CORS Configuration
    # cors_origins: List[str] = Field(
    #     default=["*"],
    #     alias="CORS_ORIGINS",
    #     description="Allowed CORS origins",
    # )

    # Database Configuration
    database_url: str = Field(
        ..., alias="DATABASE_URL", description="PostgreSQL database URL"
    )

    # Auth Service Configuration
    auth_service_url: str = Field(
        ..., alias="AUTH_SERVICE_URL", description="URL of the authentication service"
    )
    auth_service_api_key: str = Field(
        ..., alias="AUTH_SERVICE_API_KEY", description="API key for auth service"
    )
    auth_service_timeout: int = Field(
        default=10,
        alias="AUTH_SERVICE_TIMEOUT",
        description="Auth service timeout in seconds",
    )

    # Workflow Service Configuration
    workflow_service_url: str = Field(
        ..., alias="WORKFLOW_SERVICE_URL", description="URL of the workflow service"
    )
    workflow_service_api_key: str = Field(
        ...,
        alias="WORKFLOW_SERVICE_API_KEY",
        description="API key for workflow service",
    )
    workflow_service_timeout: int = Field(
        default=60,
        alias="WORKFLOW_SERVICE_TIMEOUT",
        description="Workflow service timeout in seconds",
    )

    # Google Calendar Configuration
    google_calendar_webhook_url: Optional[str] = Field(
        default=None,
        alias="GOOGLE_CALENDAR_WEBHOOK_URL",
        description="Public URL for Google Calendar webhooks",
    )
    google_calendar_webhook_secret: Optional[str] = Field(
        default=None,
        alias="GOOGLE_CALENDAR_WEBHOOK_SECRET",
        description="Secret key for Google Calendar webhook verification",
    )

    google_drive_webhook_url: Optional[str] = Field(
        default=None,
        alias="GOOGLE_DRIVE_WEBHOOK_URL",
        description="Public URL for Google Drive webhooks",
    )

    # Google Drive Service Account Configuration
    google_drive_service_account_credentials_path: str = Field(
        default="service-account.json",
        alias="GOOGLE_DRIVE_SERVICE_ACCOUNT_CREDENTIALS_PATH",
        description="Path to Google Drive service account credentials file",
    )

    # Google Drive Service Account Credentials (Environment Variables)
    google_drive_service_account_type: Optional[str] = Field(
        default=None,
        alias="GOOGLE_DRIVE_SERVICE_ACCOUNT_TYPE",
        description="Google Drive service account type",
    )
    google_drive_service_account_project_id: Optional[str] = Field(
        default=None,
        alias="GOOGLE_DRIVE_SERVICE_ACCOUNT_PROJECT_ID",
        description="Google Drive service account project ID",
    )
    google_drive_service_account_private_key_id: Optional[str] = Field(
        default=None,
        alias="GOOGLE_DRIVE_SERVICE_ACCOUNT_PRIVATE_KEY_ID",
        description="Google Drive service account private key ID",
    )
    google_drive_service_account_private_key: Optional[str] = Field(
        default=None,
        alias="GOOGLE_DRIVE_SERVICE_ACCOUNT_PRIVATE_KEY",
        description="Google Drive service account private key",
    )
    google_drive_service_account_client_email: Optional[str] = Field(
        default=None,
        alias="GOOGLE_DRIVE_SERVICE_ACCOUNT_CLIENT_EMAIL",
        description="Google Drive service account client email",
    )
    google_drive_service_account_client_id: Optional[str] = Field(
        default=None,
        alias="GOOGLE_DRIVE_SERVICE_ACCOUNT_CLIENT_ID",
        description="Google Drive service account client ID",
    )
    google_drive_service_account_auth_uri: Optional[str] = Field(
        default=None,
        alias="GOOGLE_DRIVE_SERVICE_ACCOUNT_AUTH_URI",
        description="Google Drive service account auth URI",
    )
    google_drive_service_account_token_uri: Optional[str] = Field(
        default=None,
        alias="GOOGLE_DRIVE_SERVICE_ACCOUNT_TOKEN_URI",
        description="Google Drive service account token URI",
    )
    google_drive_service_account_auth_provider_x509_cert_url: Optional[str] = Field(
        default=None,
        alias="GOOGLE_DRIVE_SERVICE_ACCOUNT_AUTH_PROVIDER_X509_CERT_URL",
        description="Google Drive service account auth provider x509 cert URL",
    )
    google_drive_service_account_client_x509_cert_url: Optional[str] = Field(
        default=None,
        alias="GOOGLE_DRIVE_SERVICE_ACCOUNT_CLIENT_X509_CERT_URL",
        description="Google Drive service account client x509 cert URL",
    )
    google_drive_service_account_universe_domain: Optional[str] = Field(
        default=None,
        alias="GOOGLE_DRIVE_SERVICE_ACCOUNT_UNIVERSE_DOMAIN",
        description="Google Drive service account universe domain",
    )

    google_client_id: Optional[str] = Field(
        default=None, alias="GOOGLE_CLIENT_ID", description="Google OAuth client ID"
    )
    google_client_secret: Optional[str] = Field(
        default=None,
        alias="GOOGLE_CLIENT_SECRET",
        description="Google OAuth client secret",
    )

    # Redis Configuration
    redis_url: str = Field(
        default="redis://localhost:6379/0",
        alias="REDIS_URL",
        description="Redis connection URL",
    )

    # Task Queue Configuration
    task_queue_redis_url: str = Field(
        default="redis://localhost:6379/0",
        alias="TASK_QUEUE_REDIS_URL",
        description="Redis URL for task queue",
    )
    task_queue_default_timeout: int = Field(
        default=30,
        alias="TASK_QUEUE_DEFAULT_TIMEOUT",
        description="Default timeout for task queue operations",
    )
    task_queue_max_retries: int = Field(
        default=3,
        alias="TASK_QUEUE_MAX_RETRIES",
        description="Maximum retries for task queue operations",
    )
    task_queue_retry_delay: int = Field(
        default=5,
        alias="TASK_QUEUE_RETRY_DELAY",
        description="Retry delay for task queue operations",
    )

    # Distributed Locking Configuration
    distributed_lock_redis_url: str = Field(
        default="redis://localhost:6379/0",
        alias="DISTRIBUTED_LOCK_REDIS_URL",
        description="Redis URL for distributed locking",
    )
    distributed_lock_default_ttl: int = Field(
        default=300,
        alias="DISTRIBUTED_LOCK_DEFAULT_TTL",
        description="Default TTL for distributed locks",
    )
    distributed_lock_retry_delay: float = Field(
        default=0.1,
        alias="DISTRIBUTED_LOCK_RETRY_DELAY",
        description="Retry delay for distributed lock acquisition",
    )
    distributed_lock_max_retries: int = Field(
        default=10,
        alias="DISTRIBUTED_LOCK_MAX_RETRIES",
        description="Maximum retries for distributed lock acquisition",
    )

    # Legacy Celery Configuration (deprecated)
    celery_broker_url: Optional[str] = Field(
        None, alias="CELERY_BROKER_URL", description="Celery broker URL (deprecated)"
    )
    celery_result_backend: Optional[str] = Field(
        None,
        alias="CELERY_RESULT_BACKEND",
        description="Celery result backend URL (deprecated)",
    )

    # Security
    secret_key: str = Field(
        ..., alias="SECRET_KEY", description="Secret key for cryptographic operations"
    )
    api_key: str = Field(
        ..., alias="API_KEY", description="API key for internal service authentication"
    )

    # HTTP Configuration
    http_timeout: int = Field(
        default=30, alias="HTTP_TIMEOUT", description="Default HTTP timeout in seconds"
    )

    # Retry Configuration
    max_retry_attempts: int = Field(
        default=5,
        alias="MAX_RETRY_ATTEMPTS",
        description="Maximum number of retry attempts",
    )
    retry_backoff_factor: float = Field(
        default=2.0,
        alias="RETRY_BACKOFF_FACTOR",
        description="Exponential backoff factor",
    )
    retry_max_delay: int = Field(
        default=300,
        alias="RETRY_MAX_DELAY",
        description="Maximum retry delay in seconds",
    )

    # Rate Limiting
    rate_limit_requests_per_minute: int = Field(
        default=100,
        alias="RATE_LIMIT_REQUESTS_PER_MINUTE",
        description="Rate limit requests per minute",
    )
    rate_limit_burst: int = Field(
        default=20, alias="RATE_LIMIT_BURST", description="Rate limit burst capacity"
    )

    # Scheduler Configuration
    scheduler_batch_size: int = Field(
        default=50,
        alias="SCHEDULER_BATCH_SIZE",
        description="Batch size for scheduler processing",
    )
    scheduler_concurrency: int = Field(
        default=10,
        alias="SCHEDULER_CONCURRENCY",
        description="Concurrency level for scheduler processing",
    )
    scheduler_cycle_interval: int = Field(
        default=30,
        alias="SCHEDULER_CYCLE_INTERVAL",
        description="Scheduler cycle interval in seconds",
    )

    # Production Scalability Configuration
    enable_concurrent_processing: bool = Field(
        default=True,
        alias="ENABLE_CONCURRENT_PROCESSING",
        description="Enable concurrent processing",
    )
    max_concurrent_schedulers: int = Field(
        default=100,
        alias="MAX_CONCURRENT_SCHEDULERS",
        description="Maximum concurrent schedulers",
    )
    semaphore_limit: int = Field(
        default=10,
        alias="SEMAPHORE_LIMIT",
        description="Semaphore limit for concurrent processing",
    )
    enable_task_queue: bool = Field(
        default=True,
        alias="ENABLE_TASK_QUEUE",
        description="Enable Redis task queue",
    )
    enable_distributed_locking: bool = Field(
        default=True,
        alias="ENABLE_DISTRIBUTED_LOCKING",
        description="Enable distributed locking",
    )

    # Database Optimization
    enable_database_indexes: bool = Field(
        default=True,
        alias="ENABLE_DATABASE_INDEXES",
        description="Enable database indexes",
    )
    database_query_timeout: int = Field(
        default=30,
        alias="DATABASE_QUERY_TIMEOUT",
        description="Database query timeout in seconds",
    )
    database_connection_pool_size: int = Field(
        default=20,
        alias="DATABASE_CONNECTION_POOL_SIZE",
        description="Database connection pool size",
    )
    database_max_overflow: int = Field(
        default=10,
        alias="DATABASE_MAX_OVERFLOW",
        description="Database connection pool max overflow",
    )

    # Task Worker Configuration
    task_worker_concurrency: int = Field(
        default=5,
        alias="TASK_WORKER_CONCURRENCY",
        description="Task worker concurrency level",
    )
    task_worker_queues: str = Field(
        default="workflow_execution,scheduler_tasks",
        alias="TASK_WORKER_QUEUES",
        description="Comma-separated list of task worker queues",
    )
    task_worker_timeout: int = Field(
        default=300,
        alias="TASK_WORKER_TIMEOUT",
        description="Task worker timeout in seconds",
    )

    # Monitoring and Metrics
    enable_metrics: bool = Field(
        default=True, alias="ENABLE_METRICS", description="Enable Prometheus metrics"
    )
    metrics_port: int = Field(
        default=9090, alias="METRICS_PORT", description="Port for metrics endpoint"
    )
    enable_scheduler_metrics: bool = Field(
        default=True,
        alias="ENABLE_SCHEDULER_METRICS",
        description="Enable scheduler metrics collection",
    )
    enable_task_queue_metrics: bool = Field(
        default=True,
        alias="ENABLE_TASK_QUEUE_METRICS",
        description="Enable task queue metrics collection",
    )
    metrics_collection_interval: int = Field(
        default=60,
        alias="METRICS_COLLECTION_INTERVAL",
        description="Metrics collection interval in seconds",
    )

    # Health Check Configuration
    health_check_redis: bool = Field(
        default=True,
        alias="HEALTH_CHECK_REDIS",
        description="Include Redis in health checks",
    )
    health_check_database: bool = Field(
        default=True,
        alias="HEALTH_CHECK_DATABASE",
        description="Include database in health checks",
    )
    health_check_timeout: int = Field(
        default=10,
        alias="HEALTH_CHECK_TIMEOUT",
        description="Health check timeout in seconds",
    )

    # Production Mode Settings
    production_mode: bool = Field(
        default=False,
        alias="PRODUCTION_MODE",
        description="Enable production mode optimizations",
    )
    enable_debug_logging: bool = Field(
        default=True,
        alias="ENABLE_DEBUG_LOGGING",
        description="Enable debug logging",
    )
    log_sql_queries: bool = Field(
        default=False,
        alias="LOG_SQL_QUERIES",
        description="Log SQL queries for debugging",
    )

    # @field_validator("cors_origins", mode="before")
    # @classmethod
    # def validate_cors_origins(cls, v):
    #     """Validate and parse CORS origins from environment variable."""
    #     if v is None:
    #         return ["*"]  # Default to allow all origins

    #     if isinstance(v, str):
    #         v = v.strip()
    #         if not v:
    #             return ["*"]

    #         # Handle JSON array format
    #         if v.startswith("[") and v.endswith("]"):
    #             try:
    #                 return json.loads(v)
    #             except json.JSONDecodeError as e:
    #                 raise ValueError(f"Invalid JSON format for CORS_ORIGINS: {e}")

    #         # Handle comma-separated format
    #         return [origin.strip() for origin in v.split(",") if origin.strip()]

    #     # If it's already a list, return as-is
    #     if isinstance(v, list):
    #         return v

    #     raise ValueError(
    #         f"CORS_ORIGINS must be a JSON array string or comma-separated string, got: {type(v)}"
    #     )

    @field_validator("log_level")
    @classmethod
    def validate_log_level(cls, v: str) -> str:
        """Validate log level is one of the allowed values."""
        allowed_levels = ["DEBUG", "INFO", "WARNING", "ERROR", "CRITICAL"]
        if v.upper() not in allowed_levels:
            raise ValueError(f"Log level must be one of: {allowed_levels}")
        return v.upper()

    @field_validator("log_format")
    @classmethod
    def validate_log_format(cls, v: str) -> str:
        """Validate log format is one of the allowed values."""
        allowed_formats = ["json", "text"]
        if v.lower() not in allowed_formats:
            raise ValueError(f"Log format must be one of: {allowed_formats}")
        return v.lower()

    @field_validator("database_url")
    @classmethod
    def validate_database_url(cls, v: str) -> str:
        """Validate database URL format."""
        if not v.startswith(("postgresql://", "postgresql+psycopg2://")):
            raise ValueError("Database URL must be a PostgreSQL connection string")
        return v

    @field_validator("redis_url")
    @classmethod
    def validate_redis_url(cls, v: str) -> str:
        """Validate Redis URL format."""
        if not v.startswith("redis://"):
            raise ValueError("Redis URL must start with 'redis://'")
        return v

    @field_validator("port", "metrics_port")
    @classmethod
    def validate_port(cls, v: int) -> int:
        """Validate port numbers are in valid range."""
        if not 1 <= v <= 65535:
            raise ValueError("Port must be between 1 and 65535")
        return v

    @field_validator("max_retry_attempts")
    @classmethod
    def validate_max_retry_attempts(cls, v: int) -> int:
        """Validate max retry attempts is positive."""
        if v < 1:
            raise ValueError("Max retry attempts must be at least 1")
        return v

    @field_validator("retry_backoff_factor")
    @classmethod
    def validate_retry_backoff_factor(cls, v: float) -> float:
        """Validate retry backoff factor is positive."""
        if v <= 0:
            raise ValueError("Retry backoff factor must be positive")
        return v

    def get_celery_broker_url(self) -> str:
        """Get Celery broker URL, defaulting to Redis URL if not set."""
        return self.celery_broker_url or self.redis_url

    def get_celery_result_backend(self) -> str:
        """Get Celery result backend URL, defaulting to Redis URL if not set."""
        return self.celery_result_backend or self.redis_url

    model_config = {
        "env_file": ".env",
        "env_file_encoding": "utf-8",
        "case_sensitive": False,
        "validate_assignment": True,
        "extra": "ignore",
    }


def get_settings() -> Settings:
    """
    Get application settings.

    Returns:
        Settings: Application settings instance
    """
    return Settings()


def get_database_url() -> str:
    """
    Get database URL for SQLAlchemy.

    Returns:
        str: Database connection URL
    """
    return get_settings().database_url


def is_development() -> bool:
    """
    Check if the application is running in development mode.

    Returns:
        bool: True if in development mode
    """
    return get_settings().debug


def is_production() -> bool:
    """
    Check if the application is running in production mode.

    Returns:
        bool: True if in production mode
    """
    return not get_settings().debug


# Integration with new configuration system
try:
    from src.core.config_manager import (
        get_config_manager,
        get_config as get_new_config,
        load_config as load_new_config,
        TriggerServiceConfig,
    )

    NEW_CONFIG_AVAILABLE = True
except ImportError:
    NEW_CONFIG_AVAILABLE = False


def get_enhanced_config() -> Optional["TriggerServiceConfig"]:
    """
    Get the enhanced configuration if available.

    Returns:
        TriggerServiceConfig: Enhanced configuration or None if not available
    """
    if NEW_CONFIG_AVAILABLE:
        try:
            return get_new_config()
        except Exception:
            return None
    return None


def load_enhanced_config() -> Optional["TriggerServiceConfig"]:
    """
    Load the enhanced configuration if available.

    Returns:
        TriggerServiceConfig: Enhanced configuration or None if not available
    """
    if NEW_CONFIG_AVAILABLE:
        try:
            return load_new_config()
        except Exception:
            return None
    return None


def is_feature_enabled(feature_name: str) -> bool:
    """
    Check if a feature is enabled using the enhanced configuration.

    Args:
        feature_name: Name of the feature

    Returns:
        bool: True if feature is enabled, False otherwise
    """
    if NEW_CONFIG_AVAILABLE:
        try:
            config_manager = get_config_manager()
            return config_manager.is_feature_enabled(feature_name)
        except Exception:
            pass

    # Fallback to default feature flags
    default_features = {
        "redis_enabled": False,
        "metrics_enabled": True,
        "health_checks_enabled": True,
        "rate_limiting_enabled": True,
        "webhook_validation_enabled": True,
    }
    return default_features.get(feature_name, False)
