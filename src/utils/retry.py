"""
Retry utilities for the Trigger Service.

This module provides retry functionality with exponential backoff
for handling transient failures in external service calls.
"""

import asyncio
import time
from typing import Any, Callable, List, Optional, Type, Union
from functools import wraps

from src.utils.logger import get_logger
from src.utils.common import retry_async, calculate_exponential_backoff

logger = get_logger(__name__)


class RetryableError(Exception):
    """Exception that indicates an operation should be retried."""
    pass


class NonRetryableError(Exception):
    """Exception that indicates an operation should not be retried."""
    pass


class RetryHandler:
    """
    Handler for retry logic with exponential backoff.
    
    This class provides configurable retry behavior for operations
    that may fail due to transient issues.
    """
    
    def __init__(
        self,
        max_attempts: int = 3,
        base_delay: float = 1.0,
        max_delay: float = 60.0,
        backoff_factor: float = 2.0,
        retryable_exceptions: Optional[List[Type[Exception]]] = None,
        non_retryable_exceptions: Optional[List[Type[Exception]]] = None,
    ):
        """
        Initialize the retry handler.
        
        Args:
            max_attempts: Maximum number of retry attempts
            base_delay: Base delay between retries in seconds
            max_delay: Maximum delay between retries in seconds
            backoff_factor: Factor to multiply delay by after each retry
            retryable_exceptions: List of exceptions that should trigger retries
            non_retryable_exceptions: List of exceptions that should not be retried
        """
        self.max_attempts = max_attempts
        self.base_delay = base_delay
        self.max_delay = max_delay
        self.backoff_factor = backoff_factor
        
        # Default retryable exceptions
        self.retryable_exceptions = retryable_exceptions or [
            ConnectionError,
            TimeoutError,
            RetryableError,
        ]
        
        # Default non-retryable exceptions
        self.non_retryable_exceptions = non_retryable_exceptions or [
            ValueError,
            TypeError,
            NonRetryableError,
        ]
    
    def is_retryable(self, exception: Exception) -> bool:
        """
        Determine if an exception should trigger a retry.
        
        Args:
            exception: Exception to check
            
        Returns:
            bool: True if the exception should trigger a retry
        """
        # Check non-retryable exceptions first
        for exc_type in self.non_retryable_exceptions:
            if isinstance(exception, exc_type):
                return False
        
        # Check retryable exceptions
        for exc_type in self.retryable_exceptions:
            if isinstance(exception, exc_type):
                return True
        
        # Default to not retryable
        return False
    
    async def execute_async(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute an async function with retry logic.
        
        Args:
            func: Async function to execute
            *args: Positional arguments for the function
            **kwargs: Keyword arguments for the function
            
        Returns:
            Any: Result of the function execution
            
        Raises:
            Exception: Last exception if all retries fail
        """
        last_exception = None
        
        for attempt in range(self.max_attempts):
            try:
                return await func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                
                if not self.is_retryable(e) or attempt == self.max_attempts - 1:
                    break
                
                delay = calculate_exponential_backoff(
                    attempt, self.base_delay, self.max_delay, self.backoff_factor
                )
                
                logger.warning(
                    f"Attempt {attempt + 1} failed for {func.__name__}: {e}. "
                    f"Retrying in {delay:.2f}s"
                )
                
                await asyncio.sleep(delay)
        
        # Re-raise the last exception
        raise last_exception
    
    def execute_sync(self, func: Callable, *args, **kwargs) -> Any:
        """
        Execute a sync function with retry logic.
        
        Args:
            func: Function to execute
            *args: Positional arguments for the function
            **kwargs: Keyword arguments for the function
            
        Returns:
            Any: Result of the function execution
            
        Raises:
            Exception: Last exception if all retries fail
        """
        last_exception = None
        
        for attempt in range(self.max_attempts):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                last_exception = e
                
                if not self.is_retryable(e) or attempt == self.max_attempts - 1:
                    break
                
                delay = calculate_exponential_backoff(
                    attempt, self.base_delay, self.max_delay, self.backoff_factor
                )
                
                logger.warning(
                    f"Attempt {attempt + 1} failed for {func.__name__}: {e}. "
                    f"Retrying in {delay:.2f}s"
                )
                
                time.sleep(delay)
        
        # Re-raise the last exception
        raise last_exception


def calculate_backoff_delay(
    attempt: int,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    backoff_factor: float = 2.0,
) -> float:
    """
    Calculate exponential backoff delay.
    
    Args:
        attempt: Current attempt number (0-based)
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds
        backoff_factor: Exponential backoff factor
        
    Returns:
        float: Delay in seconds
    """
    return calculate_exponential_backoff(attempt, base_delay, max_delay, backoff_factor)


def retry_sync(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    max_delay: float = 60.0,
    exceptions: tuple = (Exception,)
):
    """
    Decorator for sync function retry with exponential backoff.
    
    Args:
        max_attempts: Maximum number of attempts
        base_delay: Base delay between attempts
        max_delay: Maximum delay between attempts
        exceptions: Tuple of exceptions to catch
        
    Returns:
        Decorator function
    """
    def decorator(func):
        @wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    
                    if attempt == max_attempts - 1:
                        break
                    
                    delay = calculate_exponential_backoff(
                        attempt, base_delay, max_delay
                    )
                    
                    logger.warning(
                        f"Attempt {attempt + 1} failed for {func.__name__}: {e}. "
                        f"Retrying in {delay:.2f}s"
                    )
                    
                    time.sleep(delay)
            
            # Re-raise the last exception
            raise last_exception
        
        return wrapper
    return decorator


# Re-export from common for compatibility
__all__ = [
    "RetryHandler",
    "RetryableError", 
    "NonRetryableError",
    "retry_async",
    "retry_sync",
    "calculate_backoff_delay",
]
