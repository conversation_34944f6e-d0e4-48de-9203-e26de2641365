"""
Improved logging configuration for the Trigger Service.

This module provides structured logging with proper formatting,
correlation IDs, and monitoring integration.
"""

import logging
import logging.config
import sys
import json
from typing import Dict, Any, Optional
from datetime import datetime, timezone
from pathlib import Path

from src.utils.config import get_settings


class StructuredFormatter(logging.Formatter):
    """
    Custom formatter for structured JSON logging.
    
    This formatter outputs log records as JSON with consistent structure
    and additional metadata for monitoring and analysis.
    """
    
    def __init__(self, include_extra: bool = True):
        """
        Initialize the structured formatter.
        
        Args:
            include_extra: Whether to include extra fields from log records
        """
        super().__init__()
        self.include_extra = include_extra
    
    def format(self, record: logging.LogRecord) -> str:
        """
        Format a log record as structured JSON.
        
        Args:
            record: Log record to format
            
        Returns:
            str: JSON-formatted log message
        """
        # Base log structure
        log_entry = {
            "timestamp": datetime.fromtimestamp(record.created, tz=timezone.utc).isoformat(),
            "level": record.levelname,
            "logger": record.name,
            "message": record.getMessage(),
            "module": record.module,
            "function": record.funcName,
            "line": record.lineno,
        }
        
        # Add process and thread info
        log_entry["process"] = {
            "pid": record.process,
            "name": record.processName,
        }
        
        log_entry["thread"] = {
            "id": record.thread,
            "name": record.threadName,
        }
        
        # Add exception info if present
        if record.exc_info:
            log_entry["exception"] = {
                "type": record.exc_info[0].__name__ if record.exc_info[0] else None,
                "message": str(record.exc_info[1]) if record.exc_info[1] else None,
                "traceback": self.formatException(record.exc_info) if record.exc_info else None,
            }
        
        # Add correlation ID if present
        correlation_id = getattr(record, 'correlation_id', None)
        if correlation_id:
            log_entry["correlation_id"] = correlation_id
        
        # Add user ID if present
        user_id = getattr(record, 'user_id', None)
        if user_id:
            log_entry["user_id"] = user_id
        
        # Add request ID if present
        request_id = getattr(record, 'request_id', None)
        if request_id:
            log_entry["request_id"] = request_id
        
        # Add extra fields if enabled
        if self.include_extra:
            extra_fields = {}
            for key, value in record.__dict__.items():
                if key not in {
                    'name', 'msg', 'args', 'levelname', 'levelno', 'pathname',
                    'filename', 'module', 'lineno', 'funcName', 'created',
                    'msecs', 'relativeCreated', 'thread', 'threadName',
                    'processName', 'process', 'exc_info', 'exc_text',
                    'stack_info', 'correlation_id', 'user_id', 'request_id'
                }:
                    try:
                        # Only include JSON-serializable values
                        json.dumps(value)
                        extra_fields[key] = value
                    except (TypeError, ValueError):
                        extra_fields[key] = str(value)
            
            if extra_fields:
                log_entry["extra"] = extra_fields
        
        return json.dumps(log_entry, ensure_ascii=False)


class TextFormatter(logging.Formatter):
    """
    Custom formatter for human-readable text logging.
    
    This formatter provides a clean, readable format for development
    and debugging purposes.
    """
    
    def __init__(self):
        """Initialize the text formatter."""
        super().__init__(
            fmt="%(asctime)s | %(levelname)-8s | %(name)s | %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        )
    
    def format(self, record: logging.LogRecord) -> str:
        """
        Format a log record as human-readable text.
        
        Args:
            record: Log record to format
            
        Returns:
            str: Formatted log message
        """
        # Add correlation ID if present
        correlation_id = getattr(record, 'correlation_id', None)
        if correlation_id:
            record.name = f"{record.name}[{correlation_id[:8]}]"
        
        # Add user ID if present
        user_id = getattr(record, 'user_id', None)
        if user_id:
            record.message = f"[user:{user_id}] {record.getMessage()}"
        
        return super().format(record)


class CorrelationFilter(logging.Filter):
    """
    Filter to add correlation ID to log records.
    
    This filter automatically adds correlation IDs from the current
    context to all log records for request tracing.
    """
    
    def filter(self, record: logging.LogRecord) -> bool:
        """
        Add correlation ID to log record if available.
        
        Args:
            record: Log record to filter
            
        Returns:
            bool: Always True (don't filter out records)
        """
        # Try to get correlation ID from context
        try:
            from src.api.middleware.correlation import get_correlation_id
            correlation_id = get_correlation_id()
            if correlation_id:
                record.correlation_id = correlation_id
        except ImportError:
            pass
        
        return True


def setup_logging(
    log_level: str = "INFO",
    log_format: str = "json",
    log_file: Optional[str] = None,
    enable_correlation: bool = True
) -> None:
    """
    Set up application logging configuration.
    
    Args:
        log_level: Logging level (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        log_format: Log format ("json" or "text")
        log_file: Optional log file path
        enable_correlation: Whether to enable correlation ID logging
    """
    # Determine formatter
    if log_format.lower() == "json":
        formatter = StructuredFormatter()
    else:
        formatter = TextFormatter()
    
    # Configure handlers
    handlers = []
    
    # Console handler
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(formatter)
    if enable_correlation:
        console_handler.addFilter(CorrelationFilter())
    handlers.append(console_handler)
    
    # File handler if specified
    if log_file:
        log_path = Path(log_file)
        log_path.parent.mkdir(parents=True, exist_ok=True)
        
        file_handler = logging.FileHandler(log_file)
        file_handler.setFormatter(formatter)
        if enable_correlation:
            file_handler.addFilter(CorrelationFilter())
        handlers.append(file_handler)
    
    # Configure root logger
    logging.basicConfig(
        level=getattr(logging, log_level.upper()),
        handlers=handlers,
        force=True
    )
    
    # Configure specific loggers
    configure_logger_levels()
    
    # Log configuration
    logger = logging.getLogger(__name__)
    logger.info(
        "Logging configured",
        log_level=log_level,
        log_format=log_format,
        log_file=log_file,
        enable_correlation=enable_correlation
    )


def configure_logger_levels() -> None:
    """Configure specific logger levels to reduce noise."""
    # Reduce noise from third-party libraries
    logging.getLogger("urllib3").setLevel(logging.WARNING)
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    logging.getLogger("asyncio").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.pool").setLevel(logging.WARNING)
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    
    # Set application logger levels
    logging.getLogger("src").setLevel(logging.DEBUG)
    logging.getLogger("src.api").setLevel(logging.INFO)
    logging.getLogger("src.adapters").setLevel(logging.INFO)
    logging.getLogger("src.core").setLevel(logging.INFO)
    logging.getLogger("src.database").setLevel(logging.INFO)


def get_logger(name: str) -> logging.Logger:
    """
    Get a logger instance with the specified name.
    
    Args:
        name: Logger name (typically __name__)
        
    Returns:
        logging.Logger: Logger instance
    """
    return logging.getLogger(name)


def setup_logging_from_config() -> None:
    """Set up logging from application configuration."""
    try:
        settings = get_settings()
        setup_logging(
            log_level=settings.log_level,
            log_format=getattr(settings, 'log_format', 'json'),
            log_file=getattr(settings, 'log_file', None),
            enable_correlation=True
        )
    except Exception as e:
        # Fallback to basic logging if configuration fails
        logging.basicConfig(
            level=logging.INFO,
            format="%(asctime)s | %(levelname)-8s | %(name)s | %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S"
        )
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to configure logging from settings: {e}")


# Context manager for adding extra fields to logs
class LogContext:
    """
    Context manager for adding extra fields to log records.
    
    This allows adding structured data to all log records within
    a specific context (e.g., request processing).
    """
    
    def __init__(self, **kwargs):
        """
        Initialize log context.
        
        Args:
            **kwargs: Extra fields to add to log records
        """
        self.extra_fields = kwargs
        self.old_factory = None
    
    def __enter__(self):
        """Enter the log context."""
        self.old_factory = logging.getLogRecordFactory()
        
        def record_factory(*args, **kwargs):
            record = self.old_factory(*args, **kwargs)
            for key, value in self.extra_fields.items():
                setattr(record, key, value)
            return record
        
        logging.setLogRecordFactory(record_factory)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """Exit the log context."""
        if self.old_factory:
            logging.setLogRecordFactory(self.old_factory)


# Convenience functions for structured logging
def log_with_context(logger: logging.Logger, level: int, message: str, **kwargs) -> None:
    """
    Log a message with additional context fields.
    
    Args:
        logger: Logger instance
        level: Log level
        message: Log message
        **kwargs: Additional context fields
    """
    with LogContext(**kwargs):
        logger.log(level, message)


def log_api_request(
    logger: logging.Logger,
    method: str,
    path: str,
    status_code: int,
    response_time_ms: float,
    user_id: Optional[str] = None,
    **kwargs
) -> None:
    """
    Log an API request with structured data.
    
    Args:
        logger: Logger instance
        method: HTTP method
        path: Request path
        status_code: Response status code
        response_time_ms: Response time in milliseconds
        user_id: Optional user ID
        **kwargs: Additional context fields
    """
    log_data = {
        "event_type": "api_request",
        "http_method": method,
        "http_path": path,
        "http_status_code": status_code,
        "response_time_ms": response_time_ms,
        **kwargs
    }
    
    if user_id:
        log_data["user_id"] = user_id
    
    with LogContext(**log_data):
        logger.info(f"{method} {path} - {status_code} ({response_time_ms:.2f}ms)")
