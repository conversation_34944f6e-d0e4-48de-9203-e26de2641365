"""
Migration helper for transitioning to improved database models.

This module provides utilities to help migrate from the old database
models to the new improved models with better relationships and constraints.
"""

from typing import Dict, Any, List, Optional
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import text, select, update, delete
from sqlalchemy.orm import sessionmaker

from src.utils.logger import get_logger
from src.database.connection import get_db_manager

logger = get_logger(__name__)


class DatabaseMigrationHelper:
    """
    Helper class for database migrations and model improvements.
    
    This class provides methods to safely migrate data from old models
    to new improved models with better structure and constraints.
    """
    
    def __init__(self):
        """Initialize the migration helper."""
        self.db_manager = get_db_manager()
    
    async def check_migration_needed(self) -> Dict[str, bool]:
        """
        Check which migrations are needed.
        
        Returns:
            Dict[str, bool]: Dictionary of migration checks
        """
        checks = {
            "users_table_exists": False,
            "triggers_have_user_fk": False,
            "schedulers_have_user_fk": False,
            "proper_indexes_exist": False,
            "constraints_exist": False,
        }
        
        try:
            async with self.db_manager.get_async_session() as session:
                # Check if users table exists
                result = await session.execute(text("""
                    SELECT EXISTS (
                        SELECT FROM information_schema.tables 
                        WHERE table_schema = 'public' 
                        AND table_name = 'users'
                    );
                """))
                checks["users_table_exists"] = result.scalar()
                
                # Check if triggers have user_id foreign key
                if checks["users_table_exists"]:
                    result = await session.execute(text("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.table_constraints 
                            WHERE constraint_type = 'FOREIGN KEY'
                            AND table_name = 'triggers'
                            AND constraint_name LIKE '%user_id%'
                        );
                    """))
                    checks["triggers_have_user_fk"] = result.scalar()
                
                # Check if schedulers have user_id foreign key
                if checks["users_table_exists"]:
                    result = await session.execute(text("""
                        SELECT EXISTS (
                            SELECT FROM information_schema.table_constraints 
                            WHERE constraint_type = 'FOREIGN KEY'
                            AND table_name = 'schedulers'
                            AND constraint_name LIKE '%user_id%'
                        );
                    """))
                    checks["schedulers_have_user_fk"] = result.scalar()
                
                # Check for proper indexes
                result = await session.execute(text("""
                    SELECT COUNT(*) FROM pg_indexes 
                    WHERE tablename IN ('triggers', 'schedulers', 'trigger_executions', 'scheduler_executions')
                    AND indexname LIKE 'idx_%';
                """))
                checks["proper_indexes_exist"] = result.scalar() > 10
                
                # Check for constraints
                result = await session.execute(text("""
                    SELECT COUNT(*) FROM information_schema.check_constraints 
                    WHERE constraint_name LIKE 'ck_%';
                """))
                checks["constraints_exist"] = result.scalar() > 0
                
        except Exception as e:
            logger.error(f"Error checking migration status: {e}")
        
        return checks
    
    async def create_users_table(self) -> bool:
        """
        Create the users table if it doesn't exist.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            async with self.db_manager.get_async_session() as session:
                await session.execute(text("""
                    CREATE TABLE IF NOT EXISTS users (
                        id VARCHAR(255) PRIMARY KEY,
                        email VARCHAR(255),
                        display_name VARCHAR(255),
                        created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
                        is_active BOOLEAN DEFAULT TRUE
                    );
                """))
                
                # Create indexes
                await session.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);
                """))
                await session.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);
                """))
                await session.execute(text("""
                    CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(created_at);
                """))
                
                await session.commit()
                logger.info("Users table created successfully")
                return True
                
        except Exception as e:
            logger.error(f"Error creating users table: {e}")
            return False
    
    async def migrate_user_data(self) -> bool:
        """
        Migrate user data from existing triggers and schedulers.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            async with self.db_manager.get_async_session() as session:
                # Get unique user IDs from triggers
                result = await session.execute(text("""
                    SELECT DISTINCT user_id FROM triggers 
                    WHERE user_id IS NOT NULL
                    UNION
                    SELECT DISTINCT user_id FROM schedulers 
                    WHERE user_id IS NOT NULL;
                """))
                
                user_ids = [row[0] for row in result.fetchall()]
                
                # Insert users
                for user_id in user_ids:
                    await session.execute(text("""
                        INSERT INTO users (id, created_at, updated_at, is_active)
                        VALUES (:user_id, NOW(), NOW(), TRUE)
                        ON CONFLICT (id) DO NOTHING;
                    """), {"user_id": user_id})
                
                await session.commit()
                logger.info(f"Migrated {len(user_ids)} users")
                return True
                
        except Exception as e:
            logger.error(f"Error migrating user data: {e}")
            return False
    
    async def add_foreign_key_constraints(self) -> bool:
        """
        Add foreign key constraints to existing tables.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            async with self.db_manager.get_async_session() as session:
                # Add foreign key constraint to triggers
                await session.execute(text("""
                    ALTER TABLE triggers 
                    ADD CONSTRAINT fk_triggers_user_id 
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
                """))
                
                # Add foreign key constraint to schedulers
                await session.execute(text("""
                    ALTER TABLE schedulers 
                    ADD CONSTRAINT fk_schedulers_user_id 
                    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE;
                """))
                
                await session.commit()
                logger.info("Foreign key constraints added successfully")
                return True
                
        except Exception as e:
            logger.error(f"Error adding foreign key constraints: {e}")
            return False
    
    async def add_missing_indexes(self) -> bool:
        """
        Add missing indexes for better performance.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            async with self.db_manager.get_async_session() as session:
                # Triggers indexes
                indexes = [
                    "CREATE INDEX IF NOT EXISTS idx_triggers_user_id ON triggers(user_id);",
                    "CREATE INDEX IF NOT EXISTS idx_triggers_type ON triggers(trigger_type);",
                    "CREATE INDEX IF NOT EXISTS idx_triggers_workflow_id ON triggers(workflow_id);",
                    "CREATE INDEX IF NOT EXISTS idx_triggers_active ON triggers(is_active);",
                    "CREATE INDEX IF NOT EXISTS idx_triggers_created_at ON triggers(created_at);",
                    "CREATE INDEX IF NOT EXISTS idx_triggers_last_triggered ON triggers(last_triggered_at);",
                    "CREATE INDEX IF NOT EXISTS idx_triggers_user_type ON triggers(user_id, trigger_type);",
                    
                    # Trigger executions indexes
                    "CREATE INDEX IF NOT EXISTS idx_trigger_executions_trigger_id ON trigger_executions(trigger_id);",
                    "CREATE INDEX IF NOT EXISTS idx_trigger_executions_status ON trigger_executions(status);",
                    "CREATE INDEX IF NOT EXISTS idx_trigger_executions_created_at ON trigger_executions(created_at);",
                    
                    # Schedulers indexes
                    "CREATE INDEX IF NOT EXISTS idx_schedulers_user_id ON schedulers(user_id);",
                    "CREATE INDEX IF NOT EXISTS idx_schedulers_workflow_id ON schedulers(workflow_id);",
                    "CREATE INDEX IF NOT EXISTS idx_schedulers_active ON schedulers(is_active);",
                    "CREATE INDEX IF NOT EXISTS idx_schedulers_next_run ON schedulers(next_run_at);",
                    "CREATE INDEX IF NOT EXISTS idx_schedulers_created_at ON schedulers(created_at);",
                    "CREATE INDEX IF NOT EXISTS idx_schedulers_user_active ON schedulers(user_id, is_active);",
                    "CREATE INDEX IF NOT EXISTS idx_schedulers_active_next_run ON schedulers(is_active, next_run_at);",
                    
                    # Scheduler executions indexes
                    "CREATE INDEX IF NOT EXISTS idx_scheduler_executions_scheduler_id ON scheduler_executions(scheduler_id);",
                    "CREATE INDEX IF NOT EXISTS idx_scheduler_executions_status ON scheduler_executions(status);",
                    "CREATE INDEX IF NOT EXISTS idx_scheduler_executions_created_at ON scheduler_executions(created_at);",
                ]
                
                for index_sql in indexes:
                    await session.execute(text(index_sql))
                
                await session.commit()
                logger.info("Missing indexes added successfully")
                return True
                
        except Exception as e:
            logger.error(f"Error adding indexes: {e}")
            return False
    
    async def add_check_constraints(self) -> bool:
        """
        Add check constraints for data validation.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            async with self.db_manager.get_async_session() as session:
                constraints = [
                    # Triggers constraints
                    "ALTER TABLE triggers ADD CONSTRAINT ck_triggers_error_count_positive CHECK (error_count >= 0);",
                    
                    # Trigger executions constraints
                    "ALTER TABLE trigger_executions ADD CONSTRAINT ck_executions_retry_count_positive CHECK (retry_count >= 0);",
                    
                    # Schedulers constraints
                    "ALTER TABLE schedulers ADD CONSTRAINT ck_schedulers_error_count_positive CHECK (error_count >= 0);",
                    
                    # Scheduler executions constraints
                    "ALTER TABLE scheduler_executions ADD CONSTRAINT ck_scheduler_executions_retry_count_positive CHECK (retry_count >= 0);",
                ]
                
                for constraint_sql in constraints:
                    try:
                        await session.execute(text(constraint_sql))
                    except Exception as e:
                        # Constraint might already exist, log but continue
                        logger.debug(f"Constraint might already exist: {e}")
                
                await session.commit()
                logger.info("Check constraints added successfully")
                return True
                
        except Exception as e:
            logger.error(f"Error adding check constraints: {e}")
            return False
    
    async def run_full_migration(self) -> bool:
        """
        Run the complete migration process.
        
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            logger.info("Starting database migration...")
            
            # Check what needs to be migrated
            checks = await self.check_migration_needed()
            logger.info(f"Migration checks: {checks}")
            
            # Create users table if needed
            if not checks["users_table_exists"]:
                if not await self.create_users_table():
                    return False
                
                # Migrate user data
                if not await self.migrate_user_data():
                    return False
            
            # Add foreign key constraints if needed
            if not checks["triggers_have_user_fk"] or not checks["schedulers_have_user_fk"]:
                if not await self.add_foreign_key_constraints():
                    return False
            
            # Add missing indexes
            if not checks["proper_indexes_exist"]:
                if not await self.add_missing_indexes():
                    return False
            
            # Add check constraints
            if not checks["constraints_exist"]:
                if not await self.add_check_constraints():
                    return False
            
            logger.info("Database migration completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error during migration: {e}")
            return False


async def run_migration():
    """
    Convenience function to run the database migration.
    
    Returns:
        bool: True if successful, False otherwise
    """
    migration_helper = DatabaseMigrationHelper()
    return await migration_helper.run_full_migration()


if __name__ == "__main__":
    import asyncio
    
    async def main():
        success = await run_migration()
        if success:
            print("Migration completed successfully!")
        else:
            print("Migration failed!")
    
    asyncio.run(main())
