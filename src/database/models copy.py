"""
Database models for the Trigger Service.

This module contains SQLAlchemy models with proper relationships,
constraints, and optimized structure for production use.
"""

from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from uuid import UUID, uuid4
from enum import Enum

from sqlalchemy import (
    Boolean,
    DateTime,
    Integer,
    String,
    Text,
    JSON,
    ForeignKey,
    Index,
    UniqueConstraint,
    CheckConstraint,
    Enum as SQLEnum,
)
from sqlalchemy.dialects.postgresql import UUID as PostgreSQLUUID, ARRAY
from sqlalchemy.orm import Mapped, mapped_column, relationship, validates
from sqlalchemy.sql import func

from src.database.connection import Base


class TriggerStatus(str, Enum):
    """Enumeration for trigger status."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    PENDING = "pending"


class ExecutionStatus(str, Enum):
    """Enumeration for execution status."""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class SchedulerStatus(str, Enum):
    """Enumeration for scheduler status."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    PAUSED = "paused"


class BaseModel(Base):
    """Base model with common fields."""

    __abstract__ = True

    id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True), primary_key=True, default=uuid4, index=True
    )

    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), server_default=func.now(), nullable=False, index=True
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        nullable=False,
        index=True,
    )


class UserModel(BaseModel):
    """Base model for user-associated entities."""

    __abstract__ = True

    user_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True)


class Trigger(UserModel):
    """
    Trigger model for storing trigger configurations.

    Triggers monitor external services for events and execute workflows
    when specific conditions are met.
    """

    __tablename__ = "triggers"

    # Basic information
    name: Mapped[str] = mapped_column(String(255), nullable=False, index=True)

    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Trigger configuration
    trigger_type: Mapped[str] = mapped_column(String(50), nullable=False, index=True)

    workflow_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True)

    event_types: Mapped[List[str]] = mapped_column(
        ARRAY(String), nullable=False, default=list
    )

    config: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False, default=dict)

    # Status and control
    status: Mapped[TriggerStatus] = mapped_column(
        SQLEnum(TriggerStatus),
        nullable=False,
        default=TriggerStatus.PENDING,
        index=True,
    )

    is_active: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=True, index=True
    )

    # External service integration
    external_id: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True, index=True
    )

    # Execution tracking
    last_triggered_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, index=True
    )

    # Error tracking
    error_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0)

    last_error: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    last_error_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True
    )

    # Relationships
    executions: Mapped[List["TriggerExecution"]] = relationship(
        "TriggerExecution",
        back_populates="trigger",
        cascade="all, delete-orphan",
        order_by="TriggerExecution.created_at.desc()",
    )

    # Constraints and indexes
    __table_args__ = (
        Index("idx_trigger_user_type", "user_id", "trigger_type"),
        Index("idx_trigger_workflow", "workflow_id"),
        Index("idx_trigger_status_active", "status", "is_active"),
        UniqueConstraint("user_id", "name", name="uq_trigger_user_name"),
        CheckConstraint("char_length(name) >= 1", name="ck_trigger_name_not_empty"),
        CheckConstraint(
            "char_length(trigger_type) >= 1", name="ck_trigger_type_not_empty"
        ),
        CheckConstraint("error_count >= 0", name="ck_trigger_error_count_positive"),
    )

    @validates("event_types")
    def validate_event_types(self, key, value):
        """Validate event types are not empty."""
        if not value or len(value) == 0:
            raise ValueError("Event types cannot be empty")
        return value

    def __repr__(self) -> str:
        return (
            f"<Trigger(id={self.id}, name='{self.name}', type='{self.trigger_type}')>"
        )


class TriggerExecution(BaseModel):
    """
    Trigger execution model for tracking workflow executions.

    Records each time a trigger fires and executes a workflow,
    including status, timing, and error information.
    """

    __tablename__ = "trigger_executions"

    # Foreign key to trigger
    trigger_id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("triggers.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )

    # Execution status and timing
    status: Mapped[ExecutionStatus] = mapped_column(
        SQLEnum(ExecutionStatus),
        nullable=False,
        default=ExecutionStatus.PENDING,
        index=True,
    )

    started_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, index=True
    )

    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, index=True
    )

    # Event and workflow data
    event_data: Mapped[Dict[str, Any]] = mapped_column(
        JSON, nullable=False, default=dict
    )

    workflow_correlation_id: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True, index=True
    )

    # Error handling
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    retry_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0)

    # Relationships
    trigger: Mapped["Trigger"] = relationship("Trigger", back_populates="executions")

    # Constraints and indexes
    __table_args__ = (
        Index("idx_execution_trigger_status", "trigger_id", "status"),
        Index("idx_execution_correlation", "workflow_correlation_id"),
        Index("idx_execution_timing", "started_at", "completed_at"),
        CheckConstraint("retry_count >= 0", name="ck_execution_retry_count_positive"),
        CheckConstraint(
            "(started_at IS NULL AND status = 'pending') OR (started_at IS NOT NULL)",
            name="ck_execution_started_at_consistency",
        ),
        CheckConstraint(
            "(completed_at IS NULL AND status IN ('pending', 'running')) OR "
            "(completed_at IS NOT NULL AND status IN ('completed', 'failed', 'cancelled'))",
            name="ck_execution_completed_at_consistency",
        ),
    )

    def __repr__(self) -> str:
        return f"<TriggerExecution(id={self.id}, trigger_id={self.trigger_id}, status='{self.status}')>"


class Scheduler(UserModel):
    """
    Scheduler model for time-based workflow execution.

    Schedulers execute workflows at specified times or intervals
    based on cron expressions or simple schedules.
    """

    __tablename__ = "schedulers"

    # Basic information
    name: Mapped[str] = mapped_column(String(255), nullable=False, index=True)

    description: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    # Workflow configuration
    workflow_id: Mapped[str] = mapped_column(String(255), nullable=False, index=True)

    # Schedule configuration
    schedule: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False)

    input_values: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, nullable=True, default=dict
    )

    # Status and control
    status: Mapped[SchedulerStatus] = mapped_column(
        SQLEnum(SchedulerStatus),
        nullable=False,
        default=SchedulerStatus.ACTIVE,
        index=True,
    )

    is_active: Mapped[bool] = mapped_column(
        Boolean, nullable=False, default=True, index=True
    )

    # Execution tracking
    next_run_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, index=True
    )

    last_run_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, index=True
    )

    # Error tracking
    error_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0)

    last_error: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    last_error_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True
    )

    # Relationships
    executions: Mapped[List["SchedulerExecution"]] = relationship(
        "SchedulerExecution",
        back_populates="scheduler",
        cascade="all, delete-orphan",
        order_by="SchedulerExecution.created_at.desc()",
    )

    # Constraints and indexes
    __table_args__ = (
        Index("idx_scheduler_user_workflow", "user_id", "workflow_id"),
        Index("idx_scheduler_next_run", "next_run_at"),
        Index("idx_scheduler_status_active", "status", "is_active"),
        UniqueConstraint("user_id", "name", name="uq_scheduler_user_name"),
        CheckConstraint("char_length(name) >= 1", name="ck_scheduler_name_not_empty"),
        CheckConstraint("error_count >= 0", name="ck_scheduler_error_count_positive"),
    )

    def __repr__(self) -> str:
        return f"<Scheduler(id={self.id}, name='{self.name}', workflow_id='{self.workflow_id}')>"


class SchedulerExecution(BaseModel):
    """
    Scheduler execution model for tracking scheduled workflow runs.

    Records each time a scheduler executes a workflow,
    including timing and execution details.
    """

    __tablename__ = "scheduler_executions"

    # Foreign key to scheduler
    scheduler_id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("schedulers.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
    )

    # Execution details
    status: Mapped[ExecutionStatus] = mapped_column(
        SQLEnum(ExecutionStatus),
        nullable=False,
        default=ExecutionStatus.PENDING,
        index=True,
    )

    scheduled_for: Mapped[datetime] = mapped_column(
        DateTime(timezone=True), nullable=False, index=True
    )

    started_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, index=True
    )

    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, index=True
    )

    # Workflow data
    workflow_correlation_id: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True, index=True
    )

    input_values: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, nullable=True, default=dict
    )

    # Error handling
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)

    retry_count: Mapped[int] = mapped_column(Integer, nullable=False, default=0)

    # Relationships
    scheduler: Mapped["Scheduler"] = relationship(
        "Scheduler", back_populates="executions"
    )

    # Constraints and indexes
    __table_args__ = (
        Index("idx_scheduler_execution_scheduler_status", "scheduler_id", "status"),
        Index("idx_scheduler_execution_scheduled", "scheduled_for"),
        Index("idx_scheduler_execution_correlation", "workflow_correlation_id"),
        CheckConstraint(
            "retry_count >= 0", name="ck_scheduler_execution_retry_count_positive"
        ),
    )

    def __repr__(self) -> str:
        return f"<SchedulerExecution(id={self.id}, scheduler_id={self.scheduler_id}, status='{self.status}')>"


# Legacy models for backward compatibility (will be removed in future versions)
class DriveEvents(BaseModel):
    """Legacy Google Drive events model."""

    __tablename__ = "drive_events"

    event_id: Mapped[str] = mapped_column(String(255), nullable=False, unique=True)
    event_type: Mapped[str] = mapped_column(String(50), nullable=False)
    file_id: Mapped[str] = mapped_column(String(255), nullable=False)
    file_name: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    user_email: Mapped[str] = mapped_column(String(255), nullable=False)
    processed: Mapped[bool] = mapped_column(Boolean, default=False)

    __table_args__ = (
        Index("idx_drive_events_processed", "processed"),
        Index("idx_drive_events_user", "user_email"),
    )
