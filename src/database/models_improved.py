"""
Improved database models for the Trigger Service.

This module contains cleaned up SQLAlchemy models with proper relationships,
constraints, and optimized structure.
"""

from datetime import datetime, timezone
from typing import Dict, Any, List, Optional
from uuid import UUID, uuid4
from enum import Enum

from sqlalchemy import (
    <PERSON><PERSON><PERSON>,
    DateTime,
    Integer,
    String,
    Text,
    JSON,
    ForeignKey,
    Index,
    UniqueConstraint,
    CheckConstraint,
    Enum as SQLEnum,
)
from sqlalchemy.dialects.postgresql import UUID as PostgreSQLUUID, ARRAY
from sqlalchemy.orm import Mapped, mapped_column, relationship, validates
from sqlalchemy.sql import func

from src.database.connection import Base


# Enums for better type safety
class TriggerStatus(str, Enum):
    """Trigger status enumeration."""

    ACTIVE = "active"
    INACTIVE = "inactive"
    ERROR = "error"
    PENDING = "pending"


class ExecutionStatus(str, Enum):
    """Execution status enumeration."""

    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class EventType(str, Enum):
    """Event type enumeration."""

    CREATED = "created"
    UPDATED = "updated"
    DELETED = "deleted"
    REMINDER = "reminder"
    TRIGGERED = "triggered"


class User(Base):
    """
    User model for tracking users across the system.

    This model provides a central place to track user information
    and relationships with triggers and executions.
    """

    __tablename__ = "users"

    # Primary key
    id: Mapped[str] = mapped_column(
        String(255), primary_key=True, doc="User ID from external auth system"
    )

    # User metadata
    email: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True, index=True, doc="User email address"
    )

    display_name: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True, doc="User display name"
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        doc="When the user record was created",
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        doc="When the user record was last updated",
    )

    # Status
    is_active: Mapped[bool] = mapped_column(
        Boolean, default=True, doc="Whether the user is active"
    )

    # Relationships
    triggers: Mapped[List["Trigger"]] = relationship(
        "Trigger", back_populates="user", cascade="all, delete-orphan"
    )

    schedulers: Mapped[List["Scheduler"]] = relationship(
        "Scheduler", back_populates="user", cascade="all, delete-orphan"
    )

    # Indexes
    __table_args__ = (
        Index("idx_users_email", "email"),
        Index("idx_users_active", "is_active"),
        Index("idx_users_created_at", "created_at"),
    )


class Trigger(Base):
    """
    Improved trigger model with proper relationships and constraints.
    """

    __tablename__ = "triggers"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        doc="Unique identifier for the trigger",
    )

    # Foreign keys
    user_id: Mapped[str] = mapped_column(
        String(255),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user who owns this trigger",
    )

    # Core trigger information
    name: Mapped[str] = mapped_column(
        String(255), nullable=False, doc="Human-readable name for the trigger"
    )

    description: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, doc="Optional description of the trigger"
    )

    # Trigger configuration
    trigger_type: Mapped[str] = mapped_column(
        String(50),
        nullable=False,
        index=True,
        doc="Type of trigger (e.g., 'google_calendar', 'slack')",
    )

    workflow_id: Mapped[str] = mapped_column(
        String(255),
        nullable=False,
        index=True,
        doc="ID of the workflow to execute when triggered",
    )

    # Configuration and event types
    config: Mapped[Dict[str, Any]] = mapped_column(
        JSON, nullable=False, default=dict, doc="Adapter-specific configuration as JSON"
    )

    event_types: Mapped[List[str]] = mapped_column(
        ARRAY(String),
        nullable=False,
        default=list,
        doc="List of event types to monitor",
    )

    # Status and state
    status: Mapped[TriggerStatus] = mapped_column(
        SQLEnum(TriggerStatus),
        nullable=False,
        default=TriggerStatus.ACTIVE,
        index=True,
        doc="Current status of the trigger",
    )

    is_active: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        index=True,
        doc="Whether the trigger is active",
    )

    # External service integration
    external_id: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        index=True,
        doc="External service identifier (e.g., webhook channel ID)",
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        doc="When the trigger was created",
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        doc="When the trigger was last updated",
    )

    last_triggered_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        doc="When the trigger was last activated",
    )

    # Error tracking
    error_count: Mapped[int] = mapped_column(
        Integer, nullable=False, default=0, doc="Number of consecutive errors"
    )

    last_error: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, doc="Last error message"
    )

    last_error_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, doc="When the last error occurred"
    )

    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="triggers")

    executions: Mapped[List["TriggerExecution"]] = relationship(
        "TriggerExecution",
        back_populates="trigger",
        cascade="all, delete-orphan",
        order_by="TriggerExecution.created_at.desc()",
    )

    # Constraints and indexes
    __table_args__ = (
        Index("idx_triggers_user_id", "user_id"),
        Index("idx_triggers_type", "trigger_type"),
        Index("idx_triggers_workflow_id", "workflow_id"),
        Index("idx_triggers_status", "status"),
        Index("idx_triggers_active", "is_active"),
        Index("idx_triggers_external_id", "external_id"),
        Index("idx_triggers_created_at", "created_at"),
        Index("idx_triggers_last_triggered", "last_triggered_at"),
        Index("idx_triggers_user_type", "user_id", "trigger_type"),
        UniqueConstraint("user_id", "name", name="uq_triggers_user_name"),
        CheckConstraint("error_count >= 0", name="ck_triggers_error_count_positive"),
    )

    @validates("event_types")
    def validate_event_types(self, key, event_types):
        """Validate event types."""
        if not event_types:
            raise ValueError("At least one event type must be specified")

        valid_types = {e.value for e in EventType}
        for event_type in event_types:
            if event_type not in valid_types:
                raise ValueError(f"Invalid event type: {event_type}")

        return event_types

    @validates("trigger_type")
    def validate_trigger_type(self, key, trigger_type):
        """Validate trigger type."""
        if not trigger_type or not trigger_type.strip():
            raise ValueError("Trigger type cannot be empty")
        return trigger_type.strip()


class TriggerExecution(Base):
    """
    Improved trigger execution model with better tracking and relationships.
    """

    __tablename__ = "trigger_executions"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        doc="Unique identifier for the execution",
    )

    # Foreign keys
    trigger_id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("triggers.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the trigger that was executed",
    )

    # Execution details
    status: Mapped[ExecutionStatus] = mapped_column(
        SQLEnum(ExecutionStatus),
        nullable=False,
        default=ExecutionStatus.PENDING,
        index=True,
        doc="Current status of the execution",
    )

    # Event and workflow data
    event_data: Mapped[Dict[str, Any]] = mapped_column(
        JSON,
        nullable=False,
        default=dict,
        doc="Event data that triggered the execution",
    )

    workflow_correlation_id: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        index=True,
        doc="Correlation ID from workflow execution service",
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        doc="When the execution was created",
    )

    started_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, doc="When the execution started"
    )

    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, doc="When the execution completed"
    )

    # Error tracking
    error_message: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, doc="Error message if execution failed"
    )

    retry_count: Mapped[int] = mapped_column(
        Integer, nullable=False, default=0, doc="Number of retry attempts"
    )

    # Relationships
    trigger: Mapped["Trigger"] = relationship("Trigger", back_populates="executions")

    # Constraints and indexes
    __table_args__ = (
        Index("idx_trigger_executions_trigger_id", "trigger_id"),
        Index("idx_trigger_executions_status", "status"),
        Index("idx_trigger_executions_created_at", "created_at"),
        Index("idx_trigger_executions_correlation_id", "workflow_correlation_id"),
        Index("idx_trigger_executions_trigger_status", "trigger_id", "status"),
        CheckConstraint("retry_count >= 0", name="ck_executions_retry_count_positive"),
        CheckConstraint(
            "(status = 'pending' AND started_at IS NULL) OR "
            "(status != 'pending' AND started_at IS NOT NULL)",
            name="ck_executions_started_at_consistency",
        ),
        CheckConstraint(
            "(status IN ('completed', 'failed', 'cancelled') AND completed_at IS NOT NULL) OR "
            "(status NOT IN ('completed', 'failed', 'cancelled') AND completed_at IS NULL)",
            name="ck_executions_completed_at_consistency",
        ),
    )


class ScheduleFrequency(str, Enum):
    """Schedule frequency enumeration."""

    ONCE = "once"
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    YEARLY = "yearly"
    CUSTOM = "custom"


class Scheduler(Base):
    """
    Improved scheduler model with proper relationships and constraints.
    """

    __tablename__ = "schedulers"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        doc="Unique identifier for the scheduler",
    )

    # Foreign keys
    user_id: Mapped[str] = mapped_column(
        String(255),
        ForeignKey("users.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the user who owns this scheduler",
    )

    # Core scheduler information
    name: Mapped[str] = mapped_column(
        String(255), nullable=False, doc="Human-readable name for the scheduler"
    )

    description: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, doc="Optional description of the scheduler"
    )

    workflow_id: Mapped[str] = mapped_column(
        String(255), nullable=False, index=True, doc="ID of the workflow to execute"
    )

    # Schedule configuration
    frequency: Mapped[ScheduleFrequency] = mapped_column(
        SQLEnum(ScheduleFrequency), nullable=False, index=True, doc="Schedule frequency"
    )

    cron_expression: Mapped[Optional[str]] = mapped_column(
        String(255), nullable=True, doc="Cron expression for custom schedules"
    )

    timezone: Mapped[str] = mapped_column(
        String(50), nullable=False, default="UTC", doc="Timezone for schedule execution"
    )

    # Schedule timing
    time: Mapped[Optional[str]] = mapped_column(
        String(10), nullable=True, doc="Time of day for execution (HH:MM format)"
    )

    days_of_week: Mapped[Optional[List[int]]] = mapped_column(
        ARRAY(Integer),
        nullable=True,
        doc="Days of week for weekly schedules (0=Monday, 6=Sunday)",
    )

    days_of_month: Mapped[Optional[List[int]]] = mapped_column(
        ARRAY(Integer), nullable=True, doc="Days of month for monthly schedules (1-31)"
    )

    # Status and state
    is_active: Mapped[bool] = mapped_column(
        Boolean,
        nullable=False,
        default=True,
        index=True,
        doc="Whether the scheduler is active",
    )

    # Execution tracking
    next_run_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True),
        nullable=True,
        index=True,
        doc="When the scheduler should next run",
    )

    last_run_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, doc="When the scheduler last ran"
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        doc="When the scheduler was created",
    )

    updated_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        onupdate=func.now(),
        doc="When the scheduler was last updated",
    )

    # Error tracking
    error_count: Mapped[int] = mapped_column(
        Integer, nullable=False, default=0, doc="Number of consecutive errors"
    )

    last_error: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, doc="Last error message"
    )

    last_error_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, doc="When the last error occurred"
    )

    # Configuration
    input_values: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, nullable=True, default=dict, doc="Input values for workflow execution"
    )

    metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(
        JSON, nullable=True, default=dict, doc="Additional scheduler metadata"
    )

    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="schedulers")

    executions: Mapped[List["SchedulerExecution"]] = relationship(
        "SchedulerExecution",
        back_populates="scheduler",
        cascade="all, delete-orphan",
        order_by="SchedulerExecution.created_at.desc()",
    )

    # Constraints and indexes
    __table_args__ = (
        Index("idx_schedulers_user_id", "user_id"),
        Index("idx_schedulers_workflow_id", "workflow_id"),
        Index("idx_schedulers_frequency", "frequency"),
        Index("idx_schedulers_active", "is_active"),
        Index("idx_schedulers_next_run", "next_run_at"),
        Index("idx_schedulers_created_at", "created_at"),
        Index("idx_schedulers_user_active", "user_id", "is_active"),
        Index("idx_schedulers_active_next_run", "is_active", "next_run_at"),
        UniqueConstraint("user_id", "name", name="uq_schedulers_user_name"),
        CheckConstraint("error_count >= 0", name="ck_schedulers_error_count_positive"),
        CheckConstraint(
            "(frequency = 'custom' AND cron_expression IS NOT NULL) OR "
            "(frequency != 'custom')",
            name="ck_schedulers_cron_expression_required",
        ),
    )

    @validates("days_of_week")
    def validate_days_of_week(self, key, days_of_week):
        """Validate days of week."""
        if days_of_week is not None:
            for day in days_of_week:
                if not isinstance(day, int) or day < 0 or day > 6:
                    raise ValueError("Days of week must be integers between 0 and 6")
        return days_of_week

    @validates("days_of_month")
    def validate_days_of_month(self, key, days_of_month):
        """Validate days of month."""
        if days_of_month is not None:
            for day in days_of_month:
                if not isinstance(day, int) or day < 1 or day > 31:
                    raise ValueError("Days of month must be integers between 1 and 31")
        return days_of_month


class SchedulerExecution(Base):
    """
    Improved scheduler execution model with better tracking.
    """

    __tablename__ = "scheduler_executions"

    # Primary key
    id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        primary_key=True,
        default=uuid4,
        doc="Unique identifier for the execution",
    )

    # Foreign keys
    scheduler_id: Mapped[UUID] = mapped_column(
        PostgreSQLUUID(as_uuid=True),
        ForeignKey("schedulers.id", ondelete="CASCADE"),
        nullable=False,
        index=True,
        doc="ID of the scheduler that was executed",
    )

    # Execution details
    status: Mapped[ExecutionStatus] = mapped_column(
        SQLEnum(ExecutionStatus),
        nullable=False,
        default=ExecutionStatus.PENDING,
        index=True,
        doc="Current status of the execution",
    )

    # Workflow data
    workflow_correlation_id: Mapped[Optional[str]] = mapped_column(
        String(255),
        nullable=True,
        index=True,
        doc="Correlation ID from workflow execution service",
    )

    # Timestamps
    scheduled_for: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        nullable=False,
        index=True,
        doc="When this execution was scheduled to run",
    )

    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=func.now(),
        doc="When the execution was created",
    )

    started_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, doc="When the execution started"
    )

    completed_at: Mapped[Optional[datetime]] = mapped_column(
        DateTime(timezone=True), nullable=True, doc="When the execution completed"
    )

    # Error tracking
    error_message: Mapped[Optional[str]] = mapped_column(
        Text, nullable=True, doc="Error message if execution failed"
    )

    retry_count: Mapped[int] = mapped_column(
        Integer, nullable=False, default=0, doc="Number of retry attempts"
    )

    # Relationships
    scheduler: Mapped["Scheduler"] = relationship(
        "Scheduler", back_populates="executions"
    )

    # Constraints and indexes
    __table_args__ = (
        Index("idx_scheduler_executions_scheduler_id", "scheduler_id"),
        Index("idx_scheduler_executions_status", "status"),
        Index("idx_scheduler_executions_scheduled_for", "scheduled_for"),
        Index("idx_scheduler_executions_created_at", "created_at"),
        Index("idx_scheduler_executions_correlation_id", "workflow_correlation_id"),
        Index("idx_scheduler_executions_scheduler_status", "scheduler_id", "status"),
        CheckConstraint(
            "retry_count >= 0", name="ck_scheduler_executions_retry_count_positive"
        ),
    )
