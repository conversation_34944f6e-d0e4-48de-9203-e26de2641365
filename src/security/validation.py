"""
Input validation and sanitization for the Trigger Service.

This module provides comprehensive input validation, sanitization,
and security checks to protect against common vulnerabilities.
"""

import re
import html
import json
from typing import Any, Dict, List, Optional, Union
from datetime import datetime
from uuid import UUID

from pydantic import BaseModel, Field, validator
from fastapi import <PERSON><PERSON><PERSON><PERSON>x<PERSON>, status

from src.utils.logger import get_logger

logger = get_logger(__name__)


class ValidationError(Exception):
    """Custom validation error."""
    pass


class SecurityValidator:
    """
    Comprehensive security validator for input sanitization and validation.
    """
    
    # Common regex patterns
    PATTERNS = {
        "email": re.compile(r"^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$"),
        "uuid": re.compile(r"^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$", re.IGNORECASE),
        "alphanumeric": re.compile(r"^[a-zA-Z0-9_-]+$"),
        "safe_string": re.compile(r"^[a-zA-Z0-9\s\-_.@]+$"),
        "url": re.compile(r"^https?://[^\s/$.?#].[^\s]*$", re.IGNORECASE),
        "sql_injection": re.compile(r"(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)", re.IGNORECASE),
        "xss": re.compile(r"<script|javascript:|on\w+\s*=", re.IGNORECASE),
    }
    
    # Dangerous characters and strings
    DANGEROUS_CHARS = ["<", ">", "\"", "'", "&", ";", "(", ")", "|", "`", "$"]
    DANGEROUS_STRINGS = ["<script", "javascript:", "vbscript:", "onload=", "onerror="]
    
    @classmethod
    def sanitize_string(
        cls,
        value: str,
        max_length: int = 255,
        allow_html: bool = False,
        pattern: Optional[str] = None
    ) -> str:
        """
        Sanitize string input.
        
        Args:
            value: String to sanitize
            max_length: Maximum allowed length
            allow_html: Whether to allow HTML characters
            pattern: Regex pattern name for validation
            
        Returns:
            str: Sanitized string
            
        Raises:
            ValidationError: If validation fails
        """
        if not isinstance(value, str):
            value = str(value)
        
        # Check length
        if len(value) > max_length:
            raise ValidationError(f"String too long (max {max_length} characters)")
        
        # Remove null bytes and control characters
        value = ''.join(char for char in value if ord(char) >= 32 or char in '\t\n\r')
        
        # Check for SQL injection patterns
        if cls.PATTERNS["sql_injection"].search(value):
            raise ValidationError("Potential SQL injection detected")
        
        # Check for XSS patterns
        if cls.PATTERNS["xss"].search(value):
            raise ValidationError("Potential XSS attack detected")
        
        # HTML escape if not allowing HTML
        if not allow_html:
            value = html.escape(value)
        
        # Validate against pattern if provided
        if pattern and pattern in cls.PATTERNS:
            if not cls.PATTERNS[pattern].match(value):
                raise ValidationError(f"String does not match required pattern: {pattern}")
        
        return value.strip()
    
    @classmethod
    def validate_uuid(cls, value: Union[str, UUID]) -> UUID:
        """
        Validate UUID format.
        
        Args:
            value: UUID string or object
            
        Returns:
            UUID: Validated UUID object
            
        Raises:
            ValidationError: If UUID is invalid
        """
        if isinstance(value, UUID):
            return value
        
        if not isinstance(value, str):
            raise ValidationError("UUID must be a string or UUID object")
        
        try:
            return UUID(value)
        except ValueError:
            raise ValidationError("Invalid UUID format")
    
    @classmethod
    def validate_email(cls, email: str) -> str:
        """
        Validate email address.
        
        Args:
            email: Email address to validate
            
        Returns:
            str: Validated email address
            
        Raises:
            ValidationError: If email is invalid
        """
        email = cls.sanitize_string(email, max_length=254, pattern="email")
        
        if not cls.PATTERNS["email"].match(email):
            raise ValidationError("Invalid email format")
        
        return email.lower()
    
    @classmethod
    def validate_url(cls, url: str, allowed_schemes: List[str] = None) -> str:
        """
        Validate URL format and scheme.
        
        Args:
            url: URL to validate
            allowed_schemes: List of allowed schemes (default: ['http', 'https'])
            
        Returns:
            str: Validated URL
            
        Raises:
            ValidationError: If URL is invalid
        """
        if allowed_schemes is None:
            allowed_schemes = ['http', 'https']
        
        url = cls.sanitize_string(url, max_length=2048)
        
        if not cls.PATTERNS["url"].match(url):
            raise ValidationError("Invalid URL format")
        
        # Check scheme
        scheme = url.split('://')[0].lower()
        if scheme not in allowed_schemes:
            raise ValidationError(f"URL scheme not allowed. Allowed: {allowed_schemes}")
        
        return url
    
    @classmethod
    def validate_json(cls, json_str: str, max_size: int = 10240) -> Dict[str, Any]:
        """
        Validate and parse JSON string.
        
        Args:
            json_str: JSON string to validate
            max_size: Maximum JSON size in bytes
            
        Returns:
            Dict[str, Any]: Parsed JSON object
            
        Raises:
            ValidationError: If JSON is invalid
        """
        if len(json_str.encode('utf-8')) > max_size:
            raise ValidationError(f"JSON too large (max {max_size} bytes)")
        
        try:
            data = json.loads(json_str)
            if not isinstance(data, dict):
                raise ValidationError("JSON must be an object")
            return data
        except json.JSONDecodeError as e:
            raise ValidationError(f"Invalid JSON: {e}")
    
    @classmethod
    def validate_datetime(cls, dt_str: str) -> datetime:
        """
        Validate datetime string.
        
        Args:
            dt_str: Datetime string to validate
            
        Returns:
            datetime: Parsed datetime object
            
        Raises:
            ValidationError: If datetime is invalid
        """
        try:
            # Try ISO format first
            return datetime.fromisoformat(dt_str.replace('Z', '+00:00'))
        except ValueError:
            try:
                # Try common formats
                for fmt in ['%Y-%m-%d %H:%M:%S', '%Y-%m-%d', '%Y-%m-%dT%H:%M:%S']:
                    try:
                        return datetime.strptime(dt_str, fmt)
                    except ValueError:
                        continue
                raise ValueError("No matching format found")
            except ValueError:
                raise ValidationError("Invalid datetime format")
    
    @classmethod
    def validate_config_object(cls, config: Dict[str, Any], max_depth: int = 5) -> Dict[str, Any]:
        """
        Validate configuration object for security.
        
        Args:
            config: Configuration object to validate
            max_depth: Maximum nesting depth
            
        Returns:
            Dict[str, Any]: Validated configuration
            
        Raises:
            ValidationError: If configuration is invalid
        """
        def validate_recursive(obj: Any, depth: int = 0) -> Any:
            if depth > max_depth:
                raise ValidationError(f"Configuration nesting too deep (max {max_depth})")
            
            if isinstance(obj, dict):
                validated = {}
                for key, value in obj.items():
                    # Validate key
                    if not isinstance(key, str):
                        raise ValidationError("Configuration keys must be strings")
                    
                    key = cls.sanitize_string(key, max_length=100, pattern="safe_string")
                    validated[key] = validate_recursive(value, depth + 1)
                
                return validated
            
            elif isinstance(obj, list):
                if len(obj) > 100:  # Limit array size
                    raise ValidationError("Configuration arrays too large (max 100 items)")
                
                return [validate_recursive(item, depth + 1) for item in obj]
            
            elif isinstance(obj, str):
                return cls.sanitize_string(obj, max_length=1000)
            
            elif isinstance(obj, (int, float, bool)) or obj is None:
                return obj
            
            else:
                raise ValidationError(f"Unsupported configuration value type: {type(obj)}")
        
        return validate_recursive(config)


class SecureRequestValidator(BaseModel):
    """Base validator for secure API requests."""
    
    class Config:
        """Pydantic configuration."""
        str_strip_whitespace = True
        validate_assignment = True
        max_anystr_length = 10000
    
    @validator('*', pre=True)
    def sanitize_strings(cls, v):
        """Sanitize all string inputs."""
        if isinstance(v, str):
            return SecurityValidator.sanitize_string(v)
        return v


class TriggerCreateValidator(SecureRequestValidator):
    """Validator for trigger creation requests."""
    
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    trigger_type: str = Field(..., min_length=1, max_length=50)
    workflow_id: str = Field(..., min_length=1, max_length=255)
    event_types: List[str] = Field(..., min_items=1, max_items=10)
    config: Dict[str, Any] = Field(default_factory=dict)
    is_active: bool = Field(default=True)
    
    @validator('name')
    def validate_name(cls, v):
        """Validate trigger name."""
        return SecurityValidator.sanitize_string(v, pattern="safe_string")
    
    @validator('trigger_type')
    def validate_trigger_type(cls, v):
        """Validate trigger type."""
        return SecurityValidator.sanitize_string(v, pattern="alphanumeric")
    
    @validator('workflow_id')
    def validate_workflow_id(cls, v):
        """Validate workflow ID."""
        return SecurityValidator.sanitize_string(v, pattern="safe_string")
    
    @validator('event_types')
    def validate_event_types(cls, v):
        """Validate event types."""
        validated = []
        for event_type in v:
            validated.append(SecurityValidator.sanitize_string(event_type, pattern="alphanumeric"))
        return validated
    
    @validator('config')
    def validate_config(cls, v):
        """Validate configuration object."""
        return SecurityValidator.validate_config_object(v)


class SchedulerCreateValidator(SecureRequestValidator):
    """Validator for scheduler creation requests."""
    
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = Field(None, max_length=1000)
    workflow_id: str = Field(..., min_length=1, max_length=255)
    schedule: Dict[str, Any] = Field(...)
    input_values: Optional[Dict[str, Any]] = Field(default_factory=dict)
    is_active: bool = Field(default=True)
    
    @validator('name')
    def validate_name(cls, v):
        """Validate scheduler name."""
        return SecurityValidator.sanitize_string(v, pattern="safe_string")
    
    @validator('workflow_id')
    def validate_workflow_id(cls, v):
        """Validate workflow ID."""
        return SecurityValidator.sanitize_string(v, pattern="safe_string")
    
    @validator('schedule')
    def validate_schedule(cls, v):
        """Validate schedule configuration."""
        return SecurityValidator.validate_config_object(v, max_depth=3)
    
    @validator('input_values')
    def validate_input_values(cls, v):
        """Validate input values."""
        if v:
            return SecurityValidator.validate_config_object(v, max_depth=5)
        return v


def validate_user_id(user_id: str) -> str:
    """
    Validate user ID parameter.
    
    Args:
        user_id: User ID to validate
        
    Returns:
        str: Validated user ID
        
    Raises:
        HTTPException: If validation fails
    """
    try:
        return SecurityValidator.sanitize_string(
            user_id,
            max_length=100,
            pattern="safe_string"
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"Invalid user ID: {e}"
        )


def validate_uuid_param(uuid_str: str, param_name: str = "ID") -> UUID:
    """
    Validate UUID parameter.
    
    Args:
        uuid_str: UUID string to validate
        param_name: Parameter name for error messages
        
    Returns:
        UUID: Validated UUID
        
    Raises:
        HTTPException: If validation fails
    """
    try:
        return SecurityValidator.validate_uuid(uuid_str)
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_422_UNPROCESSABLE_ENTITY,
            detail=f"Invalid {param_name}: {e}"
        )


def validate_webhook_payload(
    headers: Dict[str, str],
    body: bytes,
    max_size: int = 1024 * 1024  # 1MB
) -> Dict[str, Any]:
    """
    Validate webhook payload.
    
    Args:
        headers: Request headers
        body: Request body
        max_size: Maximum payload size
        
    Returns:
        Dict[str, Any]: Validated payload data
        
    Raises:
        HTTPException: If validation fails
    """
    # Check payload size
    if len(body) > max_size:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"Payload too large (max {max_size} bytes)"
        )
    
    # Validate content type
    content_type = headers.get("content-type", "").lower()
    if not content_type.startswith("application/json"):
        raise HTTPException(
            status_code=status.HTTP_415_UNSUPPORTED_MEDIA_TYPE,
            detail="Content-Type must be application/json"
        )
    
    # Parse and validate JSON
    try:
        payload_str = body.decode('utf-8')
        return SecurityValidator.validate_json(payload_str, max_size)
    except UnicodeDecodeError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid UTF-8 encoding"
        )
    except ValidationError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Invalid JSON payload: {e}"
        )
