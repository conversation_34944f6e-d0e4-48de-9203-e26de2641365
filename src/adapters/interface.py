"""
Extensible adapter interface for the Trigger Service.

This module defines a comprehensive interface for creating new integrations
with proper abstractions, plugin architecture, and standardized patterns.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, List, Optional, Set, Type, Union
from enum import Enum
from uuid import UUID
from datetime import datetime

from pydantic import BaseModel, Field

from src.adapters.base import (
    BaseTriggerAdapter,
    TriggerEvent,
    TriggerEventType,
    TriggerConfiguration,
    AdapterHealthStatus,
)
from src.utils.logger import get_logger

logger = get_logger(__name__)


class AdapterCapability(str, Enum):
    """Adapter capability enumeration."""
    WEBHOOKS = "webhooks"
    POLLING = "polling"
    REAL_TIME = "real_time"
    BATCH_PROCESSING = "batch_processing"
    OAUTH = "oauth"
    API_KEY = "api_key"
    CUSTOM_AUTH = "custom_auth"
    FILE_UPLOAD = "file_upload"
    RATE_LIMITED = "rate_limited"
    PAGINATION = "pagination"


class AdapterCategory(str, Enum):
    """Adapter category enumeration."""
    CALENDAR = "calendar"
    EMAIL = "email"
    MESSAGING = "messaging"
    FILE_STORAGE = "file_storage"
    DATABASE = "database"
    CRM = "crm"
    PROJECT_MANAGEMENT = "project_management"
    SOCIAL_MEDIA = "social_media"
    ANALYTICS = "analytics"
    NOTIFICATION = "notification"
    CUSTOM = "custom"


class AdapterMetadata(BaseModel):
    """Metadata for adapter registration and discovery."""
    
    name: str = Field(description="Adapter name")
    display_name: str = Field(description="Human-readable display name")
    description: str = Field(description="Adapter description")
    version: str = Field(description="Adapter version")
    category: AdapterCategory = Field(description="Adapter category")
    capabilities: Set[AdapterCapability] = Field(description="Adapter capabilities")
    supported_event_types: Set[TriggerEventType] = Field(description="Supported event types")
    
    # Configuration schema
    config_schema: Dict[str, Any] = Field(description="JSON schema for configuration")
    
    # Documentation
    documentation_url: Optional[str] = Field(default=None, description="Documentation URL")
    examples: List[Dict[str, Any]] = Field(default_factory=list, description="Configuration examples")
    
    # Requirements
    required_permissions: List[str] = Field(default_factory=list, description="Required permissions")
    rate_limits: Optional[Dict[str, Any]] = Field(default=None, description="Rate limit information")
    
    # Compatibility
    min_service_version: Optional[str] = Field(default=None, description="Minimum service version")
    deprecated: bool = Field(default=False, description="Whether adapter is deprecated")
    
    class Config:
        """Pydantic configuration."""
        use_enum_values = True


class IAdapterPlugin(ABC):
    """
    Interface for adapter plugins.
    
    This interface defines the contract that all adapter plugins must implement
    to be compatible with the plugin system.
    """
    
    @property
    @abstractmethod
    def metadata(self) -> AdapterMetadata:
        """
        Get adapter metadata.
        
        Returns:
            AdapterMetadata: Adapter metadata
        """
        pass
    
    @abstractmethod
    async def create_adapter(self, **kwargs) -> BaseTriggerAdapter:
        """
        Create an adapter instance.
        
        Args:
            **kwargs: Adapter configuration parameters
            
        Returns:
            BaseTriggerAdapter: Adapter instance
        """
        pass
    
    @abstractmethod
    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """
        Validate adapter configuration.
        
        Args:
            config: Configuration to validate
            
        Returns:
            bool: True if configuration is valid
        """
        pass
    
    @abstractmethod
    def get_configuration_schema(self) -> Dict[str, Any]:
        """
        Get JSON schema for adapter configuration.
        
        Returns:
            Dict[str, Any]: JSON schema
        """
        pass
    
    @abstractmethod
    def get_sample_configuration(self) -> Dict[str, Any]:
        """
        Get sample configuration for the adapter.
        
        Returns:
            Dict[str, Any]: Sample configuration
        """
        pass
    
    async def initialize(self) -> None:
        """
        Initialize the adapter plugin.
        
        This method is called when the plugin is loaded and can be used
        for any setup operations.
        """
        pass
    
    async def cleanup(self) -> None:
        """
        Cleanup the adapter plugin.
        
        This method is called when the plugin is unloaded and should
        clean up any resources.
        """
        pass
    
    def get_health_check_info(self) -> Dict[str, Any]:
        """
        Get health check information for the adapter.
        
        Returns:
            Dict[str, Any]: Health check information
        """
        return {
            "supports_health_check": False,
            "health_check_interval": None,
            "health_check_timeout": None,
        }


class IAuthenticationHandler(ABC):
    """
    Interface for authentication handlers.
    
    This interface defines how adapters should handle authentication
    with external services.
    """
    
    @abstractmethod
    async def authenticate(self, user_id: str, credentials: Dict[str, Any]) -> bool:
        """
        Authenticate a user with the external service.
        
        Args:
            user_id: User ID
            credentials: Authentication credentials
            
        Returns:
            bool: True if authentication successful
        """
        pass
    
    @abstractmethod
    async def refresh_credentials(self, user_id: str) -> bool:
        """
        Refresh user credentials if needed.
        
        Args:
            user_id: User ID
            
        Returns:
            bool: True if refresh successful
        """
        pass
    
    @abstractmethod
    async def validate_credentials(self, user_id: str) -> bool:
        """
        Validate user credentials.
        
        Args:
            user_id: User ID
            
        Returns:
            bool: True if credentials are valid
        """
        pass
    
    @abstractmethod
    async def revoke_credentials(self, user_id: str) -> bool:
        """
        Revoke user credentials.
        
        Args:
            user_id: User ID
            
        Returns:
            bool: True if revocation successful
        """
        pass


class IWebhookHandler(ABC):
    """
    Interface for webhook handlers.
    
    This interface defines how adapters should handle webhook
    subscriptions and events.
    """
    
    @abstractmethod
    async def create_webhook(
        self,
        user_id: str,
        trigger_id: UUID,
        config: Dict[str, Any]
    ) -> Optional[str]:
        """
        Create a webhook subscription.
        
        Args:
            user_id: User ID
            trigger_id: Trigger ID
            config: Webhook configuration
            
        Returns:
            Optional[str]: Webhook ID if successful
        """
        pass
    
    @abstractmethod
    async def delete_webhook(self, webhook_id: str) -> bool:
        """
        Delete a webhook subscription.
        
        Args:
            webhook_id: Webhook ID
            
        Returns:
            bool: True if deletion successful
        """
        pass
    
    @abstractmethod
    async def process_webhook_event(
        self,
        webhook_id: str,
        headers: Dict[str, str],
        body: Any
    ) -> Optional[TriggerEvent]:
        """
        Process a webhook event.
        
        Args:
            webhook_id: Webhook ID
            headers: Request headers
            body: Request body
            
        Returns:
            Optional[TriggerEvent]: Processed trigger event
        """
        pass
    
    @abstractmethod
    def validate_webhook_signature(
        self,
        headers: Dict[str, str],
        body: bytes,
        secret: str
    ) -> bool:
        """
        Validate webhook signature.
        
        Args:
            headers: Request headers
            body: Request body
            secret: Webhook secret
            
        Returns:
            bool: True if signature is valid
        """
        pass


class IPollingHandler(ABC):
    """
    Interface for polling handlers.
    
    This interface defines how adapters should handle polling
    for events from external services.
    """
    
    @abstractmethod
    async def start_polling(
        self,
        user_id: str,
        trigger_id: UUID,
        config: Dict[str, Any]
    ) -> bool:
        """
        Start polling for events.
        
        Args:
            user_id: User ID
            trigger_id: Trigger ID
            config: Polling configuration
            
        Returns:
            bool: True if polling started successfully
        """
        pass
    
    @abstractmethod
    async def stop_polling(self, trigger_id: UUID) -> bool:
        """
        Stop polling for events.
        
        Args:
            trigger_id: Trigger ID
            
        Returns:
            bool: True if polling stopped successfully
        """
        pass
    
    @abstractmethod
    async def poll_events(
        self,
        user_id: str,
        config: Dict[str, Any]
    ) -> List[TriggerEvent]:
        """
        Poll for events from the external service.
        
        Args:
            user_id: User ID
            config: Polling configuration
            
        Returns:
            List[TriggerEvent]: List of events found
        """
        pass
    
    @abstractmethod
    def get_polling_interval(self, config: Dict[str, Any]) -> int:
        """
        Get polling interval in seconds.
        
        Args:
            config: Polling configuration
            
        Returns:
            int: Polling interval in seconds
        """
        pass


class ExtensibleAdapter(BaseTriggerAdapter):
    """
    Base class for extensible adapters.
    
    This class provides a foundation for building adapters with
    the extensible interface and plugin architecture.
    """
    
    def __init__(
        self,
        adapter_name: str,
        metadata: AdapterMetadata,
        auth_handler: Optional[IAuthenticationHandler] = None,
        webhook_handler: Optional[IWebhookHandler] = None,
        polling_handler: Optional[IPollingHandler] = None,
    ):
        """
        Initialize the extensible adapter.
        
        Args:
            adapter_name: Adapter name
            metadata: Adapter metadata
            auth_handler: Optional authentication handler
            webhook_handler: Optional webhook handler
            polling_handler: Optional polling handler
        """
        super().__init__(adapter_name)
        self.metadata = metadata
        self.auth_handler = auth_handler
        self.webhook_handler = webhook_handler
        self.polling_handler = polling_handler
        
        logger.info(f"Initialized extensible adapter: {adapter_name}")
    
    @property
    def supported_event_types(self) -> Set[TriggerEventType]:
        """Get supported event types from metadata."""
        return self.metadata.supported_event_types
    
    @property
    def capabilities(self) -> Set[AdapterCapability]:
        """Get adapter capabilities from metadata."""
        return self.metadata.capabilities
    
    async def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        Validate configuration using the metadata schema.
        
        Args:
            config: Configuration to validate
            
        Returns:
            bool: True if configuration is valid
        """
        try:
            from jsonschema import validate
            validate(config, self.metadata.config_schema)
            return True
        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False
    
    def get_configuration_schema(self) -> Dict[str, Any]:
        """Get configuration schema from metadata."""
        return self.metadata.config_schema
    
    def get_sample_event_data(self) -> Dict[str, Any]:
        """Get sample event data from metadata examples."""
        if self.metadata.examples:
            return self.metadata.examples[0]
        return {}
    
    def get_available_fields(self) -> List[Dict[str, str]]:
        """Get available fields from metadata."""
        # This could be enhanced to extract fields from schema
        return []
    
    async def setup_trigger(
        self,
        trigger_config: TriggerConfiguration,
        session=None
    ) -> bool:
        """
        Set up trigger using appropriate handler.
        
        Args:
            trigger_config: Trigger configuration
            session: Optional database session
            
        Returns:
            bool: True if setup successful
        """
        try:
            # Use webhook handler if available and webhooks are supported
            if (self.webhook_handler and 
                AdapterCapability.WEBHOOKS in self.capabilities):
                webhook_id = await self.webhook_handler.create_webhook(
                    user_id=trigger_config.user_id,
                    trigger_id=trigger_config.trigger_id,
                    config=trigger_config.config
                )
                return webhook_id is not None
            
            # Use polling handler if available and polling is supported
            elif (self.polling_handler and 
                  AdapterCapability.POLLING in self.capabilities):
                return await self.polling_handler.start_polling(
                    user_id=trigger_config.user_id,
                    trigger_id=trigger_config.trigger_id,
                    config=trigger_config.config
                )
            
            else:
                logger.error(f"No suitable handler for adapter {self.adapter_name}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to setup trigger: {e}")
            return False
    
    async def remove_trigger(self, trigger_id: UUID) -> bool:
        """
        Remove trigger using appropriate handler.
        
        Args:
            trigger_id: Trigger ID
            
        Returns:
            bool: True if removal successful
        """
        try:
            # Try webhook handler first
            if self.webhook_handler:
                # This would need to be enhanced to track webhook IDs
                pass
            
            # Try polling handler
            if self.polling_handler:
                return await self.polling_handler.stop_polling(trigger_id)
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to remove trigger: {e}")
            return False
