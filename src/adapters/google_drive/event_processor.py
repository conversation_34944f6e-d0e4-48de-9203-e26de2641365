"""
Google Drive event processor.

This module processes Google Drive webhook events and converts them
into standardized trigger events for the workflow system.
"""

from typing import Dict, Any, List, Optional
from datetime import datetime, timezone
from uuid import uuid4

from src.adapters.base import TriggerEvent, TriggerEventType
from src.adapters.google_drive.api_client import GoogleDriveAPIClient
from src.utils.logger import get_logger
from src.core.exceptions import ValidationError

logger = get_logger(__name__)


class GoogleDriveEventProcessor:
    """
    Processes Google Drive webhook events and converts them to trigger events.
    
    This class handles the conversion of Google Drive change notifications
    into standardized trigger events that can be processed by the workflow system.
    """
    
    def __init__(self, api_client: GoogleDriveAPIClient):
        """
        Initialize the event processor.
        
        Args:
            api_client: Google Drive API client
        """
        self.api_client = api_client
        
        # Event type mapping
        self.event_type_mapping = {
            'file_created': TriggerEventType.CREATED,
            'file_updated': TriggerEventType.UPDATED,
            'file_deleted': TriggerEventType.DELETED,
            'file_moved': TriggerEventType.UPDATED,
            'file_renamed': TriggerEventType.UPDATED,
        }
        
        logger.debug("Google Drive event processor initialized")
    
    async def process_webhook_event(
        self,
        headers: Dict[str, str],
        channel_id: str,
        resource_id: str,
        resource_state: str,
        page_token: Optional[str] = None
    ) -> List[TriggerEvent]:
        """
        Process a Google Drive webhook event.
        
        Args:
            headers: Webhook headers
            channel_id: Channel ID from webhook
            resource_id: Resource ID from webhook
            resource_state: Resource state (sync, update, etc.)
            page_token: Page token for change tracking
            
        Returns:
            List[TriggerEvent]: List of processed trigger events
            
        Raises:
            ValidationError: If event validation fails
        """
        try:
            events = []
            
            # Skip sync events (initial setup)
            if resource_state == 'sync':
                logger.debug(f"Skipping sync event for channel {channel_id}")
                return events
            
            # Get changes from Google Drive API
            if page_token:
                changes_response = await self.api_client.get_changes(page_token)
                changes = changes_response.get('changes', [])
                
                for change in changes:
                    event = await self._process_change(change, channel_id)
                    if event:
                        events.append(event)
            
            logger.info(f"Processed {len(events)} events from webhook {channel_id}")
            return events
            
        except Exception as e:
            logger.error(f"Failed to process webhook event: {e}")
            raise ValidationError(f"Failed to process webhook event: {e}")
    
    async def _process_change(self, change: Dict[str, Any], channel_id: str) -> Optional[TriggerEvent]:
        """
        Process a single file change.
        
        Args:
            change: Change data from Google Drive API
            channel_id: Channel ID for tracking
            
        Returns:
            Optional[TriggerEvent]: Processed trigger event or None
        """
        try:
            file_id = change.get('fileId')
            if not file_id:
                logger.warning("Change missing file ID")
                return None
            
            # Check if file was removed
            if change.get('removed', False):
                return self._create_delete_event(file_id, channel_id)
            
            # Get file details
            file_data = change.get('file')
            if not file_data:
                # File data not included, fetch it
                file_data = await self.api_client.get_file(file_id)
                if not file_data:
                    logger.warning(f"Could not retrieve file data for {file_id}")
                    return None
            
            # Determine event type
            event_type = self._determine_event_type(file_data, change)
            
            # Create trigger event
            return self._create_trigger_event(
                event_type=event_type,
                file_data=file_data,
                channel_id=channel_id,
                change_data=change
            )
            
        except Exception as e:
            logger.error(f"Failed to process change: {e}")
            return None
    
    def _determine_event_type(self, file_data: Dict[str, Any], change: Dict[str, Any]) -> TriggerEventType:
        """
        Determine the event type based on file data and change information.
        
        Args:
            file_data: File metadata
            change: Change data
            
        Returns:
            TriggerEventType: Determined event type
        """
        # For now, we'll treat all non-delete changes as updates
        # In a more sophisticated implementation, we could track
        # creation times, previous states, etc.
        return TriggerEventType.UPDATED
    
    def _create_trigger_event(
        self,
        event_type: TriggerEventType,
        file_data: Dict[str, Any],
        channel_id: str,
        change_data: Dict[str, Any]
    ) -> TriggerEvent:
        """
        Create a trigger event from file data.
        
        Args:
            event_type: Type of event
            file_data: File metadata
            channel_id: Channel ID
            change_data: Change data
            
        Returns:
            TriggerEvent: Created trigger event
        """
        file_id = file_data.get('id')
        file_name = file_data.get('name', 'Unknown')
        
        # Parse modification time
        modified_time = file_data.get('modifiedTime')
        timestamp = datetime.now(timezone.utc)
        if modified_time:
            try:
                timestamp = datetime.fromisoformat(modified_time.replace('Z', '+00:00'))
            except ValueError:
                pass
        
        # Create event data
        event_data = {
            'file_id': file_id,
            'file_name': file_name,
            'mime_type': file_data.get('mimeType'),
            'size': file_data.get('size'),
            'modified_time': modified_time,
            'web_view_link': file_data.get('webViewLink'),
            'web_content_link': file_data.get('webContentLink'),
            'parents': file_data.get('parents', []),
            'owners': file_data.get('owners', []),
            'last_modifying_user': file_data.get('lastModifyingUser', {}),
            'channel_id': channel_id,
            'change_type': change_data.get('type', 'file'),
        }
        
        # Create trigger event
        return TriggerEvent(
            event_id=f"gdrive-{file_id}-{int(timestamp.timestamp())}",
            event_type=event_type,
            source="google_drive",
            timestamp=timestamp,
            data=event_data,
            metadata={
                'channel_id': channel_id,
                'file_id': file_id,
                'file_name': file_name,
                'mime_type': file_data.get('mimeType'),
            }
        )
    
    def _create_delete_event(self, file_id: str, channel_id: str) -> TriggerEvent:
        """
        Create a delete event for a removed file.
        
        Args:
            file_id: ID of deleted file
            channel_id: Channel ID
            
        Returns:
            TriggerEvent: Delete trigger event
        """
        timestamp = datetime.now(timezone.utc)
        
        event_data = {
            'file_id': file_id,
            'file_name': 'Unknown (deleted)',
            'channel_id': channel_id,
            'change_type': 'file',
            'removed': True,
        }
        
        return TriggerEvent(
            event_id=f"gdrive-{file_id}-deleted-{int(timestamp.timestamp())}",
            event_type=TriggerEventType.DELETED,
            source="google_drive",
            timestamp=timestamp,
            data=event_data,
            metadata={
                'channel_id': channel_id,
                'file_id': file_id,
                'removed': True,
            }
        )
    
    def validate_webhook_headers(self, headers: Dict[str, str]) -> bool:
        """
        Validate webhook headers from Google Drive.
        
        Args:
            headers: Webhook headers
            
        Returns:
            bool: True if headers are valid
        """
        required_headers = [
            'x-goog-channel-id',
            'x-goog-resource-id',
            'x-goog-resource-state',
        ]
        
        for header in required_headers:
            if header not in headers:
                logger.warning(f"Missing required header: {header}")
                return False
        
        return True
    
    def extract_webhook_data(self, headers: Dict[str, str]) -> Dict[str, str]:
        """
        Extract webhook data from headers.
        
        Args:
            headers: Webhook headers
            
        Returns:
            Dict[str, str]: Extracted webhook data
        """
        return {
            'channel_id': headers.get('x-goog-channel-id'),
            'resource_id': headers.get('x-goog-resource-id'),
            'resource_state': headers.get('x-goog-resource-state'),
            'resource_uri': headers.get('x-goog-resource-uri'),
            'message_number': headers.get('x-goog-message-number'),
            'changed': headers.get('x-goog-changed'),
        }
    
    def get_supported_event_types(self) -> List[TriggerEventType]:
        """
        Get list of supported event types.
        
        Returns:
            List[TriggerEventType]: Supported event types
        """
        return list(self.event_type_mapping.values())
    
    def get_sample_event_data(self) -> Dict[str, Any]:
        """
        Get sample event data for documentation/testing.
        
        Returns:
            Dict[str, Any]: Sample event data
        """
        return {
            'file_id': '1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms',
            'file_name': 'Example Spreadsheet',
            'mime_type': 'application/vnd.google-apps.spreadsheet',
            'size': '12345',
            'modified_time': '2024-01-01T12:00:00.000Z',
            'web_view_link': 'https://docs.google.com/spreadsheets/d/1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms/edit',
            'parents': ['0BwwA4oUTeiV1TGRPeTVjaWRDY1E'],
            'owners': [{'displayName': 'John Doe', 'emailAddress': '<EMAIL>'}],
            'last_modifying_user': {'displayName': 'Jane Smith', 'emailAddress': '<EMAIL>'},
            'channel_id': 'channel-123',
            'change_type': 'file',
        }
