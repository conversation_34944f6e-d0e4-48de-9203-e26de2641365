"""
Google Drive authentication handler.

This module handles OAuth2 authentication and credential management
for Google Drive API access.
"""

import json
from typing import Dict, Any, Optional
from datetime import datetime, timezone

from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google.oauth2.service_account import Credentials as ServiceAccountCredentials
from google_auth_oauthlib.flow import Flow
from googleapiclient.discovery import build

from src.utils.logger import get_logger
from src.utils.config import get_settings
from src.core.exceptions import AuthenticationError, ValidationError

logger = get_logger(__name__)


class GoogleDriveAuthHandler:
    """
    Handles Google Drive OAuth2 authentication and credential management.

    This class manages the OAuth2 flow, token refresh, and credential
    validation for Google Drive API access.
    """

    def __init__(self):
        """Initialize the auth handler."""
        self.settings = get_settings()
        self.scopes = [
            "https://www.googleapis.com/auth/drive.readonly",
            "https://www.googleapis.com/auth/drive.metadata.readonly",
        ]

        # OAuth2 configuration
        self.client_config = {
            "web": {
                "client_id": self.settings.google_client_id,
                "client_secret": self.settings.google_client_secret,
                "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                "token_uri": "https://oauth2.googleapis.com/token",
                "redirect_uris": [self.settings.google_redirect_uri],
            }
        }

        logger.info("Google Drive auth handler initialized")

    def create_authorization_url(
        self, user_id: str, state: Optional[str] = None
    ) -> str:
        """
        Create OAuth2 authorization URL.

        Args:
            user_id: User ID for state tracking
            state: Optional state parameter

        Returns:
            str: Authorization URL

        Raises:
            AuthenticationError: If URL creation fails
        """
        try:
            flow = Flow.from_client_config(
                self.client_config,
                scopes=self.scopes,
                redirect_uri=self.settings.google_redirect_uri,
            )

            # Include user_id in state for tracking
            auth_state = json.dumps(
                {
                    "user_id": user_id,
                    "custom_state": state,
                    "timestamp": datetime.now(timezone.utc).isoformat(),
                }
            )

            authorization_url, _ = flow.authorization_url(
                access_type="offline",
                include_granted_scopes="true",
                state=auth_state,
                prompt="consent",  # Force consent to get refresh token
            )

            logger.info(f"Created authorization URL for user {user_id}")
            return authorization_url

        except Exception as e:
            logger.error(f"Failed to create authorization URL: {e}")
            raise AuthenticationError(f"Failed to create authorization URL: {e}")

    def exchange_code_for_tokens(
        self, authorization_code: str, state: str
    ) -> Dict[str, Any]:
        """
        Exchange authorization code for access tokens.

        Args:
            authorization_code: Authorization code from OAuth callback
            state: State parameter from OAuth callback

        Returns:
            Dict[str, Any]: Token information and user details

        Raises:
            AuthenticationError: If token exchange fails
            ValidationError: If state validation fails
        """
        try:
            # Validate and parse state
            try:
                state_data = json.loads(state)
                user_id = state_data.get("user_id")
                if not user_id:
                    raise ValidationError("Invalid state: missing user_id")
            except (json.JSONDecodeError, KeyError) as e:
                raise ValidationError(f"Invalid state parameter: {e}")

            # Create flow and exchange code
            flow = Flow.from_client_config(
                self.client_config,
                scopes=self.scopes,
                redirect_uri=self.settings.google_redirect_uri,
                state=state,
            )

            flow.fetch_token(code=authorization_code)
            credentials = flow.credentials

            # Get user info
            user_info = self._get_user_info(credentials)

            # Prepare token data
            token_data = {
                "user_id": user_id,
                "access_token": credentials.token,
                "refresh_token": credentials.refresh_token,
                "token_uri": credentials.token_uri,
                "client_id": credentials.client_id,
                "client_secret": credentials.client_secret,
                "scopes": credentials.scopes,
                "expiry": (
                    credentials.expiry.isoformat() if credentials.expiry else None
                ),
                "user_info": user_info,
                "created_at": datetime.now(timezone.utc).isoformat(),
            }

            logger.info(f"Successfully exchanged code for tokens for user {user_id}")
            return token_data

        except ValidationError:
            raise
        except Exception as e:
            logger.error(f"Failed to exchange code for tokens: {e}")
            raise AuthenticationError(f"Failed to exchange authorization code: {e}")

    def refresh_credentials(self, token_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Refresh expired credentials.

        Args:
            token_data: Existing token data

        Returns:
            Dict[str, Any]: Updated token data

        Raises:
            AuthenticationError: If refresh fails
        """
        try:
            # Create credentials from token data
            credentials = Credentials(
                token=token_data.get("access_token"),
                refresh_token=token_data.get("refresh_token"),
                token_uri=token_data.get("token_uri"),
                client_id=token_data.get("client_id"),
                client_secret=token_data.get("client_secret"),
                scopes=token_data.get("scopes"),
            )

            # Refresh the credentials
            credentials.refresh(Request())

            # Update token data
            updated_token_data = token_data.copy()
            updated_token_data.update(
                {
                    "access_token": credentials.token,
                    "expiry": (
                        credentials.expiry.isoformat() if credentials.expiry else None
                    ),
                    "refreshed_at": datetime.now(timezone.utc).isoformat(),
                }
            )

            logger.info(
                f"Successfully refreshed credentials for user {token_data.get('user_id')}"
            )
            return updated_token_data

        except Exception as e:
            logger.error(f"Failed to refresh credentials: {e}")
            raise AuthenticationError(f"Failed to refresh credentials: {e}")

    def validate_credentials(self, token_data: Dict[str, Any]) -> bool:
        """
        Validate credentials by making a test API call.

        Args:
            token_data: Token data to validate

        Returns:
            bool: True if credentials are valid
        """
        try:
            credentials = self._create_credentials_from_token_data(token_data)

            # Test credentials with a simple API call
            service = build("drive", "v3", credentials=credentials)
            service.about().get(fields="user").execute()

            logger.debug(f"Credentials validated for user {token_data.get('user_id')}")
            return True

        except Exception as e:
            logger.warning(f"Credential validation failed: {e}")
            return False

    def revoke_credentials(self, token_data: Dict[str, Any]) -> bool:
        """
        Revoke credentials.

        Args:
            token_data: Token data to revoke

        Returns:
            bool: True if revocation successful
        """
        try:
            credentials = self._create_credentials_from_token_data(token_data)

            # Revoke the credentials
            revoke_url = (
                f"https://oauth2.googleapis.com/revoke?token={credentials.token}"
            )
            request = Request()
            response = request(method="POST", url=revoke_url)

            success = response.status == 200
            if success:
                logger.info(
                    f"Successfully revoked credentials for user {token_data.get('user_id')}"
                )
            else:
                logger.warning(f"Failed to revoke credentials: HTTP {response.status}")

            return success

        except Exception as e:
            logger.error(f"Failed to revoke credentials: {e}")
            return False

    def _create_credentials_from_token_data(
        self, token_data: Dict[str, Any]
    ) -> Credentials:
        """Create Credentials object from token data."""
        expiry = None
        if token_data.get("expiry"):
            try:
                expiry = datetime.fromisoformat(
                    token_data["expiry"].replace("Z", "+00:00")
                )
            except ValueError:
                pass

        return Credentials(
            token=token_data.get("access_token"),
            refresh_token=token_data.get("refresh_token"),
            token_uri=token_data.get("token_uri"),
            client_id=token_data.get("client_id"),
            client_secret=token_data.get("client_secret"),
            scopes=token_data.get("scopes"),
            expiry=expiry,
        )

    def _get_user_info(self, credentials: Credentials) -> Dict[str, Any]:
        """Get user information from Google API."""
        try:
            service = build("drive", "v3", credentials=credentials)
            about = service.about().get(fields="user").execute()

            user = about.get("user", {})
            return {
                "email": user.get("emailAddress"),
                "name": user.get("displayName"),
                "photo_link": user.get("photoLink"),
                "permission_id": user.get("permissionId"),
            }

        except Exception as e:
            logger.warning(f"Failed to get user info: {e}")
            return {}

    def get_required_scopes(self) -> list:
        """Get list of required OAuth2 scopes."""
        return self.scopes.copy()

    def is_token_expired(self, token_data: Dict[str, Any]) -> bool:
        """
        Check if token is expired.

        Args:
            token_data: Token data to check

        Returns:
            bool: True if token is expired
        """
        expiry_str = token_data.get("expiry")
        if not expiry_str:
            return False

        try:
            expiry = datetime.fromisoformat(expiry_str.replace("Z", "+00:00"))
            return datetime.now(timezone.utc) >= expiry
        except ValueError:
            return True  # Assume expired if we can't parse the date

    def get_service_account_credentials(self) -> ServiceAccountCredentials:
        """
        Get service account credentials for Google Drive API.

        Returns:
            ServiceAccountCredentials: Service account credentials

        Raises:
            AuthenticationError: If service account setup fails
        """
        try:
            # Check if service account key is configured
            service_account_key = getattr(
                self.settings, "google_service_account_key", None
            )
            if not service_account_key:
                raise AuthenticationError("Google service account key not configured")

            # Parse service account key if it's a string
            if isinstance(service_account_key, str):
                try:
                    service_account_info = json.loads(service_account_key)
                except json.JSONDecodeError as e:
                    raise AuthenticationError(
                        f"Invalid service account key format: {e}"
                    )
            else:
                service_account_info = service_account_key

            # Create service account credentials with required scopes
            credentials = ServiceAccountCredentials.from_service_account_info(
                service_account_info,
                scopes=[
                    "https://www.googleapis.com/auth/drive.readonly",
                    "https://www.googleapis.com/auth/drive.metadata.readonly",
                ],
            )

            logger.info("Successfully created service account credentials")
            return credentials

        except AuthenticationError:
            raise
        except Exception as e:
            logger.error(f"Failed to create service account credentials: {e}")
            raise AuthenticationError(
                f"Failed to create service account credentials: {e}"
            )
