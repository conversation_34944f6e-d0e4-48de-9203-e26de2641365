"""
Google Drive adapter module.

This module provides a modular Google Drive integration with
separate components for authentication, API client, and event processing.
"""

from .adapter import GoogleDriveAdapter
from .auth_handler import GoogleDriveAuthHandler
from .api_client import GoogleDriveAP<PERSON>lient
from .event_processor import GoogleDriveEventProcessor

__all__ = [
    "GoogleDriveAdapter",
    "GoogleDriveAuthHandler", 
    "GoogleDriveAPIClient",
    "GoogleDriveEventProcessor",
]
