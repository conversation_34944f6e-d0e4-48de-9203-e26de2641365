"""
Google Drive API client.

This module provides a clean interface for interacting with the
Google Drive API, handling authentication and API calls.
"""

import asyncio
from typing import Dict, Any, List, Optional
from datetime import datetime, timezone

from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from src.utils.logger import get_logger
from src.core.exceptions import ExternalServiceError, AuthenticationError
from src.utils.common import retry_async

logger = get_logger(__name__)


class GoogleDriveAPIClient:
    """
    Google Drive API client with async support and error handling.
    
    This class provides methods for interacting with the Google Drive API
    including file operations, change monitoring, and webhook management.
    """
    
    def __init__(self, credentials: Credentials):
        """
        Initialize the API client.
        
        Args:
            credentials: Google OAuth2 credentials
        """
        self.credentials = credentials
        self.service = None
        self._initialize_service()
        
        logger.debug("Google Drive API client initialized")
    
    def _initialize_service(self):
        """Initialize the Google Drive service."""
        try:
            self.service = build('drive', 'v3', credentials=self.credentials)
        except Exception as e:
            logger.error(f"Failed to initialize Google Drive service: {e}")
            raise ExternalServiceError(f"Failed to initialize Google Drive service: {e}")
    
    @retry_async(max_attempts=3, base_delay=1.0)
    async def get_file(self, file_id: str, fields: str = "id,name,mimeType,modifiedTime,size") -> Dict[str, Any]:
        """
        Get file metadata.
        
        Args:
            file_id: Google Drive file ID
            fields: Fields to retrieve
            
        Returns:
            Dict[str, Any]: File metadata
            
        Raises:
            ExternalServiceError: If API call fails
        """
        try:
            # Run in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                lambda: self.service.files().get(fileId=file_id, fields=fields).execute()
            )
            
            logger.debug(f"Retrieved file metadata for {file_id}")
            return result
            
        except HttpError as e:
            if e.resp.status == 404:
                logger.warning(f"File not found: {file_id}")
                return None
            elif e.resp.status == 403:
                logger.error(f"Access denied for file {file_id}")
                raise AuthenticationError(f"Access denied for file {file_id}")
            else:
                logger.error(f"Google Drive API error: {e}")
                raise ExternalServiceError(f"Google Drive API error: {e}")
        except Exception as e:
            logger.error(f"Failed to get file {file_id}: {e}")
            raise ExternalServiceError(f"Failed to get file: {e}")
    
    @retry_async(max_attempts=3, base_delay=1.0)
    async def list_files(
        self,
        query: Optional[str] = None,
        page_size: int = 100,
        fields: str = "files(id,name,mimeType,modifiedTime,size)",
        order_by: str = "modifiedTime desc"
    ) -> List[Dict[str, Any]]:
        """
        List files in Google Drive.
        
        Args:
            query: Search query
            page_size: Number of files per page
            fields: Fields to retrieve
            order_by: Sort order
            
        Returns:
            List[Dict[str, Any]]: List of file metadata
            
        Raises:
            ExternalServiceError: If API call fails
        """
        try:
            files = []
            page_token = None
            
            while True:
                # Run in thread pool
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    None,
                    lambda: self.service.files().list(
                        q=query,
                        pageSize=page_size,
                        fields=f"nextPageToken,{fields}",
                        orderBy=order_by,
                        pageToken=page_token
                    ).execute()
                )
                
                files.extend(result.get('files', []))
                page_token = result.get('nextPageToken')
                
                if not page_token:
                    break
            
            logger.debug(f"Listed {len(files)} files")
            return files
            
        except HttpError as e:
            logger.error(f"Google Drive API error: {e}")
            raise ExternalServiceError(f"Google Drive API error: {e}")
        except Exception as e:
            logger.error(f"Failed to list files: {e}")
            raise ExternalServiceError(f"Failed to list files: {e}")
    
    @retry_async(max_attempts=3, base_delay=1.0)
    async def get_changes(
        self,
        page_token: str,
        include_removed: bool = True,
        page_size: int = 100
    ) -> Dict[str, Any]:
        """
        Get changes from Google Drive.
        
        Args:
            page_token: Token for change tracking
            include_removed: Whether to include removed files
            page_size: Number of changes per page
            
        Returns:
            Dict[str, Any]: Changes response
            
        Raises:
            ExternalServiceError: If API call fails
        """
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                lambda: self.service.changes().list(
                    pageToken=page_token,
                    includeRemoved=include_removed,
                    pageSize=page_size,
                    fields="nextPageToken,newStartPageToken,changes(fileId,file(id,name,mimeType,modifiedTime,size),removed)"
                ).execute()
            )
            
            logger.debug(f"Retrieved {len(result.get('changes', []))} changes")
            return result
            
        except HttpError as e:
            logger.error(f"Google Drive API error: {e}")
            raise ExternalServiceError(f"Google Drive API error: {e}")
        except Exception as e:
            logger.error(f"Failed to get changes: {e}")
            raise ExternalServiceError(f"Failed to get changes: {e}")
    
    @retry_async(max_attempts=3, base_delay=1.0)
    async def get_start_page_token(self) -> str:
        """
        Get start page token for change tracking.
        
        Returns:
            str: Start page token
            
        Raises:
            ExternalServiceError: If API call fails
        """
        try:
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                lambda: self.service.changes().getStartPageToken().execute()
            )
            
            token = result.get('startPageToken')
            logger.debug(f"Retrieved start page token: {token}")
            return token
            
        except HttpError as e:
            logger.error(f"Google Drive API error: {e}")
            raise ExternalServiceError(f"Google Drive API error: {e}")
        except Exception as e:
            logger.error(f"Failed to get start page token: {e}")
            raise ExternalServiceError(f"Failed to get start page token: {e}")
    
    @retry_async(max_attempts=3, base_delay=1.0)
    async def create_webhook(
        self,
        channel_id: str,
        webhook_url: str,
        resource_id: Optional[str] = None,
        ttl: Optional[int] = None
    ) -> Dict[str, Any]:
        """
        Create a webhook for change notifications.
        
        Args:
            channel_id: Unique channel ID
            webhook_url: Webhook URL to receive notifications
            resource_id: Resource ID to watch (optional)
            ttl: Time to live in seconds
            
        Returns:
            Dict[str, Any]: Webhook channel information
            
        Raises:
            ExternalServiceError: If webhook creation fails
        """
        try:
            body = {
                'id': channel_id,
                'type': 'web_hook',
                'address': webhook_url,
            }
            
            if ttl:
                # Calculate expiration time
                expiration = int((datetime.now(timezone.utc).timestamp() + ttl) * 1000)
                body['expiration'] = str(expiration)
            
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                lambda: self.service.changes().watch(
                    pageToken=resource_id or await self.get_start_page_token(),
                    body=body
                ).execute()
            )
            
            logger.info(f"Created webhook channel: {channel_id}")
            return result
            
        except HttpError as e:
            logger.error(f"Google Drive API error: {e}")
            raise ExternalServiceError(f"Google Drive API error: {e}")
        except Exception as e:
            logger.error(f"Failed to create webhook: {e}")
            raise ExternalServiceError(f"Failed to create webhook: {e}")
    
    @retry_async(max_attempts=3, base_delay=1.0)
    async def stop_webhook(self, channel_id: str, resource_id: str) -> bool:
        """
        Stop a webhook channel.
        
        Args:
            channel_id: Channel ID to stop
            resource_id: Resource ID
            
        Returns:
            bool: True if successful
            
        Raises:
            ExternalServiceError: If stopping webhook fails
        """
        try:
            body = {
                'id': channel_id,
                'resourceId': resource_id
            }
            
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: self.service.channels().stop(body=body).execute()
            )
            
            logger.info(f"Stopped webhook channel: {channel_id}")
            return True
            
        except HttpError as e:
            if e.resp.status == 404:
                logger.warning(f"Webhook channel not found: {channel_id}")
                return True  # Consider it successful if already gone
            else:
                logger.error(f"Google Drive API error: {e}")
                raise ExternalServiceError(f"Google Drive API error: {e}")
        except Exception as e:
            logger.error(f"Failed to stop webhook: {e}")
            raise ExternalServiceError(f"Failed to stop webhook: {e}")
    
    async def test_connection(self) -> bool:
        """
        Test the API connection.
        
        Returns:
            bool: True if connection is working
        """
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: self.service.about().get(fields="user").execute()
            )
            
            logger.debug("Google Drive API connection test successful")
            return True
            
        except Exception as e:
            logger.warning(f"Google Drive API connection test failed: {e}")
            return False
