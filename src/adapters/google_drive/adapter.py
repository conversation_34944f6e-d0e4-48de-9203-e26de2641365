"""
Modular Google Drive adapter.

This module provides a clean, modular Google Drive adapter that uses
separate components for authentication, API access, and event processing.
"""

from typing import Dict, Any, List, Optional, Set
from uuid import UUID, uuid4
from datetime import datetime, timezone, timedelta

from src.adapters.base import (
    BaseTriggerAdapter,
    TriggerEvent,
    TriggerEventType,
    TriggerConfiguration,
    AdapterHealthStatus,
)
from src.adapters.google_drive.auth_handler import GoogleDriveAuthHandler
from src.adapters.google_drive.api_client import GoogleDriveAPIClient
from src.adapters.google_drive.event_processor import GoogleDriveEventProcessor
from src.schemas.google_drive_subscription import (
    GoogleDriveSubscriptionCreate,
    GoogleDriveSubscriptionResponse,
)
from src.utils.logger import get_logger
from src.core.exceptions import ValidationError, ExternalServiceError

logger = get_logger(__name__)


class GoogleDriveAdapter(BaseTriggerAdapter):
    """
    Modular Google Drive adapter with clean separation of concerns.

    This adapter uses separate components for authentication, API access,
    and event processing to provide a maintainable and testable solution.
    """

    def __init__(self):
        """Initialize the Google Drive adapter."""
        super().__init__("google_drive")

        # Initialize components
        self.auth_handler = GoogleDriveAuthHandler()
        self.api_client = None
        self.event_processor = None

        # Active webhooks tracking
        self._active_webhooks: Dict[str, Dict[str, Any]] = {}

        logger.info("Modular Google Drive adapter initialized")

    async def setup_trigger(
        self, trigger_config: TriggerConfiguration, session=None
    ) -> bool:
        """
        Set up a Google Drive trigger with webhook.

        Args:
            trigger_config: Trigger configuration
            session: Optional database session

        Returns:
            bool: True if setup successful
        """
        try:
            # Validate configuration
            self._validate_trigger_config(trigger_config)

            # Get user credentials
            credentials_data = await self._get_user_credentials(trigger_config.user_id)
            if not credentials_data:
                logger.error(f"No credentials found for user {trigger_config.user_id}")
                return False

            # Initialize API client
            credentials = self.auth_handler._create_credentials_from_token_data(
                credentials_data
            )
            self.api_client = GoogleDriveAPIClient(credentials)
            self.event_processor = GoogleDriveEventProcessor(self.api_client)

            # Create webhook
            webhook_url = self._get_webhook_url(trigger_config.trigger_id)
            channel_id = f"trigger-{trigger_config.trigger_id}"

            webhook_response = await self.api_client.create_webhook(
                channel_id=channel_id,
                webhook_url=webhook_url,
                ttl=trigger_config.config.get("webhook_ttl", 3600),
            )

            # Store webhook information
            self._active_webhooks[str(trigger_config.trigger_id)] = {
                "channel_id": channel_id,
                "resource_id": webhook_response.get("resourceId"),
                "webhook_url": webhook_url,
                "user_id": trigger_config.user_id,
                "created_at": datetime.now(timezone.utc),
                "config": trigger_config.config,
            }

            logger.info(
                f"Successfully set up Google Drive trigger {trigger_config.trigger_id}"
            )
            return True

        except Exception as e:
            logger.error(f"Failed to setup Google Drive trigger: {e}")
            return False

    async def remove_trigger(self, trigger_id: UUID) -> bool:
        """
        Remove a Google Drive trigger and its webhook.

        Args:
            trigger_id: Trigger ID to remove

        Returns:
            bool: True if removal successful
        """
        try:
            trigger_key = str(trigger_id)
            webhook_info = self._active_webhooks.get(trigger_key)

            if not webhook_info:
                logger.warning(f"No webhook found for trigger {trigger_id}")
                return True  # Consider it successful if already gone

            # Stop the webhook
            if self.api_client:
                await self.api_client.stop_webhook(
                    channel_id=webhook_info["channel_id"],
                    resource_id=webhook_info["resource_id"],
                )

            # Remove from tracking
            del self._active_webhooks[trigger_key]

            logger.info(f"Successfully removed Google Drive trigger {trigger_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to remove Google Drive trigger: {e}")
            return False

    async def process_webhook_event(
        self, trigger_id: UUID, headers: Dict[str, str], body: Any
    ) -> List[TriggerEvent]:
        """
        Process a webhook event from Google Drive.

        Args:
            trigger_id: Trigger ID
            headers: Webhook headers
            body: Webhook body (not used for Google Drive)

        Returns:
            List[TriggerEvent]: Processed trigger events
        """
        try:
            if not self.event_processor:
                logger.error("Event processor not initialized")
                return []

            # Validate webhook headers
            if not self.event_processor.validate_webhook_headers(headers):
                logger.warning("Invalid webhook headers")
                return []

            # Extract webhook data
            webhook_data = self.event_processor.extract_webhook_data(headers)

            # Process the event
            events = await self.event_processor.process_webhook_event(
                headers=headers,
                channel_id=webhook_data["channel_id"],
                resource_id=webhook_data["resource_id"],
                resource_state=webhook_data["resource_state"],
            )

            logger.info(f"Processed {len(events)} events for trigger {trigger_id}")
            return events

        except Exception as e:
            logger.error(f"Failed to process webhook event: {e}")
            return []

    async def _perform_health_check(self) -> bool:
        """
        Perform health check for Google Drive adapter.

        Returns:
            bool: True if healthy
        """
        try:
            if not self.api_client:
                return True  # No active connections to check

            # Test API connection
            return await self.api_client.test_connection()

        except Exception as e:
            logger.warning(f"Google Drive health check failed: {e}")
            return False

    def get_supported_event_types(self) -> Set[TriggerEventType]:
        """Get supported event types."""
        return {
            TriggerEventType.CREATED,
            TriggerEventType.UPDATED,
            TriggerEventType.DELETED,
        }

    def get_sample_event_data(self) -> Dict[str, Any]:
        """Get sample event data."""
        if self.event_processor:
            return self.event_processor.get_sample_event_data()

        return {
            "file_id": "1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms",
            "file_name": "Example Document",
            "mime_type": "application/vnd.google-apps.document",
            "modified_time": "2024-01-01T12:00:00.000Z",
        }

    def get_available_fields(self) -> List[Dict[str, str]]:
        """Get available fields for trigger configuration."""
        return [
            {
                "name": "file_id",
                "type": "string",
                "description": "Google Drive file ID",
            },
            {"name": "file_name", "type": "string", "description": "File name"},
            {"name": "mime_type", "type": "string", "description": "File MIME type"},
            {"name": "size", "type": "string", "description": "File size in bytes"},
            {
                "name": "modified_time",
                "type": "datetime",
                "description": "Last modification time",
            },
            {
                "name": "web_view_link",
                "type": "url",
                "description": "Link to view file",
            },
            {"name": "parents", "type": "array", "description": "Parent folder IDs"},
            {"name": "owners", "type": "array", "description": "File owners"},
            {
                "name": "last_modifying_user",
                "type": "object",
                "description": "Last user to modify",
            },
        ]

    def _validate_trigger_config(self, trigger_config: TriggerConfiguration) -> None:
        """
        Validate trigger configuration.

        Args:
            trigger_config: Configuration to validate

        Raises:
            ValidationError: If configuration is invalid
        """
        if not trigger_config.user_id:
            raise ValidationError("User ID is required")

        if not trigger_config.event_types:
            raise ValidationError("At least one event type is required")

        # Validate event types
        supported_types = self.get_supported_event_types()
        for event_type in trigger_config.event_types:
            if event_type not in supported_types:
                raise ValidationError(f"Unsupported event type: {event_type}")

        # Validate webhook TTL
        webhook_ttl = trigger_config.config.get("webhook_ttl", 3600)
        if not isinstance(webhook_ttl, int) or webhook_ttl < 300 or webhook_ttl > 86400:
            raise ValidationError("Webhook TTL must be between 300 and 86400 seconds")

    def _get_webhook_url(self, trigger_id: UUID) -> str:
        """
        Get webhook URL for a trigger.

        Args:
            trigger_id: Trigger ID

        Returns:
            str: Webhook URL
        """
        from src.utils.config import get_settings

        settings = get_settings()
        base_url = settings.webhook_base_url.rstrip("/")
        return f"{base_url}/api/v1/webhooks/google_drive/{trigger_id}"

    async def _get_user_credentials(self, user_id: str) -> Optional[Dict[str, Any]]:
        """
        Get user credentials from the auth service.

        Args:
            user_id: User ID

        Returns:
            Optional[Dict[str, Any]]: User credentials or None
        """
        try:
            # This would typically call the auth service
            # For now, return None to indicate no credentials
            logger.warning(f"Credential retrieval not implemented for user {user_id}")
            return None

        except Exception as e:
            logger.error(f"Failed to get user credentials: {e}")
            return None

    async def cleanup(self) -> None:
        """Cleanup adapter resources."""
        try:
            # Stop all active webhooks
            for trigger_id, webhook_info in list(self._active_webhooks.items()):
                try:
                    if self.api_client:
                        await self.api_client.stop_webhook(
                            channel_id=webhook_info["channel_id"],
                            resource_id=webhook_info["resource_id"],
                        )
                except Exception as e:
                    logger.warning(
                        f"Failed to stop webhook for trigger {trigger_id}: {e}"
                    )

            self._active_webhooks.clear()
            logger.info("Google Drive adapter cleanup completed")

        except Exception as e:
            logger.error(f"Error during Google Drive adapter cleanup: {e}")

    def get_active_webhooks(self) -> Dict[str, Dict[str, Any]]:
        """Get information about active webhooks."""
        return self._active_webhooks.copy()

    async def process_event(self, raw_event: Dict[str, Any]) -> Optional[TriggerEvent]:
        """
        Process a raw event from Google Drive.

        Args:
            raw_event: Raw event data from Google Drive

        Returns:
            Optional[TriggerEvent]: Processed trigger event or None
        """
        try:
            # Extract file information
            file_id = raw_event.get("id") or raw_event.get("fileId")
            if not file_id:
                logger.warning("Event missing file ID")
                return None

            # Determine event type
            event_type = TriggerEventType.UPDATED  # Default
            if raw_event.get("removed", False):
                event_type = TriggerEventType.DELETED
            elif raw_event.get("created") == raw_event.get("modifiedTime"):
                event_type = TriggerEventType.CREATED

            # Create trigger event
            trigger_event = TriggerEvent(
                event_id=f"gdrive-{file_id}-{int(datetime.now(timezone.utc).timestamp())}",
                event_type=event_type,
                source="google_drive",
                timestamp=datetime.now(timezone.utc),
                data=raw_event,
                metadata={
                    "file_id": file_id,
                    "file_name": raw_event.get("name", "Unknown"),
                    "mime_type": raw_event.get("mimeType"),
                    "removed": raw_event.get("removed", False),
                },
            )

            logger.debug(f"Processed Google Drive event: {file_id}")
            return trigger_event

        except Exception as e:
            logger.error(f"Failed to process Google Drive event: {e}")
            return None

    async def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        Validate Google Drive adapter configuration.

        Args:
            config: Configuration to validate

        Returns:
            bool: True if configuration is valid
        """
        try:
            # Validate webhook TTL
            webhook_ttl = config.get("webhook_ttl", 3600)
            if (
                not isinstance(webhook_ttl, int)
                or webhook_ttl < 300
                or webhook_ttl > 86400
            ):
                logger.error("Webhook TTL must be between 300 and 86400 seconds")
                return False

            # Validate folder IDs if provided
            folder_ids = config.get("folder_ids", [])
            if folder_ids and not isinstance(folder_ids, list):
                logger.error("Folder IDs must be a list")
                return False

            # Validate file types if provided
            file_types = config.get("file_types", [])
            if file_types and not isinstance(file_types, list):
                logger.error("File types must be a list")
                return False

            # Validate polling settings if used
            use_polling = config.get("use_polling", False)
            if use_polling:
                poll_interval = config.get("poll_interval_seconds", 300)
                if not isinstance(poll_interval, int) or poll_interval < 60:
                    logger.error("Poll interval must be at least 60 seconds")
                    return False

            # Validate event filters if provided
            event_filters = config.get("event_filters", {})
            if event_filters and not isinstance(event_filters, dict):
                logger.error("Event filters must be a dictionary")
                return False

            logger.debug("Google Drive configuration validation passed")
            return True

        except Exception as e:
            logger.error(f"Configuration validation failed: {e}")
            return False

    async def create_subscription(self, subscription_data, session=None):
        """
        Create a Google Drive subscription for the API route.

        This method provides compatibility with the Google Drive API routes
        that expect a create_subscription method.

        Args:
            subscription_data: Subscription creation data
            session: Optional database session

        Returns:
            dict: Subscription response data
        """
        try:
            # Extract organization ID and event types
            organization_id = subscription_data.organization_id
            event_types = subscription_data.event_types

            # Create a unique channel ID
            channel_id = f"org-{organization_id}-{uuid4().hex[:8]}"

            # Initialize API client with service account credentials
            # For organization-level subscriptions, we use service account
            if not self.api_client:
                # Initialize with service account credentials
                credentials = self.auth_handler.get_service_account_credentials()
                if not credentials:
                    raise RuntimeError("No service account credentials available")

                self.api_client = GoogleDriveAPIClient(credentials)
                self.event_processor = GoogleDriveEventProcessor(self.api_client)

            # Create webhook URL
            webhook_url = f"{self._get_base_webhook_url()}/api/v1/webhooks/google_drive/org/{organization_id}"

            # Create the webhook
            webhook_response = await self.api_client.create_webhook(
                channel_id=channel_id,
                webhook_url=webhook_url,
                ttl=3600,  # 1 hour default
            )

            # Store subscription information
            subscription_id = str(uuid4())
            current_time = datetime.now(timezone.utc)

            subscription_info = {
                "id": subscription_id,
                "organization_id": organization_id,
                "event_types": event_types,
                "channel_id": channel_id,
                "resource_id": webhook_response.get("resourceId"),
                "is_active": True,
                "start_page_token": webhook_response.get("pageToken"),
                "current_page_token": webhook_response.get("pageToken"),
                "expires_at": current_time.isoformat(),
                "created_at": current_time.isoformat(),
                "updated_at": current_time.isoformat(),
            }

            # Store in active webhooks for tracking
            self._active_webhooks[subscription_id] = {
                "channel_id": channel_id,
                "resource_id": webhook_response.get("resourceId"),
                "webhook_url": webhook_url,
                "organization_id": organization_id,
                "created_at": current_time,
                "config": {"event_types": event_types},
            }

            logger.info(
                f"Created Google Drive subscription for organization {organization_id}"
            )

            return GoogleDriveSubscriptionResponse(**subscription_info)

        except Exception as e:
            logger.error(f"Failed to create Google Drive subscription: {e}")
            raise RuntimeError(f"Failed to create subscription: {str(e)}")

    async def list_subscriptions(self, session=None):
        """
        List all active Google Drive subscriptions.

        Args:
            session: Optional database session

        Returns:
            list: List of active subscriptions
        """
        try:
            subscriptions = []
            for sub_id, webhook_info in self._active_webhooks.items():
                if "organization_id" in webhook_info:  # Organization subscription
                    subscription = {
                        "id": sub_id,
                        "organization_id": webhook_info["organization_id"],
                        "event_types": webhook_info["config"].get("event_types", []),
                        "channel_id": webhook_info["channel_id"],
                        "resource_id": webhook_info["resource_id"],
                        "is_active": True,
                        "created_at": webhook_info["created_at"].isoformat(),
                        "updated_at": webhook_info["created_at"].isoformat(),
                    }
                    subscriptions.append(subscription)

            return subscriptions

        except Exception as e:
            logger.error(f"Failed to list subscriptions: {e}")
            return []

    async def delete_subscription(self, subscription_id: str, session=None):
        """
        Delete a Google Drive subscription.

        Args:
            subscription_id: ID of the subscription to delete
            session: Optional database session

        Returns:
            bool: True if deletion successful
        """
        try:
            webhook_info = self._active_webhooks.get(subscription_id)
            if not webhook_info:
                raise ValueError(f"Subscription {subscription_id} not found")

            # Stop the webhook
            if self.api_client:
                await self.api_client.stop_webhook(
                    channel_id=webhook_info["channel_id"],
                    resource_id=webhook_info["resource_id"],
                )

            # Remove from tracking
            del self._active_webhooks[subscription_id]

            logger.info(f"Deleted Google Drive subscription {subscription_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to delete subscription {subscription_id}: {e}")
            return False

    async def check_expired_subscriptions(self, session=None):
        """
        Check and update expired subscriptions.

        Args:
            session: Optional database session

        Returns:
            dict: Status of expired subscription check
        """
        try:
            current_time = datetime.now(timezone.utc)
            expired_count = 0

            # In a real implementation, you would check expiration times
            # For now, we'll just return a status

            return {
                "checked_at": current_time.isoformat(),
                "expired_count": expired_count,
                "active_count": len(self._active_webhooks),
            }

        except Exception as e:
            logger.error(f"Failed to check expired subscriptions: {e}")
            return {"error": str(e)}

    def _get_base_webhook_url(self) -> str:
        """Get base webhook URL from settings."""
        from src.utils.config import get_settings

        settings = get_settings()
        return settings.webhook_base_url.rstrip("/")
