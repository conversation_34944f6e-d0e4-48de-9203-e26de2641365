"""
Google Calendar adapter for the Trigger Service.

This module provides the main GoogleCalendarAdapter class that integrates
with Google Calendar API for event-based triggers.
"""

from typing import Dict, Any, List, Optional, Set
from uuid import UUID
from datetime import datetime, timezone

from src.adapters.base import (
    BaseTriggerAdapter,
    TriggerEvent,
    TriggerEventType,
    TriggerConfiguration,
    AdapterHealthStatus,
)
from src.adapters.google_calendar_components.oauth_handler import (
    GoogleCalendarOAuthHandler,
)
from src.adapters.google_calendar_components.webhook_manager import (
    GoogleCalendarWebhookManager,
)
from src.adapters.google_calendar_components.event_processor import (
    GoogleCalendarEventProcessor,
)
from src.adapters.google_calendar_components.api_client import GoogleCalendarAPIClient
from src.utils.logger import get_logger
from src.core.exceptions import ValidationError, ExternalServiceError

logger = get_logger(__name__)


class GoogleCalendarAdapter(BaseTriggerAdapter):
    """
    Google Calendar adapter with modular components.

    This adapter uses separate components for OAuth handling, webhook management,
    event processing, and API client operations.
    """

    def __init__(self):
        """Initialize the Google Calendar adapter."""
        super().__init__("google_calendar")

        # Initialize components
        self.oauth_handler = GoogleCalendarOAuthHandler()
        self.webhook_manager = GoogleCalendarWebhookManager()
        self.event_processor = GoogleCalendarEventProcessor()
        self.api_client = GoogleCalendarAPIClient()

        # Active webhooks tracking
        self._active_webhooks: Dict[str, Dict[str, Any]] = {}

        logger.info("Google Calendar adapter initialized")

    async def setup_trigger(
        self, trigger_config: TriggerConfiguration, session=None
    ) -> bool:
        """
        Set up a Google Calendar trigger with webhook.

        Args:
            trigger_config: Trigger configuration
            session: Optional database session

        Returns:
            bool: True if setup successful
        """
        try:
            # Validate configuration
            self._validate_trigger_config(trigger_config)

            # Get user credentials
            credentials = await self.oauth_handler.get_credentials(
                trigger_config.user_id
            )
            if not credentials:
                logger.error(f"No credentials found for user {trigger_config.user_id}")
                return False

            # Set up webhook
            webhook_url = self._get_webhook_url(trigger_config.trigger_id)
            channel_id = f"trigger-{trigger_config.trigger_id}"

            webhook_response = await self.webhook_manager.create_webhook(
                credentials=credentials,
                channel_id=channel_id,
                webhook_url=webhook_url,
                calendar_id=trigger_config.config.get("calendar_id", "primary"),
                ttl=trigger_config.config.get("webhook_ttl", 3600),
            )

            # Store webhook information
            self._active_webhooks[str(trigger_config.trigger_id)] = {
                "channel_id": channel_id,
                "resource_id": webhook_response.get("resourceId"),
                "webhook_url": webhook_url,
                "user_id": trigger_config.user_id,
                "calendar_id": trigger_config.config.get("calendar_id", "primary"),
                "created_at": datetime.now(timezone.utc),
                "config": trigger_config.config,
            }

            logger.info(
                f"Successfully set up Google Calendar trigger {trigger_config.trigger_id}"
            )
            return True

        except Exception as e:
            logger.error(f"Failed to setup Google Calendar trigger: {e}")
            return False

    async def remove_trigger(self, trigger_id: UUID) -> bool:
        """
        Remove a Google Calendar trigger and its webhook.

        Args:
            trigger_id: Trigger ID to remove

        Returns:
            bool: True if removal successful
        """
        try:
            trigger_key = str(trigger_id)
            webhook_info = self._active_webhooks.get(trigger_key)

            if not webhook_info:
                logger.warning(f"No webhook found for trigger {trigger_id}")
                return True  # Consider it successful if already gone

            # Get user credentials
            credentials = await self.oauth_handler.get_credentials(
                webhook_info["user_id"]
            )
            if credentials:
                # Stop the webhook
                await self.webhook_manager.stop_webhook(
                    credentials=credentials,
                    channel_id=webhook_info["channel_id"],
                    resource_id=webhook_info["resource_id"],
                )

            # Remove from tracking
            del self._active_webhooks[trigger_key]

            logger.info(f"Successfully removed Google Calendar trigger {trigger_id}")
            return True

        except Exception as e:
            logger.error(f"Failed to remove Google Calendar trigger: {e}")
            return False

    async def process_webhook_event(
        self, trigger_id: UUID, headers: Dict[str, str], body: Any
    ) -> List[TriggerEvent]:
        """
        Process a webhook event from Google Calendar.

        Args:
            trigger_id: Trigger ID
            headers: Webhook headers
            body: Webhook body

        Returns:
            List[TriggerEvent]: Processed trigger events
        """
        try:
            trigger_key = str(trigger_id)
            webhook_info = self._active_webhooks.get(trigger_key)

            if not webhook_info:
                logger.warning(f"No webhook info found for trigger {trigger_id}")
                return []

            # Get user credentials
            credentials = await self.oauth_handler.get_credentials(
                webhook_info["user_id"]
            )
            if not credentials:
                logger.error(f"No credentials found for user {webhook_info['user_id']}")
                return []

            # Process the event
            events = await self.event_processor.process_webhook_event(
                credentials=credentials,
                headers=headers,
                body=body,
                calendar_id=webhook_info["calendar_id"],
                trigger_config=webhook_info["config"],
            )

            logger.info(f"Processed {len(events)} events for trigger {trigger_id}")
            return events

        except Exception as e:
            logger.error(f"Failed to process webhook event: {e}")
            return []

    async def _perform_health_check(self) -> bool:
        """
        Perform health check for Google Calendar adapter.

        Returns:
            bool: True if healthy
        """
        try:
            # Test API connectivity
            return await self.api_client.test_connection()

        except Exception as e:
            logger.warning(f"Google Calendar health check failed: {e}")
            return False

    def get_supported_event_types(self) -> Set[TriggerEventType]:
        """Get supported event types."""
        return {
            TriggerEventType.CREATED,
            TriggerEventType.UPDATED,
            TriggerEventType.DELETED,
        }

    def get_sample_event_data(self) -> Dict[str, Any]:
        """Get sample event data."""
        return {
            "event_id": "example_event_123",
            "summary": "Example Meeting",
            "description": "This is an example calendar event",
            "start": {"dateTime": "2024-01-01T10:00:00Z", "timeZone": "UTC"},
            "end": {"dateTime": "2024-01-01T11:00:00Z", "timeZone": "UTC"},
            "attendees": [{"email": "<EMAIL>", "responseStatus": "accepted"}],
            "creator": {"email": "<EMAIL>"},
            "organizer": {"email": "<EMAIL>"},
        }

    def get_available_fields(self) -> List[Dict[str, str]]:
        """Get available fields for trigger configuration."""
        return [
            {
                "name": "event_id",
                "type": "string",
                "description": "Google Calendar event ID",
            },
            {"name": "summary", "type": "string", "description": "Event title/summary"},
            {
                "name": "description",
                "type": "string",
                "description": "Event description",
            },
            {"name": "start", "type": "object", "description": "Event start time"},
            {"name": "end", "type": "object", "description": "Event end time"},
            {"name": "attendees", "type": "array", "description": "Event attendees"},
            {"name": "creator", "type": "object", "description": "Event creator"},
            {"name": "organizer", "type": "object", "description": "Event organizer"},
            {"name": "location", "type": "string", "description": "Event location"},
            {"name": "status", "type": "string", "description": "Event status"},
        ]

    def _validate_trigger_config(self, trigger_config: TriggerConfiguration) -> None:
        """
        Validate trigger configuration.

        Args:
            trigger_config: Configuration to validate

        Raises:
            ValidationError: If configuration is invalid
        """
        if not trigger_config.user_id:
            raise ValidationError("User ID is required")

        if not trigger_config.event_types:
            raise ValidationError("At least one event type is required")

        # Validate event types
        supported_types = self.get_supported_event_types()
        for event_type in trigger_config.event_types:
            if event_type not in supported_types:
                raise ValidationError(f"Unsupported event type: {event_type}")

        # Validate calendar ID
        calendar_id = trigger_config.config.get("calendar_id", "primary")
        if not isinstance(calendar_id, str) or not calendar_id.strip():
            raise ValidationError("Calendar ID must be a non-empty string")

        # Validate webhook TTL
        webhook_ttl = trigger_config.config.get("webhook_ttl", 3600)
        if not isinstance(webhook_ttl, int) or webhook_ttl < 300 or webhook_ttl > 86400:
            raise ValidationError("Webhook TTL must be between 300 and 86400 seconds")

    def _get_webhook_url(self, trigger_id: UUID) -> str:
        """
        Get webhook URL for a trigger.

        Args:
            trigger_id: Trigger ID

        Returns:
            str: Webhook URL
        """
        from src.utils.config import get_settings

        settings = get_settings()
        base_url = settings.webhook_base_url.rstrip("/")
        return f"{base_url}/api/v1/webhooks/google_calendar/{trigger_id}"

    async def cleanup(self) -> None:
        """Cleanup adapter resources."""
        try:
            # Stop all active webhooks
            for trigger_id, webhook_info in list(self._active_webhooks.items()):
                try:
                    credentials = await self.oauth_handler.get_credentials(
                        webhook_info["user_id"]
                    )
                    if credentials:
                        await self.webhook_manager.stop_webhook(
                            credentials=credentials,
                            channel_id=webhook_info["channel_id"],
                            resource_id=webhook_info["resource_id"],
                        )
                except Exception as e:
                    logger.warning(
                        f"Failed to stop webhook for trigger {trigger_id}: {e}"
                    )

            self._active_webhooks.clear()
            logger.info("Google Calendar adapter cleanup completed")

        except Exception as e:
            logger.error(f"Error during Google Calendar adapter cleanup: {e}")

    def get_active_webhooks(self) -> Dict[str, Dict[str, Any]]:
        """Get information about active webhooks."""
        return self._active_webhooks.copy()
