"""
Event processor for Google Calendar integration.

This module handles processing of Google Calendar events from webhooks
and transforms them into standardized trigger events.
"""

from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List
from uuid import uuid4

from googleapiclient.errors import HttpError

from src.adapters.base import TriggerEvent, TriggerEventType
from src.utils.logger import get_logger
from src.utils.retry import RetryHandler

logger = get_logger(__name__)


class GoogleCalendarEventProcessor:
    """
    Processes Google Calendar events and transforms them into trigger events.

    This class handles the transformation of raw Google Calendar webhook
    notifications into standardized trigger events.
    """

    def __init__(self, oauth_handler, api_client):
        """
        Initialize the event processor.

        Args:
            oauth_handler: OAuth handler for API access
            api_client: API client for Calendar operations
        """
        self.oauth_handler = oauth_handler
        self.api_client = api_client
        self.retry_handler = RetryHandler(
            max_attempts=3, base_delay=1.0, backoff_factor=2.0
        )

    async def process_webhook_event(
        self, raw_event: Dict[str, Any]
    ) -> Optional[TriggerEvent]:
        """
        Process a raw webhook event from Google Calendar.

        Args:
            raw_event: Raw webhook event data

        Returns:
            TriggerEvent: Standardized trigger event or None if ignored
        """
        try:
            # Extract webhook headers
            channel_id = raw_event.get("channel_id")
            resource_state = raw_event.get("resource_state", "").lower()
            resource_id = raw_event.get("resource_id")

            if not channel_id or not resource_id:
                logger.warning("Missing required webhook headers")
                return None

            # Determine event type from resource state
            event_type = self._map_resource_state_to_event_type(resource_state)
            if not event_type:
                logger.debug(f"Ignoring webhook with resource_state: {resource_state}")
                return None

            # Get channel information to determine user and calendar
            channel_info = raw_event.get("channel_info")
            if not channel_info:
                logger.warning(f"No channel info found for channel_id: {channel_id}")
                return None

            user_id = channel_info.get("user_id")
            calendar_id = channel_info.get("calendar_id")

            if not user_id or not calendar_id:
                logger.warning("Missing user_id or calendar_id in channel info")
                return None

            # Fetch the actual event data from Google Calendar API
            event_data = await self._fetch_latest_events(user_id, calendar_id)
            if not event_data:
                logger.warning("No event data found from Calendar API")
                return None

            # Create standardized trigger event
            trigger_event = TriggerEvent(
                event_id=f"gcal-{resource_id}-{uuid4().hex[:8]}",
                event_type=event_type,
                source="google_calendar",
                timestamp=datetime.now(timezone.utc),
                data={
                    "channel_id": channel_id,
                    "resource_id": resource_id,
                    "resource_state": resource_state,
                    "user_id": user_id,
                    "calendar_id": calendar_id,
                    "events": event_data,
                },
                metadata={
                    "webhook_received_at": datetime.now(timezone.utc).isoformat(),
                    "channel_info": channel_info,
                },
            )

            logger.info(
                f"Processed Google Calendar webhook event",
                event_type=event_type,
                channel_id=channel_id,
                user_id=user_id,
                events_count=len(event_data),
            )

            return trigger_event

        except Exception as e:
            logger.error(f"Error processing webhook event: {e}")
            return None

    def _map_resource_state_to_event_type(
        self, resource_state: str
    ) -> Optional[TriggerEventType]:
        """
        Map Google Calendar resource state to trigger event type.

        Args:
            resource_state: Resource state from webhook

        Returns:
            TriggerEventType: Mapped event type or None if not supported
        """
        state_mapping = {
            "exists": TriggerEventType.UPDATED,
            "not_exists": TriggerEventType.DELETED,
            "sync": None,  # Ignore sync events
        }

        return state_mapping.get(resource_state)

    async def _fetch_latest_events(
        self, user_id: str, calendar_id: str, max_results: int = 10
    ) -> List[Dict[str, Any]]:
        """
        Fetch the latest events from Google Calendar.

        Args:
            user_id: User ID
            calendar_id: Calendar ID
            max_results: Maximum number of events to fetch

        Returns:
            List[Dict[str, Any]]: List of calendar events
        """
        try:
            service = await self.oauth_handler.get_calendar_service(user_id)
            if not service:
                logger.error(f"Failed to get Calendar service for user {user_id}")
                return []

            # Fetch recent events (last 24 hours to current time + 1 hour)
            time_min = (datetime.now(timezone.utc) - timedelta(hours=24)).isoformat()
            time_max = (datetime.now(timezone.utc) + timedelta(hours=1)).isoformat()

            events_result = (
                service.events()
                .list(
                    calendarId=calendar_id,
                    timeMin=time_min,
                    timeMax=time_max,
                    maxResults=max_results,
                    singleEvents=True,
                    orderBy="updated",
                )
                .execute()
            )

            events = events_result.get("items", [])

            # Process and clean event data
            processed_events = []
            for event in events:
                processed_event = self._process_calendar_event(event)
                if processed_event:
                    processed_events.append(processed_event)

            logger.debug(f"Fetched {len(processed_events)} events for user {user_id}")
            return processed_events

        except HttpError as e:
            logger.error(f"Google API error fetching events: {e}")
            return []
        except Exception as e:
            logger.error(f"Error fetching latest events: {e}")
            return []

    def _process_calendar_event(
        self, event: Dict[str, Any]
    ) -> Optional[Dict[str, Any]]:
        """
        Process and clean a calendar event.

        Args:
            event: Raw calendar event from Google API

        Returns:
            Dict[str, Any]: Processed event data or None if invalid
        """
        try:
            # Extract essential event information
            processed_event = {
                "id": event.get("id"),
                "summary": event.get("summary", ""),
                "description": event.get("description", ""),
                "status": event.get("status", ""),
                "created": event.get("created"),
                "updated": event.get("updated"),
                "creator": event.get("creator", {}),
                "organizer": event.get("organizer", {}),
                "attendees": event.get("attendees", []),
                "location": event.get("location", ""),
                "html_link": event.get("htmlLink", ""),
            }

            # Process start and end times
            start = event.get("start", {})
            end = event.get("end", {})

            processed_event["start"] = {
                "dateTime": start.get("dateTime"),
                "date": start.get("date"),
                "timeZone": start.get("timeZone"),
            }

            processed_event["end"] = {
                "dateTime": end.get("dateTime"),
                "date": end.get("date"),
                "timeZone": end.get("timeZone"),
            }

            # Add recurrence information if present
            if "recurrence" in event:
                processed_event["recurrence"] = event["recurrence"]

            return processed_event

        except Exception as e:
            logger.error(f"Error processing calendar event: {e}")
            return None

    async def validate_event_data(self, event_data: Dict[str, Any]) -> bool:
        """
        Validate event data structure.

        Args:
            event_data: Event data to validate

        Returns:
            bool: True if valid, False otherwise
        """
        try:
            required_fields = ["id", "summary", "status"]

            for field in required_fields:
                if field not in event_data:
                    logger.warning(f"Missing required field in event data: {field}")
                    return False

            # Validate start/end times
            start = event_data.get("start", {})
            end = event_data.get("end", {})

            if not start or not end:
                logger.warning("Missing start or end time in event data")
                return False

            return True

        except Exception as e:
            logger.error(f"Error validating event data: {e}")
            return False

    async def cleanup(self) -> None:
        """Cleanup event processor resources."""
        logger.info("Event processor cleaned up")
