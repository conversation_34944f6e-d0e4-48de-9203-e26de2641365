"""
Webhook manager for Google Calendar integration.

This module handles webhook subscription lifecycle, channel management,
and webhook event processing for Google Calendar.
"""

import asyncio
import json
import os
from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List
from uuid import UUID, uuid4

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, update, delete
from googleapiclient.errors import HttpError

from src.database.models import Trigger
from src.utils.config import get_settings
from src.utils.logger import get_logger
from src.utils.retry import RetryHandler

logger = get_logger(__name__)


class GoogleCalendarWebhookManager:
    """
    Manages Google Calendar webhook subscriptions and channels.
    
    This class handles the lifecycle of webhook subscriptions including
    creation, renewal, and cleanup of webhook channels.
    """
    
    def __init__(self, oauth_handler):
        """
        Initialize the webhook manager.
        
        Args:
            oauth_handler: OAuth handler instance for API access
        """
        self.oauth_handler = oauth_handler
        self.settings = get_settings()
        self._active_channels: Dict[str, Dict[str, Any]] = {}
        self.retry_handler = RetryHandler(
            max_attempts=3,
            base_delay=1.0,
            backoff_factor=2.0
        )
    
    async def create_webhook_subscription(
        self,
        user_id: str,
        calendar_id: str,
        trigger_id: UUID,
        event_types: List[str],
        webhook_ttl: int = 3600,
        session: Optional[AsyncSession] = None
    ) -> Optional[str]:
        """
        Create a webhook subscription for calendar events.
        
        Args:
            user_id: ID of the user
            calendar_id: Google Calendar ID to monitor
            trigger_id: Trigger ID for this subscription
            event_types: List of event types to monitor
            webhook_ttl: Webhook TTL in seconds
            session: Optional database session
            
        Returns:
            str: Channel ID if successful, None otherwise
        """
        try:
            service = await self.oauth_handler.get_calendar_service(user_id)
            if not service:
                logger.error(f"Failed to get Calendar service for user {user_id}")
                return None
            
            # Generate unique channel ID
            channel_id = f"trigger-{trigger_id}-{uuid4().hex[:8]}"
            
            # Calculate expiration time
            expiration_time = datetime.now(timezone.utc) + timedelta(seconds=webhook_ttl)
            expiration_ms = int(expiration_time.timestamp() * 1000)
            
            # Prepare webhook request
            webhook_url = f"{self.settings.webhook_base_url}/api/v1/webhooks/google_calendar"
            
            body = {
                "id": channel_id,
                "type": "web_hook",
                "address": webhook_url,
                "expiration": expiration_ms,
            }
            
            logger.info(
                f"Creating webhook subscription",
                user_id=user_id,
                calendar_id=calendar_id,
                channel_id=channel_id,
                webhook_url=webhook_url,
                expiration=expiration_time.isoformat()
            )
            
            # Create the webhook subscription
            request = service.events().watch(calendarId=calendar_id, body=body)
            response = request.execute()
            
            resource_id = response.get("resourceId")
            if not resource_id:
                logger.error("No resource ID in webhook response")
                return None
            
            # Store channel information
            channel_info = {
                "resource_id": resource_id,
                "expiration": expiration_ms,
                "trigger_id": trigger_id,
                "user_id": user_id,
                "calendar_id": calendar_id,
                "event_types": event_types,
                "created_at": datetime.now(timezone.utc).timestamp(),
            }
            
            self._active_channels[channel_id] = channel_info
            
            # Store in database if session provided
            if session:
                try:
                    stmt = update(Trigger).where(
                        Trigger.id == trigger_id
                    ).values(channel_id=channel_id)
                    await session.execute(stmt)
                    await session.commit()
                    logger.info(f"Stored channel_id in database: {channel_id}")
                except Exception as db_error:
                    logger.error(f"Failed to store channel_id in database: {db_error}")
            
            logger.info(
                f"Successfully created webhook subscription",
                channel_id=channel_id,
                resource_id=resource_id,
                user_id=user_id
            )
            
            return channel_id
            
        except HttpError as e:
            logger.error(f"Google API error creating webhook: {e}")
            return None
        except Exception as e:
            logger.error(f"Error creating webhook subscription: {e}")
            return None
    
    async def stop_webhook_channel(self, channel_id: str) -> bool:
        """
        Stop a webhook channel.
        
        Args:
            channel_id: Channel ID to stop
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            channel_info = self._active_channels.get(channel_id)
            if not channel_info:
                logger.warning(f"Channel not found: {channel_id}")
                return False
            
            user_id = channel_info["user_id"]
            resource_id = channel_info["resource_id"]
            
            service = await self.oauth_handler.get_calendar_service(user_id)
            if not service:
                logger.error(f"Failed to get Calendar service for user {user_id}")
                return False
            
            # Stop the channel
            body = {
                "id": channel_id,
                "resourceId": resource_id,
            }
            
            service.channels().stop(body=body).execute()
            
            # Remove from active channels
            del self._active_channels[channel_id]
            
            logger.info(f"Successfully stopped webhook channel: {channel_id}")
            return True
            
        except HttpError as e:
            if e.resp.status == 404:
                # Channel already expired or doesn't exist
                logger.info(f"Channel not found (already expired): {channel_id}")
                self._active_channels.pop(channel_id, None)
                return True
            else:
                logger.error(f"Google API error stopping channel {channel_id}: {e}")
                return False
        except Exception as e:
            logger.error(f"Error stopping webhook channel {channel_id}: {e}")
            return False
    
    async def cleanup_expired_channels(self) -> int:
        """
        Clean up expired webhook channels.
        
        Returns:
            int: Number of channels cleaned up
        """
        cleaned_count = 0
        current_time = datetime.now(timezone.utc).timestamp() * 1000
        
        expired_channels = []
        for channel_id, channel_info in self._active_channels.items():
            if channel_info["expiration"] <= current_time:
                expired_channels.append(channel_id)
        
        for channel_id in expired_channels:
            if await self.stop_webhook_channel(channel_id):
                cleaned_count += 1
        
        logger.info(f"Cleaned up {cleaned_count} expired webhook channels")
        return cleaned_count
    
    def get_channel_info(self, channel_id: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a webhook channel.
        
        Args:
            channel_id: Channel ID
            
        Returns:
            Dict[str, Any]: Channel information if found, None otherwise
        """
        return self._active_channels.get(channel_id)
    
    def list_active_channels(self) -> List[Dict[str, Any]]:
        """
        List all active webhook channels.
        
        Returns:
            List[Dict[str, Any]]: List of active channel information
        """
        return [
            {"channel_id": channel_id, **info}
            for channel_id, info in self._active_channels.items()
        ]
    
    async def renew_channel(self, channel_id: str, webhook_ttl: int = 3600) -> bool:
        """
        Renew a webhook channel.
        
        Args:
            channel_id: Channel ID to renew
            webhook_ttl: New TTL in seconds
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            channel_info = self._active_channels.get(channel_id)
            if not channel_info:
                logger.warning(f"Channel not found for renewal: {channel_id}")
                return False
            
            # Stop the old channel
            await self.stop_webhook_channel(channel_id)
            
            # Create a new channel
            new_channel_id = await self.create_webhook_subscription(
                user_id=channel_info["user_id"],
                calendar_id=channel_info["calendar_id"],
                trigger_id=channel_info["trigger_id"],
                event_types=channel_info["event_types"],
                webhook_ttl=webhook_ttl
            )
            
            return new_channel_id is not None
            
        except Exception as e:
            logger.error(f"Error renewing webhook channel {channel_id}: {e}")
            return False
    
    async def cleanup(self) -> None:
        """Cleanup webhook manager resources."""
        # Stop all active channels
        for channel_id in list(self._active_channels.keys()):
            await self.stop_webhook_channel(channel_id)
        
        logger.info("Webhook manager cleaned up")
