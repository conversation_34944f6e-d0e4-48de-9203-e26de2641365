"""
API client for Google Calendar integration.

This module provides a high-level interface for Google Calendar API operations
including calendar management, event operations, and API error handling.
"""

from datetime import datetime, timezone, timedelta
from typing import Dict, Any, Optional, List
from googleapiclient.errors import HttpError

from src.utils.logger import get_logger
from src.utils.retry import RetryHandler

logger = get_logger(__name__)


class GoogleCalendarAPIClient:
    """
    High-level API client for Google Calendar operations.
    
    This class provides a simplified interface for common Google Calendar
    API operations with proper error handling and retry logic.
    """
    
    def __init__(self, oauth_handler):
        """
        Initialize the API client.
        
        Args:
            oauth_handler: OAuth handler for authentication
        """
        self.oauth_handler = oauth_handler
        self.retry_handler = RetryHandler(
            max_attempts=3,
            base_delay=1.0,
            backoff_factor=2.0
        )
    
    async def list_calendars(self, user_id: str) -> List[Dict[str, Any]]:
        """
        List calendars for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            List[Dict[str, Any]]: List of calendar information
        """
        try:
            service = await self.oauth_handler.get_calendar_service(user_id)
            if not service:
                return []
            
            calendar_list = service.calendarList().list().execute()
            calendars = calendar_list.get('items', [])
            
            # Process calendar data
            processed_calendars = []
            for calendar in calendars:
                processed_calendar = {
                    "id": calendar.get("id"),
                    "summary": calendar.get("summary", ""),
                    "description": calendar.get("description", ""),
                    "primary": calendar.get("primary", False),
                    "access_role": calendar.get("accessRole", ""),
                    "selected": calendar.get("selected", False),
                    "color_id": calendar.get("colorId", ""),
                    "background_color": calendar.get("backgroundColor", ""),
                    "foreground_color": calendar.get("foregroundColor", ""),
                }
                processed_calendars.append(processed_calendar)
            
            logger.debug(f"Listed {len(processed_calendars)} calendars for user {user_id}")
            return processed_calendars
            
        except HttpError as e:
            logger.error(f"Google API error listing calendars for user {user_id}: {e}")
            return []
        except Exception as e:
            logger.error(f"Error listing calendars for user {user_id}: {e}")
            return []
    
    async def get_calendar(self, user_id: str, calendar_id: str) -> Optional[Dict[str, Any]]:
        """
        Get calendar information.
        
        Args:
            user_id: User ID
            calendar_id: Calendar ID
            
        Returns:
            Dict[str, Any]: Calendar information or None if not found
        """
        try:
            service = await self.oauth_handler.get_calendar_service(user_id)
            if not service:
                return None
            
            calendar = service.calendars().get(calendarId=calendar_id).execute()
            
            processed_calendar = {
                "id": calendar.get("id"),
                "summary": calendar.get("summary", ""),
                "description": calendar.get("description", ""),
                "location": calendar.get("location", ""),
                "time_zone": calendar.get("timeZone", ""),
            }
            
            logger.debug(f"Retrieved calendar {calendar_id} for user {user_id}")
            return processed_calendar
            
        except HttpError as e:
            if e.resp.status == 404:
                logger.warning(f"Calendar {calendar_id} not found for user {user_id}")
            else:
                logger.error(f"Google API error getting calendar {calendar_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error getting calendar {calendar_id} for user {user_id}: {e}")
            return None
    
    async def list_events(
        self,
        user_id: str,
        calendar_id: str,
        time_min: Optional[datetime] = None,
        time_max: Optional[datetime] = None,
        max_results: int = 250,
        single_events: bool = True,
        order_by: str = "startTime"
    ) -> List[Dict[str, Any]]:
        """
        List events from a calendar.
        
        Args:
            user_id: User ID
            calendar_id: Calendar ID
            time_min: Minimum time for events
            time_max: Maximum time for events
            max_results: Maximum number of events to return
            single_events: Whether to expand recurring events
            order_by: How to order the events
            
        Returns:
            List[Dict[str, Any]]: List of events
        """
        try:
            service = await self.oauth_handler.get_calendar_service(user_id)
            if not service:
                return []
            
            # Set default time range if not provided
            if time_min is None:
                time_min = datetime.now(timezone.utc)
            if time_max is None:
                time_max = time_min + timedelta(days=30)
            
            # Convert to ISO format
            time_min_str = time_min.isoformat()
            time_max_str = time_max.isoformat()
            
            events_result = service.events().list(
                calendarId=calendar_id,
                timeMin=time_min_str,
                timeMax=time_max_str,
                maxResults=max_results,
                singleEvents=single_events,
                orderBy=order_by
            ).execute()
            
            events = events_result.get('items', [])
            
            logger.debug(f"Listed {len(events)} events from calendar {calendar_id}")
            return events
            
        except HttpError as e:
            logger.error(f"Google API error listing events: {e}")
            return []
        except Exception as e:
            logger.error(f"Error listing events: {e}")
            return []
    
    async def get_event(
        self, 
        user_id: str, 
        calendar_id: str, 
        event_id: str
    ) -> Optional[Dict[str, Any]]:
        """
        Get a specific event.
        
        Args:
            user_id: User ID
            calendar_id: Calendar ID
            event_id: Event ID
            
        Returns:
            Dict[str, Any]: Event data or None if not found
        """
        try:
            service = await self.oauth_handler.get_calendar_service(user_id)
            if not service:
                return None
            
            event = service.events().get(
                calendarId=calendar_id,
                eventId=event_id
            ).execute()
            
            logger.debug(f"Retrieved event {event_id} from calendar {calendar_id}")
            return event
            
        except HttpError as e:
            if e.resp.status == 404:
                logger.warning(f"Event {event_id} not found in calendar {calendar_id}")
            else:
                logger.error(f"Google API error getting event {event_id}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error getting event {event_id}: {e}")
            return None
    
    async def test_api_access(self, user_id: str) -> bool:
        """
        Test API access for a user.
        
        Args:
            user_id: User ID
            
        Returns:
            bool: True if API access is working, False otherwise
        """
        try:
            service = await self.oauth_handler.get_calendar_service(user_id)
            if not service:
                return False
            
            # Simple API call to test access
            service.calendarList().list(maxResults=1).execute()
            
            logger.debug(f"API access test successful for user {user_id}")
            return True
            
        except HttpError as e:
            if e.resp.status in [401, 403]:
                logger.warning(f"API access denied for user {user_id}: {e}")
                # Invalidate credentials
                self.oauth_handler.invalidate_credentials(user_id)
            else:
                logger.error(f"API access test failed for user {user_id}: {e}")
            return False
        except Exception as e:
            logger.error(f"Error testing API access for user {user_id}: {e}")
            return False
    
    async def get_api_quota_info(self, user_id: str) -> Dict[str, Any]:
        """
        Get API quota information (if available).
        
        Args:
            user_id: User ID
            
        Returns:
            Dict[str, Any]: Quota information
        """
        # Google Calendar API doesn't provide direct quota info
        # This is a placeholder for future implementation
        return {
            "quota_available": True,
            "requests_remaining": "unknown",
            "reset_time": None,
        }
    
    async def cleanup(self) -> None:
        """Cleanup API client resources."""
        logger.info("API client cleaned up")
