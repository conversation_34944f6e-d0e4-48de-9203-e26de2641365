"""
OAuth handler for Google Calendar integration.

This module handles OAuth2 authentication and credential management
for Google Calendar API access.
"""

from typing import Optional, Dict, Any
from google.oauth2.credentials import Credentials
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

from src.utils.auth_client import get_oauth_credentials
from src.utils.logger import get_logger
from src.utils.common import retry_async
from src.utils.retry import RetryHandler

logger = get_logger(__name__)


class GoogleCalendarOAuthHandler:
    """
    Handles OAuth2 authentication for Google Calendar API.

    This class manages credential retrieval, validation, and refresh
    for Google Calendar API access.
    """

    def __init__(self):
        """Initialize the OAuth handler."""
        self._user_credentials: Dict[str, Credentials] = {}
        self._user_services: Dict[str, Any] = {}
        self.retry_handler = RetryHandler(
            max_attempts=3, base_delay=1.0, backoff_factor=2.0
        )

    async def get_credentials(self, user_id: str) -> Optional[Credentials]:
        """
        Get OAuth credentials for a user.

        Args:
            user_id: ID of the user

        Returns:
            Credentials: Google OAuth2 credentials if available
        """
        try:
            # Check cache first
            if user_id in self._user_credentials:
                credentials = self._user_credentials[user_id]
                if credentials.valid:
                    return credentials

                # Try to refresh expired credentials
                if credentials.expired and credentials.refresh_token:
                    try:
                        credentials.refresh(Request())
                        self._user_credentials[user_id] = credentials
                        logger.info(f"Refreshed credentials for user {user_id}")
                        return credentials
                    except Exception as e:
                        logger.warning(
                            f"Failed to refresh credentials for user {user_id}: {e}"
                        )
                        # Remove invalid credentials from cache
                        del self._user_credentials[user_id]

            # Fetch fresh credentials from auth service
            credentials_data = await get_oauth_credentials(
                user_id=user_id, tool_name="google_calendar", provider="google"
            )

            if not credentials_data:
                logger.warning(f"No OAuth credentials found for user {user_id}")
                return None

            # Create credentials object
            credentials = Credentials(
                token=credentials_data.get("access_token"),
                refresh_token=credentials_data.get("refresh_token"),
                token_uri=credentials_data.get(
                    "token_uri", "https://oauth2.googleapis.com/token"
                ),
                client_id=credentials_data.get("client_id"),
                client_secret=credentials_data.get("client_secret"),
                scopes=credentials_data.get(
                    "scopes", ["https://www.googleapis.com/auth/calendar"]
                ),
            )

            # Validate credentials
            if not credentials.valid:
                if credentials.expired and credentials.refresh_token:
                    try:
                        credentials.refresh(Request())
                        logger.info(f"Refreshed new credentials for user {user_id}")
                    except Exception as e:
                        logger.error(
                            f"Failed to refresh new credentials for user {user_id}: {e}"
                        )
                        return None
                else:
                    logger.error(f"Invalid credentials for user {user_id}")
                    return None

            # Cache valid credentials
            self._user_credentials[user_id] = credentials
            logger.info(f"Successfully retrieved credentials for user {user_id}")
            return credentials

        except Exception as e:
            logger.error(f"Error getting credentials for user {user_id}: {e}")
            return None

    async def get_calendar_service(self, user_id: str):
        """
        Get Google Calendar service instance for a user.

        Args:
            user_id: ID of the user

        Returns:
            Google Calendar service instance or None if failed
        """
        try:
            # Check cache first
            if user_id in self._user_services:
                return self._user_services[user_id]

            # Get credentials
            credentials = await self.get_credentials(user_id)
            if not credentials:
                return None

            # Create service
            service = build("calendar", "v3", credentials=credentials)

            # Cache service
            self._user_services[user_id] = service
            logger.debug(f"Created Calendar service for user {user_id}")
            return service

        except Exception as e:
            logger.error(f"Failed to create Calendar service for user {user_id}: {e}")
            return None

    def invalidate_credentials(self, user_id: str) -> None:
        """
        Invalidate cached credentials for a user.

        Args:
            user_id: ID of the user
        """
        if user_id in self._user_credentials:
            del self._user_credentials[user_id]
            logger.info(f"Invalidated credentials for user {user_id}")

        if user_id in self._user_services:
            del self._user_services[user_id]
            logger.info(f"Invalidated service for user {user_id}")

    def clear_cache(self) -> None:
        """Clear all cached credentials and services."""
        self._user_credentials.clear()
        self._user_services.clear()
        logger.info("Cleared OAuth cache")

    async def validate_credentials(self, user_id: str) -> bool:
        """
        Validate credentials for a user.

        Args:
            user_id: ID of the user

        Returns:
            bool: True if credentials are valid, False otherwise
        """
        try:
            service = await self.get_calendar_service(user_id)
            if not service:
                return False

            # Test API call
            service.calendarList().list(maxResults=1).execute()
            return True

        except HttpError as e:
            if e.resp.status in [401, 403]:
                logger.warning(f"Invalid credentials for user {user_id}: {e}")
                self.invalidate_credentials(user_id)
            else:
                logger.error(
                    f"API error validating credentials for user {user_id}: {e}"
                )
            return False
        except Exception as e:
            logger.error(f"Error validating credentials for user {user_id}: {e}")
            return False

    async def cleanup(self) -> None:
        """Cleanup OAuth handler resources."""
        self.clear_cache()
        logger.info("OAuth handler cleaned up")
