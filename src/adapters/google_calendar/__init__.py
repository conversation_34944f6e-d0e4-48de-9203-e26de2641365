"""
Google Calendar adapter package.

This package contains modular components for Google Calendar integration
including OAuth handling, webhook management, event processing, and API client.
"""

from .oauth_handler import GoogleCalendarOAuthHandler
from .webhook_manager import GoogleCalendarWebhookManager
from .event_processor import GoogleCalendarEventProcessor
from .api_client import GoogleCalendarAP<PERSON>lient

__all__ = [
    "GoogleCalendarOAuthHandler",
    "GoogleCalendarWebhookManager", 
    "GoogleCalendarEventProcessor",
    "GoogleCalendarAPIClient",
]
