"""
Google Calendar adapter package.

This package contains modular components for Google Calendar integration
including OAuth handling, webhook management, event processing, and API client.
"""

from .adapter import GoogleCalendarAdapter
from .oauth_handler import GoogleCalendarOAuthHandler
from .webhook_manager import GoogleCalendarWebhookManager
from .event_processor import GoogleCalendarEventProcessor
from .api_client import GoogleCalendarAPIClient

__all__ = [
    "GoogleCalendarAdapter",
    "GoogleCalendarOAuthHandler",
    "GoogleCalendarWebhookManager",
    "GoogleCalendarEventProcessor",
    "GoogleCalendarAPIClient",
]
