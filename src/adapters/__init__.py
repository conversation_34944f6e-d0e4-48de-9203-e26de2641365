"""
Adapters package for the Trigger Service.

This package contains the adapter/connector pattern implementation for
different trigger types. Each adapter implements the BaseTriggerAdapter
interface to provide consistent trigger functionality.
"""

from .base import (
    BaseTriggerAdapter,
    TriggerEvent,
    TriggerEventType,
    TriggerStatus,
    TriggerConfiguration,
    AdapterHealthStatus,
)
from .google_calendar import GoogleCalendarAdapter
from .google_calendar_modular import ModularGoogleCalendarAdapter
from .factory import AdapterFactory, AdapterRegistry, get_adapter_factory

__all__ = [
    "BaseTriggerAdapter",
    "TriggerEvent",
    "TriggerEventType",
    "TriggerStatus",
    "TriggerConfiguration",
    "AdapterHealthStatus",
    "GoogleCalendarAdapter",
    "ModularGoogleCalendarAdapter",
    "AdapterFactory",
    "AdapterRegistry",
    "get_adapter_factory",
]
