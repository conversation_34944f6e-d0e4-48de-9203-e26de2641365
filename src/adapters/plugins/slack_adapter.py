"""
Example Slack adapter plugin demonstrating the extensible interface.

This module shows how to implement a new adapter using the plugin
architecture and extensible interface.
"""

from typing import Any, Dict, List, Optional, Set
from uuid import UUID
from datetime import datetime, timezone

from src.adapters.interface import (
    IAdapterPlugin,
    IWebhookHandler,
    IAuthenticationHandler,
    AdapterMetadata,
    AdapterCategory,
    AdapterCapability,
    ExtensibleAdapter,
)
from src.adapters.base import (
    BaseTriggerAdapter,
    TriggerEvent,
    TriggerEventType,
    TriggerConfiguration,
)
from src.utils.logger import get_logger

logger = get_logger(__name__)


class SlackAuthHandler(IAuthenticationHandler):
    """Authentication handler for Slack integration."""
    
    async def authenticate(self, user_id: str, credentials: Dict[str, Any]) -> bool:
        """
        Authenticate with Slack using OAuth or bot token.
        
        Args:
            user_id: User ID
            credentials: Slack credentials (bot_token, user_token, etc.)
            
        Returns:
            bool: True if authentication successful
        """
        try:
            # Validate required credentials
            bot_token = credentials.get("bot_token")
            if not bot_token:
                logger.error("Bot token is required for Slack authentication")
                return False
            
            # Test API access
            # In a real implementation, you would make an API call to Slack
            # to verify the token is valid
            
            logger.info(f"Slack authentication successful for user {user_id}")
            return True
            
        except Exception as e:
            logger.error(f"Slack authentication failed for user {user_id}: {e}")
            return False
    
    async def refresh_credentials(self, user_id: str) -> bool:
        """
        Refresh Slack credentials if needed.
        
        Args:
            user_id: User ID
            
        Returns:
            bool: True if refresh successful
        """
        # Slack bot tokens don't typically need refreshing
        # OAuth user tokens might need refreshing
        return True
    
    async def validate_credentials(self, user_id: str) -> bool:
        """
        Validate Slack credentials.
        
        Args:
            user_id: User ID
            
        Returns:
            bool: True if credentials are valid
        """
        try:
            # In a real implementation, make an API call to test the token
            return True
        except Exception as e:
            logger.error(f"Slack credential validation failed for user {user_id}: {e}")
            return False
    
    async def revoke_credentials(self, user_id: str) -> bool:
        """
        Revoke Slack credentials.
        
        Args:
            user_id: User ID
            
        Returns:
            bool: True if revocation successful
        """
        try:
            # In a real implementation, revoke the token via Slack API
            logger.info(f"Slack credentials revoked for user {user_id}")
            return True
        except Exception as e:
            logger.error(f"Failed to revoke Slack credentials for user {user_id}: {e}")
            return False


class SlackWebhookHandler(IWebhookHandler):
    """Webhook handler for Slack events."""
    
    def __init__(self):
        """Initialize the webhook handler."""
        self._active_webhooks: Dict[str, Dict[str, Any]] = {}
    
    async def create_webhook(
        self,
        user_id: str,
        trigger_id: UUID,
        config: Dict[str, Any]
    ) -> Optional[str]:
        """
        Create a Slack webhook subscription.
        
        Args:
            user_id: User ID
            trigger_id: Trigger ID
            config: Webhook configuration
            
        Returns:
            Optional[str]: Webhook ID if successful
        """
        try:
            # In a real implementation, you would:
            # 1. Register webhook URL with Slack Events API
            # 2. Subscribe to specific event types
            # 3. Store webhook configuration
            
            webhook_id = f"slack-{trigger_id}"
            
            webhook_config = {
                "user_id": user_id,
                "trigger_id": trigger_id,
                "event_types": config.get("event_types", ["message"]),
                "channel": config.get("channel"),
                "created_at": datetime.now(timezone.utc),
            }
            
            self._active_webhooks[webhook_id] = webhook_config
            
            logger.info(f"Created Slack webhook: {webhook_id}")
            return webhook_id
            
        except Exception as e:
            logger.error(f"Failed to create Slack webhook: {e}")
            return None
    
    async def delete_webhook(self, webhook_id: str) -> bool:
        """
        Delete a Slack webhook subscription.
        
        Args:
            webhook_id: Webhook ID
            
        Returns:
            bool: True if deletion successful
        """
        try:
            if webhook_id in self._active_webhooks:
                # In a real implementation, unsubscribe from Slack Events API
                del self._active_webhooks[webhook_id]
                logger.info(f"Deleted Slack webhook: {webhook_id}")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to delete Slack webhook {webhook_id}: {e}")
            return False
    
    async def process_webhook_event(
        self,
        webhook_id: str,
        headers: Dict[str, str],
        body: Any
    ) -> Optional[TriggerEvent]:
        """
        Process a Slack webhook event.
        
        Args:
            webhook_id: Webhook ID
            headers: Request headers
            body: Request body
            
        Returns:
            Optional[TriggerEvent]: Processed trigger event
        """
        try:
            webhook_config = self._active_webhooks.get(webhook_id)
            if not webhook_config:
                logger.warning(f"Unknown webhook ID: {webhook_id}")
                return None
            
            # Parse Slack event
            if isinstance(body, dict):
                event_type = body.get("type")
                event_data = body.get("event", {})
                
                # Map Slack event to trigger event
                trigger_event_type = self._map_slack_event_type(event_type)
                if not trigger_event_type:
                    return None
                
                # Create trigger event
                trigger_event = TriggerEvent(
                    event_id=f"slack-{body.get('event_id', 'unknown')}",
                    event_type=trigger_event_type,
                    source="slack",
                    timestamp=datetime.now(timezone.utc),
                    data={
                        "webhook_id": webhook_id,
                        "slack_event": event_data,
                        "channel": event_data.get("channel"),
                        "user": event_data.get("user"),
                        "text": event_data.get("text"),
                        "ts": event_data.get("ts"),
                    },
                    metadata={
                        "trigger_id": str(webhook_config["trigger_id"]),
                        "user_id": webhook_config["user_id"],
                    }
                )
                
                logger.info(f"Processed Slack webhook event: {event_type}")
                return trigger_event
            
            return None
            
        except Exception as e:
            logger.error(f"Error processing Slack webhook event: {e}")
            return None
    
    def validate_webhook_signature(
        self,
        headers: Dict[str, str],
        body: bytes,
        secret: str
    ) -> bool:
        """
        Validate Slack webhook signature.
        
        Args:
            headers: Request headers
            body: Request body
            secret: Webhook secret
            
        Returns:
            bool: True if signature is valid
        """
        try:
            # Slack signature validation
            timestamp = headers.get("x-slack-request-timestamp", "")
            signature = headers.get("x-slack-signature", "")
            
            if not timestamp or not signature:
                return False
            
            # In a real implementation, validate the signature using HMAC
            # This is a simplified version
            return True
            
        except Exception as e:
            logger.error(f"Error validating Slack webhook signature: {e}")
            return False
    
    def _map_slack_event_type(self, slack_event_type: str) -> Optional[TriggerEventType]:
        """Map Slack event type to trigger event type."""
        mapping = {
            "message": TriggerEventType.CREATED,
            "message.channels": TriggerEventType.CREATED,
            "message.groups": TriggerEventType.CREATED,
            "message.im": TriggerEventType.CREATED,
            "message.mpim": TriggerEventType.CREATED,
            "reaction_added": TriggerEventType.UPDATED,
            "reaction_removed": TriggerEventType.UPDATED,
        }
        
        return mapping.get(slack_event_type)


class SlackAdapter(ExtensibleAdapter):
    """Slack adapter implementation using the extensible interface."""
    
    def __init__(self):
        """Initialize the Slack adapter."""
        # Create handlers
        auth_handler = SlackAuthHandler()
        webhook_handler = SlackWebhookHandler()
        
        # Define metadata
        metadata = AdapterMetadata(
            name="slack",
            display_name="Slack",
            description="Slack messaging platform integration",
            version="1.0.0",
            category=AdapterCategory.MESSAGING,
            capabilities={
                AdapterCapability.WEBHOOKS,
                AdapterCapability.OAUTH,
                AdapterCapability.REAL_TIME,
            },
            supported_event_types={
                TriggerEventType.CREATED,
                TriggerEventType.UPDATED,
            },
            config_schema={
                "type": "object",
                "properties": {
                    "bot_token": {
                        "type": "string",
                        "description": "Slack bot token"
                    },
                    "channel": {
                        "type": "string",
                        "description": "Slack channel to monitor"
                    },
                    "event_types": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Event types to monitor"
                    }
                },
                "required": ["bot_token"]
            },
            documentation_url="https://api.slack.com/events-api",
            examples=[
                {
                    "bot_token": "xoxb-your-bot-token",
                    "channel": "#general",
                    "event_types": ["message", "reaction_added"]
                }
            ],
            required_permissions=["channels:read", "chat:write"],
            rate_limits={
                "requests_per_minute": 60,
                "burst_limit": 100
            }
        )
        
        super().__init__(
            adapter_name="slack",
            metadata=metadata,
            auth_handler=auth_handler,
            webhook_handler=webhook_handler
        )


class SlackAdapterPlugin(IAdapterPlugin):
    """Slack adapter plugin implementation."""
    
    @property
    def metadata(self) -> AdapterMetadata:
        """Get adapter metadata."""
        return AdapterMetadata(
            name="slack",
            display_name="Slack",
            description="Slack messaging platform integration",
            version="1.0.0",
            category=AdapterCategory.MESSAGING,
            capabilities={
                AdapterCapability.WEBHOOKS,
                AdapterCapability.OAUTH,
                AdapterCapability.REAL_TIME,
            },
            supported_event_types={
                TriggerEventType.CREATED,
                TriggerEventType.UPDATED,
            },
            config_schema={
                "type": "object",
                "properties": {
                    "bot_token": {
                        "type": "string",
                        "description": "Slack bot token"
                    },
                    "channel": {
                        "type": "string",
                        "description": "Slack channel to monitor"
                    },
                    "event_types": {
                        "type": "array",
                        "items": {"type": "string"},
                        "description": "Event types to monitor"
                    }
                },
                "required": ["bot_token"]
            },
            documentation_url="https://api.slack.com/events-api",
            examples=[
                {
                    "bot_token": "xoxb-your-bot-token",
                    "channel": "#general",
                    "event_types": ["message", "reaction_added"]
                }
            ]
        )
    
    async def create_adapter(self, **kwargs) -> BaseTriggerAdapter:
        """Create a Slack adapter instance."""
        return SlackAdapter()
    
    def validate_configuration(self, config: Dict[str, Any]) -> bool:
        """Validate Slack adapter configuration."""
        try:
            from jsonschema import validate
            validate(config, self.metadata.config_schema)
            return True
        except Exception as e:
            logger.error(f"Slack configuration validation failed: {e}")
            return False
    
    def get_configuration_schema(self) -> Dict[str, Any]:
        """Get configuration schema."""
        return self.metadata.config_schema
    
    def get_sample_configuration(self) -> Dict[str, Any]:
        """Get sample configuration."""
        return self.metadata.examples[0] if self.metadata.examples else {}
    
    async def initialize(self) -> None:
        """Initialize the plugin."""
        logger.info("Slack adapter plugin initialized")
    
    async def cleanup(self) -> None:
        """Cleanup the plugin."""
        logger.info("Slack adapter plugin cleaned up")
    
    def get_health_check_info(self) -> Dict[str, Any]:
        """Get health check information."""
        return {
            "supports_health_check": True,
            "health_check_interval": 300,  # 5 minutes
            "health_check_timeout": 10,    # 10 seconds
        }
