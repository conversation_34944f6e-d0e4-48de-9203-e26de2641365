"""
Modular Google Calendar Adapter for the Trigger Service.

This module implements a refactored Google Calendar adapter using
modular components for better maintainability and extensibility.
"""

from datetime import datetime, timezone
from typing import Any, Dict, List, Optional, Set
from uuid import UUID

from src.adapters.base import (
    BaseTriggerAdapter,
    TriggerEvent,
    TriggerEventType,
    TriggerConfiguration,
    AdapterHealthStatus,
)
from src.adapters.google_calendar import (
    GoogleCalendarOAuthHandler,
    GoogleCalendarWebhookManager,
    GoogleCalendarEventProcessor,
    GoogleCalendarAPIClient,
)
from src.core.exceptions import (
    GoogleCalendarError,
    GoogleCalendarAuthError,
    GoogleCalendarAPIError,
    GoogleCalendarWebhookError,
    AdapterConfigurationError,
    ValidationError,
)
from src.utils.config import get_settings
from src.utils.logger import get_logger
from src.utils.retry import RetryHandler

logger = get_logger(__name__)


class ModularGoogleCalendarAdapter(BaseTriggerAdapter):
    """
    Modular Google Calendar adapter for monitoring calendar events.

    This adapter uses separate components for OAuth, webhooks, event processing,
    and API operations to provide better separation of concerns and maintainability.
    """

    def __init__(self):
        """Initialize the modular Google Calendar adapter."""
        super().__init__("google_calendar")
        self.settings = get_settings()

        # Initialize modular components
        self.oauth_handler = GoogleCalendarOAuthHandler()
        self.webhook_manager = GoogleCalendarWebhookManager(self.oauth_handler)
        self.api_client = GoogleCalendarAPIClient(self.oauth_handler)
        self.event_processor = GoogleCalendarEventProcessor(
            self.oauth_handler, self.api_client
        )

        # General retry handler
        self.retry_handler = RetryHandler(
            max_attempts=3, base_delay=1.0, backoff_factor=2.0
        )

        logger.info("Modular Google Calendar adapter initialized")

    @property
    def supported_event_types(self) -> Set[TriggerEventType]:
        """
        Get the set of event types supported by this adapter.

        Returns:
            Set[TriggerEventType]: Supported event types
        """
        return {
            TriggerEventType.CREATED,
            TriggerEventType.UPDATED,
            TriggerEventType.DELETED,
        }

    async def validate_config(self, config: Dict[str, Any]) -> bool:
        """
        Validate adapter-specific configuration.

        Args:
            config: Configuration to validate

        Returns:
            bool: True if configuration is valid, False otherwise
        """
        try:
            # Check required fields
            calendar_id = config.get("calendar_id")
            if not calendar_id or not isinstance(calendar_id, str):
                raise AdapterConfigurationError(
                    "calendar_id is required and must be a string",
                    details={"provided_calendar_id": calendar_id},
                )

            # Validate webhook_ttl if provided
            if "webhook_ttl" in config:
                webhook_ttl = config["webhook_ttl"]
                if not isinstance(webhook_ttl, int) or webhook_ttl <= 0:
                    raise AdapterConfigurationError(
                        "webhook_ttl must be a positive integer",
                        details={"provided_webhook_ttl": webhook_ttl},
                    )

            logger.debug(
                f"Configuration validation passed for calendar_id: {calendar_id}"
            )
            return True

        except AdapterConfigurationError:
            raise
        except Exception as e:
            raise AdapterConfigurationError(
                f"Error validating configuration: {str(e)}", cause=e
            )

    async def setup_trigger(
        self, trigger_config: TriggerConfiguration, session=None
    ) -> bool:
        """
        Set up a new Google Calendar trigger with webhook subscription.

        Args:
            trigger_config: Complete trigger configuration
            session: Optional database session

        Returns:
            bool: True if setup was successful, False otherwise
        """
        try:
            user_id = trigger_config.user_id
            calendar_id = trigger_config.config.get("calendar_id")
            webhook_ttl = trigger_config.config.get("webhook_ttl", 3600)

            # Validate user credentials
            if not await self.oauth_handler.validate_credentials(user_id):
                logger.error(f"Invalid credentials for user {user_id}")
                return False

            # Test calendar access
            calendar_info = await self.api_client.get_calendar(user_id, calendar_id)
            if not calendar_info:
                logger.error(f"Cannot access calendar {calendar_id} for user {user_id}")
                return False

            # Create webhook subscription
            channel_id = await self.webhook_manager.create_webhook_subscription(
                user_id=user_id,
                calendar_id=calendar_id,
                trigger_id=trigger_config.trigger_id,
                event_types=[et.value for et in trigger_config.event_types],
                webhook_ttl=webhook_ttl,
                session=session,
            )

            if not channel_id:
                logger.error(
                    f"Failed to create webhook subscription for trigger {trigger_config.trigger_id}"
                )
                return False

            logger.info(
                f"Successfully set up Google Calendar trigger",
                trigger_id=trigger_config.trigger_id,
                user_id=user_id,
                calendar_id=calendar_id,
                channel_id=channel_id,
            )

            return True

        except Exception as e:
            logger.error(f"Error setting up trigger {trigger_config.trigger_id}: {e}")
            return False

    async def remove_trigger(self, trigger_id: UUID) -> bool:
        """
        Remove a trigger from the external service.

        Args:
            trigger_id: Unique identifier for the trigger to remove

        Returns:
            bool: True if removal was successful, False otherwise
        """
        try:
            # Find the channel associated with this trigger
            active_channels = self.webhook_manager.list_active_channels()
            channel_to_remove = None

            for channel in active_channels:
                if str(channel.get("trigger_id")) == str(trigger_id):
                    channel_to_remove = channel["channel_id"]
                    break

            if not channel_to_remove:
                logger.warning(f"No active channel found for trigger {trigger_id}")
                return True  # Consider it successful if no channel exists

            # Stop the webhook channel
            success = await self.webhook_manager.stop_webhook_channel(channel_to_remove)

            if success:
                logger.info(f"Successfully removed trigger {trigger_id}")
            else:
                logger.error(f"Failed to remove trigger {trigger_id}")

            return success

        except Exception as e:
            logger.error(f"Error removing trigger {trigger_id}: {e}")
            return False

    async def process_event(self, raw_event: Dict[str, Any]) -> Optional[TriggerEvent]:
        """
        Process a raw event from the external service.

        Args:
            raw_event: Raw event data from the external service

        Returns:
            TriggerEvent: Standardized event data, or None if event should be ignored
        """
        try:
            # Add channel info to raw event for processing
            channel_id = raw_event.get("channel_id")
            if channel_id:
                channel_info = self.webhook_manager.get_channel_info(channel_id)
                if channel_info:
                    raw_event["channel_info"] = channel_info

            # Process the event using the event processor
            trigger_event = await self.event_processor.process_webhook_event(raw_event)

            if trigger_event:
                logger.info(
                    f"Processed Google Calendar event",
                    event_type=trigger_event.event_type,
                    event_id=trigger_event.event_id,
                )

            return trigger_event

        except Exception as e:
            logger.error(f"Error processing event: {e}")
            return None

    async def _perform_health_check(self) -> bool:
        """
        Perform health check for Google Calendar adapter.

        Returns:
            bool: True if healthy, False otherwise
        """
        try:
            # Test basic API connectivity
            import httpx

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://www.googleapis.com/calendar/v3/users/me/calendarList",
                    timeout=10.0,
                )
                # We expect 401 since we're not authenticated, but service should be reachable
                api_reachable = response.status_code in [200, 401, 403]

            if not api_reachable:
                logger.error("Google Calendar API is not reachable")
                return False

            # Check webhook manager status
            active_channels = self.webhook_manager.list_active_channels()
            logger.debug(
                f"Health check: {len(active_channels)} active webhook channels"
            )

            return True

        except Exception as e:
            logger.error(f"Google Calendar health check failed: {e}")
            return False

    def get_configuration_schema(self) -> Dict[str, Any]:
        """
        Get the JSON schema for Google Calendar adapter configuration.

        Returns:
            Dict[str, Any]: JSON schema for configuration validation
        """
        return {
            "type": "object",
            "properties": {
                "calendar_id": {
                    "type": "string",
                    "description": "Google Calendar ID to monitor",
                    "examples": ["primary", "<EMAIL>"],
                },
                "webhook_ttl": {
                    "type": "integer",
                    "description": "Webhook subscription TTL in seconds",
                    "default": 3600,
                    "minimum": 300,
                    "maximum": 86400,
                },
            },
            "required": ["calendar_id"],
            "additionalProperties": False,
        }

    def get_sample_event_data(self) -> Dict[str, Any]:
        """
        Get sample event data for Google Calendar adapter.

        Returns:
            Dict[str, Any]: Sample event data structure
        """
        return {
            "event_id": "gcal-resource123-abc12345",
            "event_type": "updated",
            "source": "google_calendar",
            "timestamp": datetime.now(timezone.utc).isoformat(),
            "data": {
                "channel_id": "trigger-uuid-abc12345",
                "resource_id": "resource123",
                "resource_state": "exists",
                "user_id": "user123",
                "calendar_id": "primary",
                "events": [
                    {
                        "id": "event123",
                        "summary": "Sample Meeting",
                        "start": {"dateTime": "2024-01-01T10:00:00Z"},
                        "end": {"dateTime": "2024-01-01T11:00:00Z"},
                    }
                ],
            },
        }

    def get_available_fields(self) -> List[Dict[str, str]]:
        """
        Get available fields for Google Calendar adapter.

        Returns:
            List[Dict[str, str]]: List of available fields for mapping
        """
        return [
            {"field": "event.id", "description": "Calendar event ID", "type": "string"},
            {
                "field": "event.summary",
                "description": "Event title/summary",
                "type": "string",
            },
            {
                "field": "event.description",
                "description": "Event description",
                "type": "string",
            },
            {
                "field": "event.start.dateTime",
                "description": "Event start time",
                "type": "datetime",
            },
            {
                "field": "event.end.dateTime",
                "description": "Event end time",
                "type": "datetime",
            },
            {
                "field": "event.location",
                "description": "Event location",
                "type": "string",
            },
            {
                "field": "event.creator.email",
                "description": "Event creator email",
                "type": "string",
            },
            {
                "field": "event.organizer.email",
                "description": "Event organizer email",
                "type": "string",
            },
            {"field": "calendar_id", "description": "Calendar ID", "type": "string"},
            {"field": "user_id", "description": "User ID", "type": "string"},
        ]

    async def cleanup(self) -> None:
        """
        Cleanup adapter resources.

        This method should be called during application shutdown.
        """
        try:
            # Cleanup all components
            await self.webhook_manager.cleanup()
            await self.oauth_handler.cleanup()
            await self.api_client.cleanup()
            await self.event_processor.cleanup()

            logger.info("Modular Google Calendar adapter cleaned up")

        except Exception as e:
            logger.error(f"Error cleaning up adapter: {e}")

    # Additional utility methods
    async def list_user_calendars(self, user_id: str) -> List[Dict[str, Any]]:
        """
        List calendars for a user.

        Args:
            user_id: User ID

        Returns:
            List[Dict[str, Any]]: List of user's calendars
        """
        return await self.api_client.list_calendars(user_id)

    async def test_user_access(self, user_id: str) -> bool:
        """
        Test API access for a user.

        Args:
            user_id: User ID

        Returns:
            bool: True if user has valid access, False otherwise
        """
        return await self.api_client.test_api_access(user_id)

    async def cleanup_expired_webhooks(self) -> int:
        """
        Clean up expired webhook channels.

        Returns:
            int: Number of channels cleaned up
        """
        return await self.webhook_manager.cleanup_expired_channels()
