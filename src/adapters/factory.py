"""
Adapter factory for managing trigger adapter instances.

This module provides a factory pattern for creating and managing
trigger adapters, making it easy to add new integrations.
"""

from typing import Dict, Type, Optional, List, Any
from abc import ABC, abstractmethod

from src.adapters.base import BaseTriggerAdapter
from src.adapters.interface import AdapterMetadata, AdapterCategory, AdapterCapability
from src.utils.logger import get_logger

logger = get_logger(__name__)


class AdapterRegistry:
    """
    Registry for managing adapter types and their configurations.

    This class maintains a registry of available adapter types and
    provides methods for registering new adapter types.
    """

    def __init__(self):
        self._adapter_types: Dict[str, Type[BaseTriggerAdapter]] = {}
        self._adapter_configs: Dict[str, Dict[str, Any]] = {}

    def register_adapter_type(
        self,
        adapter_name: str,
        adapter_class: Type[BaseTriggerAdapter],
        config: Optional[Dict[str, Any]] = None,
    ) -> None:
        """
        Register an adapter type.

        Args:
            adapter_name: Unique name for the adapter
            adapter_class: Adapter class to register
            config: Optional configuration for the adapter
        """
        if adapter_name in self._adapter_types:
            logger.warning(
                f"Adapter type '{adapter_name}' already registered, overwriting"
            )

        self._adapter_types[adapter_name] = adapter_class
        self._adapter_configs[adapter_name] = config or {}

        logger.info(f"Registered adapter type: {adapter_name}")

    def get_adapter_type(self, adapter_name: str) -> Optional[Type[BaseTriggerAdapter]]:
        """
        Get an adapter type by name.

        Args:
            adapter_name: Name of the adapter type

        Returns:
            Type[BaseTriggerAdapter]: Adapter class if found, None otherwise
        """
        return self._adapter_types.get(adapter_name)

    def get_adapter_config(self, adapter_name: str) -> Dict[str, Any]:
        """
        Get adapter configuration by name.

        Args:
            adapter_name: Name of the adapter

        Returns:
            Dict[str, Any]: Adapter configuration
        """
        return self._adapter_configs.get(adapter_name, {})

    def list_adapter_types(self) -> List[str]:
        """
        List all registered adapter types.

        Returns:
            List[str]: List of adapter type names
        """
        return list(self._adapter_types.keys())

    def unregister_adapter_type(self, adapter_name: str) -> bool:
        """
        Unregister an adapter type.

        Args:
            adapter_name: Name of the adapter to unregister

        Returns:
            bool: True if unregistered, False if not found
        """
        if adapter_name in self._adapter_types:
            del self._adapter_types[adapter_name]
            self._adapter_configs.pop(adapter_name, None)
            logger.info(f"Unregistered adapter type: {adapter_name}")
            return True
        return False


class AdapterFactory:
    """
    Factory for creating and managing adapter instances.

    This factory provides a centralized way to create adapter instances
    and manage their lifecycle.
    """

    def __init__(self, registry: Optional[AdapterRegistry] = None):
        """
        Initialize the adapter factory.

        Args:
            registry: Adapter registry to use, creates new one if None
        """
        self._registry = registry or AdapterRegistry()
        self._instances: Dict[str, BaseTriggerAdapter] = {}
        self._plugin_registry = None
        self._register_default_adapters()

    def _register_default_adapters(self) -> None:
        """Register default adapter types."""
        try:
            # Register Google Calendar adapter (modular version)
            from src.adapters.google_calendar_modular import (
                ModularGoogleCalendarAdapter,
            )

            self._registry.register_adapter_type(
                "google_calendar", ModularGoogleCalendarAdapter
            )

            # Register Google Drive adapters if available
            try:
                from src.adapters.google_drive_user import GoogleDriveUserAdapter

                self._registry.register_adapter_type(
                    "google_drive_user", GoogleDriveUserAdapter
                )
            except ImportError:
                logger.debug("Google Drive User adapter not available")

            try:
                from src.adapters.google_drive_service_account import (
                    GoogleDriveServiceAccountAdapter,
                )

                self._registry.register_adapter_type(
                    "google_drive_service_account", GoogleDriveServiceAccountAdapter
                )
            except ImportError:
                logger.debug("Google Drive Service Account adapter not available")

            logger.info("Default adapters registered successfully")

        except Exception as e:
            logger.error(f"Failed to register default adapters: {e}")

    def create_adapter(
        self, adapter_name: str, **kwargs
    ) -> Optional[BaseTriggerAdapter]:
        """
        Create an adapter instance.

        Args:
            adapter_name: Name of the adapter to create
            **kwargs: Additional arguments to pass to adapter constructor

        Returns:
            BaseTriggerAdapter: Adapter instance if successful, None otherwise
        """
        try:
            adapter_class = self._registry.get_adapter_type(adapter_name)
            if not adapter_class:
                logger.error(f"Unknown adapter type: {adapter_name}")
                return None

            # Get adapter configuration
            config = self._registry.get_adapter_config(adapter_name)

            # Merge config with kwargs
            merged_kwargs = {**config, **kwargs}

            # Create adapter instance
            adapter = adapter_class(**merged_kwargs)

            logger.info(f"Created adapter instance: {adapter_name}")
            return adapter

        except Exception as e:
            logger.error(f"Failed to create adapter '{adapter_name}': {e}")
            return None

    def get_or_create_adapter(
        self, adapter_name: str, **kwargs
    ) -> Optional[BaseTriggerAdapter]:
        """
        Get existing adapter instance or create a new one.

        Args:
            adapter_name: Name of the adapter
            **kwargs: Additional arguments for adapter creation

        Returns:
            BaseTriggerAdapter: Adapter instance if successful, None otherwise
        """
        # Check if instance already exists
        if adapter_name in self._instances:
            return self._instances[adapter_name]

        # Create new instance
        adapter = self.create_adapter(adapter_name, **kwargs)
        if adapter:
            self._instances[adapter_name] = adapter

        return adapter

    def get_adapter(self, adapter_name: str) -> Optional[BaseTriggerAdapter]:
        """
        Get an existing adapter instance.

        Args:
            adapter_name: Name of the adapter

        Returns:
            BaseTriggerAdapter: Adapter instance if found, None otherwise
        """
        return self._instances.get(adapter_name)

    def list_adapters(self) -> List[str]:
        """
        List all available adapter types.

        Returns:
            List[str]: List of adapter type names
        """
        return self._registry.list_adapter_types()

    def list_instances(self) -> List[str]:
        """
        List all created adapter instances.

        Returns:
            List[str]: List of adapter instance names
        """
        return list(self._instances.keys())

    def remove_instance(self, adapter_name: str) -> bool:
        """
        Remove an adapter instance.

        Args:
            adapter_name: Name of the adapter to remove

        Returns:
            bool: True if removed, False if not found
        """
        if adapter_name in self._instances:
            del self._instances[adapter_name]
            logger.info(f"Removed adapter instance: {adapter_name}")
            return True
        return False

    def get_registry(self) -> AdapterRegistry:
        """
        Get the adapter registry.

        Returns:
            AdapterRegistry: The adapter registry instance
        """
        return self._registry

    def enable_plugin_system(self) -> None:
        """Enable the plugin system for dynamic adapter loading."""
        try:
            from src.adapters.plugin_registry import get_plugin_registry

            self._plugin_registry = get_plugin_registry()
            logger.info("Plugin system enabled")
        except ImportError:
            logger.warning("Plugin system not available")

    async def discover_plugins(self) -> int:
        """
        Discover and register plugins.

        Returns:
            int: Number of plugins discovered
        """
        if not self._plugin_registry:
            self.enable_plugin_system()

        if self._plugin_registry:
            return self._plugin_registry.discover_plugins()
        return 0

    async def cleanup_all(self) -> None:
        """
        Cleanup all adapter instances.

        This method should be called during application shutdown.
        """
        try:
            for adapter_name, adapter in self._instances.items():
                if hasattr(adapter, "cleanup"):
                    await adapter.cleanup()
                logger.debug(f"Cleaned up adapter: {adapter_name}")

            self._instances.clear()

            # Cleanup plugin registry
            if self._plugin_registry:
                await self._plugin_registry.cleanup_all()

            logger.info("All adapter instances cleaned up")

        except Exception as e:
            logger.error(f"Failed to cleanup adapters: {e}")


# Global adapter factory instance
_adapter_factory: Optional[AdapterFactory] = None


def get_adapter_factory() -> AdapterFactory:
    """
    Get the global adapter factory instance.

    Returns:
        AdapterFactory: The global adapter factory
    """
    global _adapter_factory
    if _adapter_factory is None:
        _adapter_factory = AdapterFactory()
    return _adapter_factory
