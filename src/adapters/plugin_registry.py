"""
Plugin registry and discovery system for adapters.

This module provides a comprehensive plugin system for discovering,
loading, and managing adapter plugins dynamically.
"""

import importlib
import inspect
import pkgutil
from typing import Dict, List, Optional, Type, Any, Set
from pathlib import Path
from dataclasses import dataclass
from datetime import datetime

from src.adapters.interface import (
    IAdapterPlugin,
    AdapterMetadata,
    AdapterCategory,
    AdapterCapability,
)
from src.adapters.base import BaseTriggerAdapter
from src.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class PluginInfo:
    """Information about a registered plugin."""
    
    plugin_class: Type[IAdapterPlugin]
    metadata: AdapterMetadata
    module_path: str
    loaded_at: datetime
    instance: Optional[IAdapterPlugin] = None
    is_active: bool = True
    load_error: Optional[str] = None


class AdapterPluginRegistry:
    """
    Registry for adapter plugins with discovery and management capabilities.
    
    This class provides a centralized way to discover, load, and manage
    adapter plugins with proper lifecycle management.
    """
    
    def __init__(self):
        """Initialize the plugin registry."""
        self._plugins: Dict[str, PluginInfo] = {}
        self._categories: Dict[AdapterCategory, Set[str]] = {}
        self._capabilities: Dict[AdapterCapability, Set[str]] = {}
        self._search_paths: List[Path] = []
        
        # Add default search paths
        self._add_default_search_paths()
        
        logger.info("Plugin registry initialized")
    
    def _add_default_search_paths(self) -> None:
        """Add default search paths for plugins."""
        # Built-in adapters
        builtin_path = Path(__file__).parent
        self._search_paths.append(builtin_path)
        
        # External plugins directory
        external_path = Path("plugins/adapters")
        if external_path.exists():
            self._search_paths.append(external_path)
        
        # User plugins directory
        user_path = Path.home() / ".trigger-service" / "plugins"
        if user_path.exists():
            self._search_paths.append(user_path)
    
    def add_search_path(self, path: Path) -> None:
        """
        Add a search path for plugins.
        
        Args:
            path: Path to search for plugins
        """
        if path.exists() and path not in self._search_paths:
            self._search_paths.append(path)
            logger.info(f"Added plugin search path: {path}")
    
    def discover_plugins(self) -> int:
        """
        Discover plugins in all search paths.
        
        Returns:
            int: Number of plugins discovered
        """
        discovered_count = 0
        
        for search_path in self._search_paths:
            try:
                discovered_count += self._discover_plugins_in_path(search_path)
            except Exception as e:
                logger.error(f"Error discovering plugins in {search_path}: {e}")
        
        logger.info(f"Discovered {discovered_count} plugins")
        return discovered_count
    
    def _discover_plugins_in_path(self, path: Path) -> int:
        """
        Discover plugins in a specific path.
        
        Args:
            path: Path to search
            
        Returns:
            int: Number of plugins discovered
        """
        discovered_count = 0
        
        # Look for Python modules
        for module_info in pkgutil.iter_modules([str(path)]):
            try:
                module_name = module_info.name
                module_path = f"{path.name}.{module_name}"
                
                # Import the module
                module = importlib.import_module(module_path)
                
                # Look for plugin classes
                for name, obj in inspect.getmembers(module, inspect.isclass):
                    if (obj != IAdapterPlugin and 
                        issubclass(obj, IAdapterPlugin) and 
                        not inspect.isabstract(obj)):
                        
                        try:
                            # Create instance to get metadata
                            plugin_instance = obj()
                            metadata = plugin_instance.metadata
                            
                            # Register the plugin
                            self._register_plugin(
                                plugin_name=metadata.name,
                                plugin_class=obj,
                                metadata=metadata,
                                module_path=module_path
                            )
                            
                            discovered_count += 1
                            logger.debug(f"Discovered plugin: {metadata.name}")
                            
                        except Exception as e:
                            logger.error(f"Error loading plugin {name}: {e}")
                            
            except Exception as e:
                logger.error(f"Error importing module {module_info.name}: {e}")
        
        return discovered_count
    
    def _register_plugin(
        self,
        plugin_name: str,
        plugin_class: Type[IAdapterPlugin],
        metadata: AdapterMetadata,
        module_path: str
    ) -> None:
        """
        Register a plugin.
        
        Args:
            plugin_name: Plugin name
            plugin_class: Plugin class
            metadata: Plugin metadata
            module_path: Module path
        """
        plugin_info = PluginInfo(
            plugin_class=plugin_class,
            metadata=metadata,
            module_path=module_path,
            loaded_at=datetime.utcnow()
        )
        
        self._plugins[plugin_name] = plugin_info
        
        # Index by category
        if metadata.category not in self._categories:
            self._categories[metadata.category] = set()
        self._categories[metadata.category].add(plugin_name)
        
        # Index by capabilities
        for capability in metadata.capabilities:
            if capability not in self._capabilities:
                self._capabilities[capability] = set()
            self._capabilities[capability].add(plugin_name)
    
    def register_plugin_class(
        self,
        plugin_class: Type[IAdapterPlugin],
        module_path: str = "manual"
    ) -> bool:
        """
        Manually register a plugin class.
        
        Args:
            plugin_class: Plugin class to register
            module_path: Module path for tracking
            
        Returns:
            bool: True if registration successful
        """
        try:
            # Create instance to get metadata
            plugin_instance = plugin_class()
            metadata = plugin_instance.metadata
            
            self._register_plugin(
                plugin_name=metadata.name,
                plugin_class=plugin_class,
                metadata=metadata,
                module_path=module_path
            )
            
            logger.info(f"Manually registered plugin: {metadata.name}")
            return True
            
        except Exception as e:
            logger.error(f"Error registering plugin class: {e}")
            return False
    
    def get_plugin(self, plugin_name: str) -> Optional[PluginInfo]:
        """
        Get plugin information.
        
        Args:
            plugin_name: Plugin name
            
        Returns:
            Optional[PluginInfo]: Plugin information if found
        """
        return self._plugins.get(plugin_name)
    
    def list_plugins(
        self,
        category: Optional[AdapterCategory] = None,
        capability: Optional[AdapterCapability] = None,
        active_only: bool = True
    ) -> List[PluginInfo]:
        """
        List plugins with optional filtering.
        
        Args:
            category: Optional category filter
            capability: Optional capability filter
            active_only: Whether to include only active plugins
            
        Returns:
            List[PluginInfo]: List of matching plugins
        """
        plugins = []
        
        # Get plugin names based on filters
        plugin_names = set(self._plugins.keys())
        
        if category:
            category_plugins = self._categories.get(category, set())
            plugin_names = plugin_names.intersection(category_plugins)
        
        if capability:
            capability_plugins = self._capabilities.get(capability, set())
            plugin_names = plugin_names.intersection(capability_plugins)
        
        # Filter by active status and collect plugin info
        for plugin_name in plugin_names:
            plugin_info = self._plugins[plugin_name]
            if not active_only or plugin_info.is_active:
                plugins.append(plugin_info)
        
        return plugins
    
    def get_plugin_metadata(self, plugin_name: str) -> Optional[AdapterMetadata]:
        """
        Get plugin metadata.
        
        Args:
            plugin_name: Plugin name
            
        Returns:
            Optional[AdapterMetadata]: Plugin metadata if found
        """
        plugin_info = self._plugins.get(plugin_name)
        return plugin_info.metadata if plugin_info else None
    
    async def load_plugin(self, plugin_name: str) -> Optional[IAdapterPlugin]:
        """
        Load and initialize a plugin.
        
        Args:
            plugin_name: Plugin name
            
        Returns:
            Optional[IAdapterPlugin]: Plugin instance if successful
        """
        plugin_info = self._plugins.get(plugin_name)
        if not plugin_info:
            logger.error(f"Plugin not found: {plugin_name}")
            return None
        
        if plugin_info.instance:
            return plugin_info.instance
        
        try:
            # Create plugin instance
            plugin_instance = plugin_info.plugin_class()
            
            # Initialize the plugin
            await plugin_instance.initialize()
            
            # Store the instance
            plugin_info.instance = plugin_instance
            plugin_info.is_active = True
            plugin_info.load_error = None
            
            logger.info(f"Loaded plugin: {plugin_name}")
            return plugin_instance
            
        except Exception as e:
            error_msg = f"Failed to load plugin {plugin_name}: {e}"
            logger.error(error_msg)
            plugin_info.load_error = error_msg
            plugin_info.is_active = False
            return None
    
    async def unload_plugin(self, plugin_name: str) -> bool:
        """
        Unload a plugin.
        
        Args:
            plugin_name: Plugin name
            
        Returns:
            bool: True if unload successful
        """
        plugin_info = self._plugins.get(plugin_name)
        if not plugin_info or not plugin_info.instance:
            return True
        
        try:
            # Cleanup the plugin
            await plugin_info.instance.cleanup()
            
            # Remove the instance
            plugin_info.instance = None
            plugin_info.is_active = False
            
            logger.info(f"Unloaded plugin: {plugin_name}")
            return True
            
        except Exception as e:
            logger.error(f"Error unloading plugin {plugin_name}: {e}")
            return False
    
    async def create_adapter(
        self,
        plugin_name: str,
        **kwargs
    ) -> Optional[BaseTriggerAdapter]:
        """
        Create an adapter instance from a plugin.
        
        Args:
            plugin_name: Plugin name
            **kwargs: Adapter configuration
            
        Returns:
            Optional[BaseTriggerAdapter]: Adapter instance if successful
        """
        plugin_instance = await self.load_plugin(plugin_name)
        if not plugin_instance:
            return None
        
        try:
            adapter = await plugin_instance.create_adapter(**kwargs)
            logger.info(f"Created adapter from plugin: {plugin_name}")
            return adapter
            
        except Exception as e:
            logger.error(f"Error creating adapter from plugin {plugin_name}: {e}")
            return None
    
    def get_registry_stats(self) -> Dict[str, Any]:
        """
        Get registry statistics.
        
        Returns:
            Dict[str, Any]: Registry statistics
        """
        active_plugins = sum(1 for p in self._plugins.values() if p.is_active)
        loaded_plugins = sum(1 for p in self._plugins.values() if p.instance)
        
        return {
            "total_plugins": len(self._plugins),
            "active_plugins": active_plugins,
            "loaded_plugins": loaded_plugins,
            "categories": {cat.value: len(plugins) for cat, plugins in self._categories.items()},
            "capabilities": {cap.value: len(plugins) for cap, plugins in self._capabilities.items()},
            "search_paths": [str(path) for path in self._search_paths],
        }
    
    async def cleanup_all(self) -> None:
        """Cleanup all loaded plugins."""
        for plugin_name in list(self._plugins.keys()):
            await self.unload_plugin(plugin_name)
        
        logger.info("All plugins cleaned up")


# Global plugin registry instance
_plugin_registry: Optional[AdapterPluginRegistry] = None


def get_plugin_registry() -> AdapterPluginRegistry:
    """
    Get the global plugin registry instance.
    
    Returns:
        AdapterPluginRegistry: Global plugin registry
    """
    global _plugin_registry
    if _plugin_registry is None:
        _plugin_registry = AdapterPluginRegistry()
    return _plugin_registry


async def discover_and_load_plugins() -> int:
    """
    Discover and load all available plugins.
    
    Returns:
        int: Number of plugins discovered
    """
    registry = get_plugin_registry()
    return registry.discover_plugins()
