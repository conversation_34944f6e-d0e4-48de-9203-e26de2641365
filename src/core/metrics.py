"""
Metrics collection for the Trigger Service.

This module provides metrics collection and monitoring capabilities
for various components of the trigger service.
"""

import time
from typing import Dict, Any, Optional
from dataclasses import dataclass, field
from datetime import datetime, timezone

from src.utils.logger import get_logger

logger = get_logger(__name__)


@dataclass
class TaskQueueMetrics:
    """Metrics for task queue operations."""

    # Queue size metrics
    pending_tasks: int = 0
    running_tasks: int = 0
    completed_tasks: int = 0
    failed_tasks: int = 0

    # Performance metrics
    average_processing_time: float = 0.0
    max_processing_time: float = 0.0
    min_processing_time: float = float("inf")

    # Throughput metrics
    tasks_per_second: float = 0.0
    tasks_per_minute: float = 0.0

    # Error metrics
    error_rate: float = 0.0
    retry_count: int = 0

    # Timing
    last_updated: datetime = field(default_factory=lambda: datetime.now(timezone.utc))

    def update_processing_time(self, processing_time: float):
        """Update processing time metrics."""
        self.max_processing_time = max(self.max_processing_time, processing_time)
        self.min_processing_time = min(self.min_processing_time, processing_time)

        # Simple moving average (could be improved with proper windowing)
        total_tasks = self.completed_tasks + self.failed_tasks
        if total_tasks > 0:
            self.average_processing_time = (
                self.average_processing_time * (total_tasks - 1) + processing_time
            ) / total_tasks

    def increment_completed(self):
        """Increment completed task count."""
        self.completed_tasks += 1
        self.last_updated = datetime.now(timezone.utc)

    def increment_failed(self):
        """Increment failed task count."""
        self.failed_tasks += 1
        self.last_updated = datetime.now(timezone.utc)

    def increment_retry(self):
        """Increment retry count."""
        self.retry_count += 1
        self.last_updated = datetime.now(timezone.utc)

    def calculate_error_rate(self) -> float:
        """Calculate current error rate."""
        total_tasks = self.completed_tasks + self.failed_tasks
        if total_tasks == 0:
            return 0.0
        return self.failed_tasks / total_tasks

    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary."""
        return {
            "pending_tasks": self.pending_tasks,
            "running_tasks": self.running_tasks,
            "completed_tasks": self.completed_tasks,
            "failed_tasks": self.failed_tasks,
            "average_processing_time": self.average_processing_time,
            "max_processing_time": self.max_processing_time,
            "min_processing_time": (
                self.min_processing_time
                if self.min_processing_time != float("inf")
                else 0.0
            ),
            "tasks_per_second": self.tasks_per_second,
            "tasks_per_minute": self.tasks_per_minute,
            "error_rate": self.calculate_error_rate(),
            "retry_count": self.retry_count,
            "last_updated": self.last_updated.isoformat(),
        }


@dataclass
class AdapterMetrics:
    """Metrics for adapter operations."""

    # Request metrics
    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0

    # Response time metrics
    average_response_time: float = 0.0
    max_response_time: float = 0.0
    min_response_time: float = float("inf")

    # Error metrics
    error_rate: float = 0.0
    timeout_count: int = 0

    # Health metrics
    health_check_count: int = 0
    health_check_failures: int = 0

    # Timing
    last_updated: datetime = field(default_factory=lambda: datetime.now(timezone.utc))

    def record_request(self, success: bool, response_time: float):
        """Record a request with its outcome and response time."""
        self.total_requests += 1

        if success:
            self.successful_requests += 1
        else:
            self.failed_requests += 1

        # Update response time metrics
        self.max_response_time = max(self.max_response_time, response_time)
        self.min_response_time = min(self.min_response_time, response_time)

        # Update average response time
        if self.total_requests > 0:
            self.average_response_time = (
                self.average_response_time * (self.total_requests - 1) + response_time
            ) / self.total_requests

        self.last_updated = datetime.now(timezone.utc)

    def record_health_check(self, success: bool):
        """Record a health check result."""
        self.health_check_count += 1
        if not success:
            self.health_check_failures += 1
        self.last_updated = datetime.now(timezone.utc)

    def calculate_error_rate(self) -> float:
        """Calculate current error rate."""
        if self.total_requests == 0:
            return 0.0
        return self.failed_requests / self.total_requests

    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary."""
        return {
            "total_requests": self.total_requests,
            "successful_requests": self.successful_requests,
            "failed_requests": self.failed_requests,
            "average_response_time": self.average_response_time,
            "max_response_time": self.max_response_time,
            "min_response_time": (
                self.min_response_time
                if self.min_response_time != float("inf")
                else 0.0
            ),
            "error_rate": self.calculate_error_rate(),
            "timeout_count": self.timeout_count,
            "health_check_count": self.health_check_count,
            "health_check_failures": self.health_check_failures,
            "last_updated": self.last_updated.isoformat(),
        }


@dataclass
class SchedulerMetrics:
    """Metrics for scheduler operations."""

    # Scheduler execution metrics
    total_executions: int = 0
    successful_executions: int = 0
    failed_executions: int = 0
    skipped_executions: int = 0

    # Timing metrics
    average_execution_time: float = 0.0
    max_execution_time: float = 0.0
    min_execution_time: float = float("inf")

    # Schedule metrics
    active_schedules: int = 0
    paused_schedules: int = 0
    error_schedules: int = 0

    # Timing
    last_updated: datetime = field(default_factory=lambda: datetime.now(timezone.utc))

    def record_execution(self, success: bool, execution_time: float):
        """Record a scheduler execution."""
        self.total_executions += 1

        if success:
            self.successful_executions += 1
        else:
            self.failed_executions += 1

        # Update execution time metrics
        self.max_execution_time = max(self.max_execution_time, execution_time)
        self.min_execution_time = min(self.min_execution_time, execution_time)

        # Update average execution time
        if self.total_executions > 0:
            self.average_execution_time = (
                self.average_execution_time * (self.total_executions - 1)
                + execution_time
            ) / self.total_executions

        self.last_updated = datetime.now(timezone.utc)

    def record_skipped_execution(self):
        """Record a skipped execution."""
        self.skipped_executions += 1
        self.last_updated = datetime.now(timezone.utc)

    def calculate_success_rate(self) -> float:
        """Calculate execution success rate."""
        if self.total_executions == 0:
            return 0.0
        return self.successful_executions / self.total_executions

    def to_dict(self) -> Dict[str, Any]:
        """Convert metrics to dictionary."""
        return {
            "total_executions": self.total_executions,
            "successful_executions": self.successful_executions,
            "failed_executions": self.failed_executions,
            "skipped_executions": self.skipped_executions,
            "average_execution_time": self.average_execution_time,
            "max_execution_time": self.max_execution_time,
            "min_execution_time": (
                self.min_execution_time
                if self.min_execution_time != float("inf")
                else 0.0
            ),
            "active_schedules": self.active_schedules,
            "paused_schedules": self.paused_schedules,
            "error_schedules": self.error_schedules,
            "success_rate": self.calculate_success_rate(),
            "last_updated": self.last_updated.isoformat(),
        }


class MetricsCollector:
    """
    Central metrics collector for the trigger service.

    This class aggregates metrics from various components and provides
    a unified interface for metrics collection and reporting.
    """

    def __init__(self):
        """Initialize the metrics collector."""
        self.task_queue_metrics = TaskQueueMetrics()
        self.adapter_metrics: Dict[str, AdapterMetrics] = {}
        self.start_time = time.time()

        logger.info("Metrics collector initialized")

    def get_task_queue_metrics(self) -> TaskQueueMetrics:
        """Get task queue metrics."""
        return self.task_queue_metrics

    def get_adapter_metrics(self, adapter_name: str) -> AdapterMetrics:
        """Get metrics for a specific adapter."""
        if adapter_name not in self.adapter_metrics:
            self.adapter_metrics[adapter_name] = AdapterMetrics()
        return self.adapter_metrics[adapter_name]

    def get_all_metrics(self) -> Dict[str, Any]:
        """Get all collected metrics."""
        uptime = time.time() - self.start_time

        return {
            "uptime_seconds": uptime,
            "task_queue": self.task_queue_metrics.to_dict(),
            "adapters": {
                name: metrics.to_dict()
                for name, metrics in self.adapter_metrics.items()
            },
            "system": {
                "start_time": datetime.fromtimestamp(
                    self.start_time, timezone.utc
                ).isoformat(),
                "current_time": datetime.now(timezone.utc).isoformat(),
            },
        }

    def reset_metrics(self):
        """Reset all metrics."""
        self.task_queue_metrics = TaskQueueMetrics()
        self.adapter_metrics.clear()
        self.start_time = time.time()
        logger.info("All metrics reset")


# Global metrics collector instance
_metrics_collector: Optional[MetricsCollector] = None


def get_metrics_collector() -> MetricsCollector:
    """Get the global metrics collector instance."""
    global _metrics_collector
    if _metrics_collector is None:
        _metrics_collector = MetricsCollector()
    return _metrics_collector


def record_task_completion(processing_time: float, success: bool):
    """Record task completion metrics."""
    collector = get_metrics_collector()
    task_metrics = collector.get_task_queue_metrics()

    task_metrics.update_processing_time(processing_time)

    if success:
        task_metrics.increment_completed()
    else:
        task_metrics.increment_failed()


def record_adapter_request(adapter_name: str, success: bool, response_time: float):
    """Record adapter request metrics."""
    collector = get_metrics_collector()
    adapter_metrics = collector.get_adapter_metrics(adapter_name)
    adapter_metrics.record_request(success, response_time)


def record_adapter_health_check(adapter_name: str, success: bool):
    """Record adapter health check metrics."""
    collector = get_metrics_collector()
    adapter_metrics = collector.get_adapter_metrics(adapter_name)
    adapter_metrics.record_health_check(success)
