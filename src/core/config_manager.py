"""
Centralized configuration management for the Trigger Service.

This module provides a comprehensive configuration management system with
validation, environment-specific configs, and proper secret management.
"""

import os
from typing import Dict, Any, Optional, List, Union
from pathlib import Path
from enum import Enum
import json
import yaml
from pydantic import BaseSettings, Field, validator, SecretStr
from pydantic.env_settings import SettingsSourceCallable

from src.utils.logger import get_logger

logger = get_logger(__name__)


class Environment(str, Enum):
    """Environment enumeration."""
    DEVELOPMENT = "development"
    TESTING = "testing"
    STAGING = "staging"
    PRODUCTION = "production"


class LogLevel(str, Enum):
    """Log level enumeration."""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class DatabaseConfig(BaseSettings):
    """Database configuration."""
    
    url: str = Field(..., description="Database connection URL")
    pool_size: int = Field(default=10, description="Connection pool size")
    max_overflow: int = Field(default=20, description="Maximum pool overflow")
    pool_timeout: int = Field(default=30, description="Pool timeout in seconds")
    pool_recycle: int = Field(default=3600, description="Pool recycle time in seconds")
    echo: bool = Field(default=False, description="Enable SQL query logging")
    
    @validator('url')
    def validate_database_url(cls, v):
        """Validate database URL."""
        if not v.startswith(('postgresql://', 'postgresql+asyncpg://')):
            raise ValueError('Database URL must be a PostgreSQL URL')
        return v
    
    @validator('pool_size')
    def validate_pool_size(cls, v):
        """Validate pool size."""
        if v < 1 or v > 100:
            raise ValueError('Pool size must be between 1 and 100')
        return v


class AuthServiceConfig(BaseSettings):
    """Auth service configuration."""
    
    url: str = Field(..., description="Auth service URL")
    api_key: SecretStr = Field(..., description="API key for auth service")
    timeout: int = Field(default=10, description="Request timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    
    @validator('url')
    def validate_auth_url(cls, v):
        """Validate auth service URL."""
        if not v.startswith(('http://', 'https://')):
            raise ValueError('Auth service URL must be a valid HTTP/HTTPS URL')
        return v
    
    @validator('timeout')
    def validate_timeout(cls, v):
        """Validate timeout."""
        if v < 1 or v > 300:
            raise ValueError('Timeout must be between 1 and 300 seconds')
        return v


class RedisConfig(BaseSettings):
    """Redis configuration."""
    
    url: Optional[str] = Field(default=None, description="Redis connection URL")
    host: str = Field(default="localhost", description="Redis host")
    port: int = Field(default=6379, description="Redis port")
    db: int = Field(default=0, description="Redis database number")
    password: Optional[SecretStr] = Field(default=None, description="Redis password")
    ssl: bool = Field(default=False, description="Use SSL connection")
    max_connections: int = Field(default=10, description="Maximum connections")
    
    @validator('port')
    def validate_port(cls, v):
        """Validate Redis port."""
        if v < 1 or v > 65535:
            raise ValueError('Port must be between 1 and 65535')
        return v
    
    @validator('db')
    def validate_db(cls, v):
        """Validate Redis database number."""
        if v < 0 or v > 15:
            raise ValueError('Redis database must be between 0 and 15')
        return v


class WebhookConfig(BaseSettings):
    """Webhook configuration."""
    
    base_url: str = Field(..., description="Base URL for webhook endpoints")
    timeout: int = Field(default=30, description="Webhook timeout in seconds")
    max_retries: int = Field(default=3, description="Maximum retry attempts")
    verify_ssl: bool = Field(default=True, description="Verify SSL certificates")
    
    @validator('base_url')
    def validate_base_url(cls, v):
        """Validate webhook base URL."""
        if not v.startswith(('http://', 'https://')):
            raise ValueError('Webhook base URL must be a valid HTTP/HTTPS URL')
        return v.rstrip('/')


class RetryConfig(BaseSettings):
    """Retry configuration."""
    
    max_attempts: int = Field(default=3, description="Maximum retry attempts")
    base_delay: float = Field(default=1.0, description="Base delay in seconds")
    backoff_factor: float = Field(default=2.0, description="Exponential backoff factor")
    max_delay: float = Field(default=60.0, description="Maximum delay in seconds")
    jitter: bool = Field(default=True, description="Add jitter to delays")
    
    @validator('max_attempts')
    def validate_max_attempts(cls, v):
        """Validate max attempts."""
        if v < 1 or v > 10:
            raise ValueError('Max attempts must be between 1 and 10')
        return v
    
    @validator('base_delay', 'max_delay')
    def validate_delays(cls, v):
        """Validate delay values."""
        if v < 0.1 or v > 300:
            raise ValueError('Delay must be between 0.1 and 300 seconds')
        return v
    
    @validator('backoff_factor')
    def validate_backoff_factor(cls, v):
        """Validate backoff factor."""
        if v < 1.0 or v > 10.0:
            raise ValueError('Backoff factor must be between 1.0 and 10.0')
        return v


class SecurityConfig(BaseSettings):
    """Security configuration."""
    
    secret_key: SecretStr = Field(..., description="Application secret key")
    api_key_header: str = Field(default="X-API-Key", description="API key header name")
    cors_origins: List[str] = Field(default=["*"], description="CORS allowed origins")
    cors_methods: List[str] = Field(
        default=["GET", "POST", "PUT", "DELETE", "OPTIONS"],
        description="CORS allowed methods"
    )
    cors_headers: List[str] = Field(default=["*"], description="CORS allowed headers")
    rate_limit_enabled: bool = Field(default=True, description="Enable rate limiting")
    rate_limit_requests: int = Field(default=100, description="Requests per minute")
    
    @validator('secret_key')
    def validate_secret_key(cls, v):
        """Validate secret key."""
        if len(v.get_secret_value()) < 32:
            raise ValueError('Secret key must be at least 32 characters long')
        return v


class TriggerServiceConfig(BaseSettings):
    """Main trigger service configuration."""
    
    # Environment
    environment: Environment = Field(default=Environment.DEVELOPMENT, description="Application environment")
    debug: bool = Field(default=False, description="Enable debug mode")
    
    # Server
    host: str = Field(default="0.0.0.0", description="Server host")
    port: int = Field(default=8000, description="Server port")
    workers: int = Field(default=1, description="Number of worker processes")
    
    # Logging
    log_level: LogLevel = Field(default=LogLevel.INFO, description="Logging level")
    log_format: str = Field(default="json", description="Log format (json or text)")
    log_file: Optional[str] = Field(default=None, description="Log file path")
    
    # Component configurations
    database: DatabaseConfig = Field(default_factory=DatabaseConfig, description="Database configuration")
    auth_service: AuthServiceConfig = Field(default_factory=AuthServiceConfig, description="Auth service configuration")
    redis: RedisConfig = Field(default_factory=RedisConfig, description="Redis configuration")
    webhook: WebhookConfig = Field(default_factory=WebhookConfig, description="Webhook configuration")
    retry: RetryConfig = Field(default_factory=RetryConfig, description="Retry configuration")
    security: SecurityConfig = Field(default_factory=SecurityConfig, description="Security configuration")
    
    # Feature flags
    features: Dict[str, bool] = Field(
        default_factory=lambda: {
            "redis_enabled": False,
            "metrics_enabled": True,
            "health_checks_enabled": True,
            "rate_limiting_enabled": True,
            "webhook_validation_enabled": True,
        },
        description="Feature flags"
    )
    
    @validator('port')
    def validate_port(cls, v):
        """Validate server port."""
        if v < 1 or v > 65535:
            raise ValueError('Port must be between 1 and 65535')
        return v
    
    @validator('workers')
    def validate_workers(cls, v):
        """Validate worker count."""
        if v < 1 or v > 32:
            raise ValueError('Workers must be between 1 and 32')
        return v
    
    class Config:
        """Pydantic configuration."""
        env_file = ".env"
        env_file_encoding = "utf-8"
        env_nested_delimiter = "__"
        case_sensitive = False
        
        @classmethod
        def customise_sources(
            cls,
            init_settings: SettingsSourceCallable,
            env_settings: SettingsSourceCallable,
            file_secret_settings: SettingsSourceCallable,
        ) -> tuple[SettingsSourceCallable, ...]:
            """Customize settings sources priority."""
            return (
                init_settings,
                env_settings,
                file_secret_settings,
                yaml_config_settings_source,
                json_config_settings_source,
            )


def yaml_config_settings_source(settings: BaseSettings) -> Dict[str, Any]:
    """
    Load configuration from YAML file.
    
    Args:
        settings: Settings instance
        
    Returns:
        Dict[str, Any]: Configuration from YAML file
    """
    config_file = os.getenv("CONFIG_FILE", "config.yaml")
    config_path = Path(config_file)
    
    if not config_path.exists():
        return {}
    
    try:
        with open(config_path, "r", encoding="utf-8") as f:
            config_data = yaml.safe_load(f)
        
        logger.info(f"Loaded configuration from {config_path}")
        return config_data or {}
        
    except Exception as e:
        logger.warning(f"Failed to load YAML config from {config_path}: {e}")
        return {}


def json_config_settings_source(settings: BaseSettings) -> Dict[str, Any]:
    """
    Load configuration from JSON file.
    
    Args:
        settings: Settings instance
        
    Returns:
        Dict[str, Any]: Configuration from JSON file
    """
    config_file = os.getenv("CONFIG_JSON_FILE", "config.json")
    config_path = Path(config_file)
    
    if not config_path.exists():
        return {}
    
    try:
        with open(config_path, "r", encoding="utf-8") as f:
            config_data = json.load(f)
        
        logger.info(f"Loaded configuration from {config_path}")
        return config_data or {}
        
    except Exception as e:
        logger.warning(f"Failed to load JSON config from {config_path}: {e}")
        return {}


class ConfigManager:
    """
    Configuration manager for the Trigger Service.
    
    This class provides a centralized way to manage application configuration
    with validation, environment-specific settings, and secret management.
    """
    
    def __init__(self):
        """Initialize the configuration manager."""
        self._config: Optional[TriggerServiceConfig] = None
        self._environment = os.getenv("ENVIRONMENT", Environment.DEVELOPMENT.value)
    
    def load_config(self, config_override: Optional[Dict[str, Any]] = None) -> TriggerServiceConfig:
        """
        Load and validate configuration.
        
        Args:
            config_override: Optional configuration overrides
            
        Returns:
            TriggerServiceConfig: Validated configuration
        """
        try:
            # Load base configuration
            if config_override:
                self._config = TriggerServiceConfig(**config_override)
            else:
                self._config = TriggerServiceConfig()
            
            # Validate environment-specific settings
            self._validate_environment_config()
            
            logger.info(
                f"Configuration loaded successfully",
                environment=self._config.environment,
                debug=self._config.debug,
                features=self._config.features
            )
            
            return self._config
            
        except Exception as e:
            logger.error(f"Failed to load configuration: {e}")
            raise
    
    def _validate_environment_config(self) -> None:
        """Validate environment-specific configuration."""
        if not self._config:
            return
        
        # Production environment validations
        if self._config.environment == Environment.PRODUCTION:
            if self._config.debug:
                logger.warning("Debug mode is enabled in production environment")
            
            if self._config.database.echo:
                logger.warning("Database query logging is enabled in production")
            
            if "*" in self._config.security.cors_origins:
                logger.warning("CORS allows all origins in production")
    
    def get_config(self) -> TriggerServiceConfig:
        """
        Get the current configuration.
        
        Returns:
            TriggerServiceConfig: Current configuration
            
        Raises:
            RuntimeError: If configuration is not loaded
        """
        if not self._config:
            raise RuntimeError("Configuration not loaded. Call load_config() first.")
        return self._config
    
    def reload_config(self) -> TriggerServiceConfig:
        """
        Reload configuration from sources.
        
        Returns:
            TriggerServiceConfig: Reloaded configuration
        """
        self._config = None
        return self.load_config()
    
    def get_database_url(self) -> str:
        """Get database URL with secrets resolved."""
        config = self.get_config()
        return config.database.url
    
    def get_auth_service_config(self) -> Dict[str, Any]:
        """Get auth service configuration with secrets resolved."""
        config = self.get_config()
        return {
            "url": config.auth_service.url,
            "api_key": config.auth_service.api_key.get_secret_value(),
            "timeout": config.auth_service.timeout,
            "max_retries": config.auth_service.max_retries,
        }
    
    def is_feature_enabled(self, feature_name: str) -> bool:
        """
        Check if a feature is enabled.
        
        Args:
            feature_name: Name of the feature
            
        Returns:
            bool: True if feature is enabled, False otherwise
        """
        config = self.get_config()
        return config.features.get(feature_name, False)


# Global configuration manager instance
_config_manager: Optional[ConfigManager] = None


def get_config_manager() -> ConfigManager:
    """
    Get the global configuration manager instance.
    
    Returns:
        ConfigManager: Global configuration manager
    """
    global _config_manager
    if _config_manager is None:
        _config_manager = ConfigManager()
    return _config_manager


def get_config() -> TriggerServiceConfig:
    """
    Get the current configuration.
    
    Returns:
        TriggerServiceConfig: Current configuration
    """
    return get_config_manager().get_config()


def load_config(config_override: Optional[Dict[str, Any]] = None) -> TriggerServiceConfig:
    """
    Load and validate configuration.
    
    Args:
        config_override: Optional configuration overrides
        
    Returns:
        TriggerServiceConfig: Validated configuration
    """
    return get_config_manager().load_config(config_override)
