"""
Trigger Manager - Core orchestration for trigger lifecycle management.

This module provides the main TriggerManager class that coordinates trigger
detection, adapter management, and workflow execution.
"""

from typing import Dict, List, Optional, Any, Tuple
from uuid import UUID
import asyncio
from datetime import datetime

from sqlalchemy import select, and_, desc, func

from src.adapters.base import (
    BaseTriggerAdapter,
    TriggerEvent,
    TriggerConfiguration,
    TriggerEventType,
    AdapterHealthStatus,
)
from src.database.models import Trigger, TriggerExecution
from src.database.connection import get_async_session, get_db_manager
from src.core.workflow_executor import WorkflowExecutor
from src.utils.logger import get_logger
from src.utils.retry import Retry<PERSON>andler
from src.schemas.trigger import TriggerCreate
from sqlalchemy.ext.asyncio import AsyncSession

logger = get_logger(__name__)


class TriggerManager:
    """
    Central manager for trigger lifecycle and event processing.

    The TriggerManager coordinates between different trigger adapters,
    manages trigger configurations, and orchestrates workflow execution
    when triggers are activated.
    """

    def __init__(
        self,
        db_session: Optional[AsyncSession] = None,
        workflow_executor: Optional[WorkflowExecutor] = None,
    ):
        """
        Initialize the trigger manager.

        Args:
            db_session: Database session to use for operations
            workflow_executor: Workflow executor instance
        """
        self._adapters: Dict[str, BaseTriggerAdapter] = {}
        self._workflow_executor = workflow_executor or WorkflowExecutor(db_session)
        self._retry_handler = RetryHandler()
        self.db_manager = get_db_manager()
        self._db_session = db_session
        logger.info("TriggerManager initialized")

    def register_adapter(self, adapter_name: str, adapter: BaseTriggerAdapter) -> None:
        """
        Register a trigger adapter with the manager.

        Args:
            adapter_name: Name to register the adapter under
            adapter: The adapter instance to register
        """
        self._adapters[adapter_name] = adapter
        logger.info("Registered adapter", adapter_name=adapter_name)

    def get_adapter(self, adapter_name: str) -> Optional[BaseTriggerAdapter]:
        """
        Get a registered adapter by name.

        Args:
            adapter_name: Name of the adapter to retrieve

        Returns:
            BaseTriggerAdapter: The adapter instance, or None if not found
        """
        return self._adapters.get(adapter_name)

    def list_adapters(self) -> List[str]:
        """
        Get list of registered adapter names.

        Returns:
            List[str]: List of adapter names
        """
        return list(self._adapters.keys())

    async def create_trigger(
        self,
        trigger_data: TriggerCreate,
        session: AsyncSession,
    ) -> Optional[Trigger]:
        """
        Create a new trigger from TriggerCreate schema object.

        Args:
            trigger_data: TriggerCreate object with trigger configuration
            session: Database session to use

        Returns:
            Trigger: Created trigger object if successful, None otherwise
        """
        try:
            adapter = self.get_adapter(trigger_data.trigger_type)
            if not adapter:
                logger.error(f"Adapter not found: {trigger_data.trigger_type}")
                return None

            # Check if trigger already exists for this user/workflow/trigger_type combination
            existing_trigger_result = await session.execute(
                select(Trigger).where(
                    and_(
                        Trigger.user_id == trigger_data.user_id,
                        Trigger.workflow_id == trigger_data.workflow_id,
                        Trigger.trigger_type == trigger_data.trigger_type,
                    )
                )
            )
            existing_trigger = existing_trigger_result.scalars().first()

            if existing_trigger:
                logger.warning(
                    f"Trigger already exists for user {trigger_data.user_id}, workflow {trigger_data.workflow_id}, "
                    f"trigger_type {trigger_data.trigger_type}. Returning existing trigger."
                )
                return existing_trigger

            # Create new trigger
            trigger = Trigger(
                user_id=trigger_data.user_id,
                workflow_id=trigger_data.workflow_id,
                trigger_type=trigger_data.trigger_type,
                name=trigger_data.trigger_name,  # Map trigger_name to name field
                trigger_config=trigger_data.trigger_config,
                event_types=trigger_data.event_types,
                is_active=True,
            )
            session.add(trigger)
            await session.flush()  # Get the ID

            # Create trigger configuration for adapter
            # Convert string event types to enum values
            enum_event_types = []
            for et in trigger_data.event_types:
                if et == "created":
                    enum_event_types.append(TriggerEventType.CREATED)
                elif et == "updated":
                    enum_event_types.append(TriggerEventType.UPDATED)
                elif et == "deleted":
                    enum_event_types.append(TriggerEventType.DELETED)
                elif et == "reminder":
                    enum_event_types.append(TriggerEventType.REMINDER)

            trigger_config_obj = TriggerConfiguration(
                trigger_id=trigger.id,
                user_id=trigger_data.user_id,
                workflow_id=trigger_data.workflow_id,
                event_types=enum_event_types,
                config=trigger_data.trigger_config,
                is_active=True,
            )

            # Register with adapter, passing the current session
            success = await adapter.register_trigger(trigger_config_obj, session)
            if success:
                logger.info("Successfully created trigger", trigger_id=trigger.id)
                return trigger
            else:
                logger.error(
                    f"Failed to register trigger with adapter {trigger_data.trigger_type}"
                )
                return None

        except Exception as e:
            logger.error(f"Failed to create trigger", error=str(e))
            return None

    async def create_trigger_legacy(
        self,
        user_id: str,
        workflow_id: str,
        trigger_type: str,
        trigger_name: str,
        trigger_config: Dict[str, Any],
        event_types: List[str],
    ) -> Optional[UUID]:
        """
        Create a new trigger with the specified adapter.

        Args:
            user_id: ID of the user creating the trigger
            workflow_id: ID of the workflow to execute
            trigger_type: Type of trigger (adapter name)
            trigger_name: Human-readable name for the trigger
            trigger_config: Adapter-specific configuration
            event_types: List of event types to monitor

        Returns:
            UUID: Trigger ID if created successfully, None otherwise
        """
        try:
            adapter = self.get_adapter(trigger_type)
            if not adapter:
                logger.error(f"Adapter not found: {trigger_type}")
                return None

            # Check for existing trigger first
            async with self.db_manager.get_async_session(auto_commit=False) as session:
                # Check if trigger already exists for this user/workflow/trigger_type combination
                existing_trigger_result = await session.execute(
                    select(Trigger).where(
                        and_(
                            Trigger.user_id == user_id,
                            Trigger.workflow_id == workflow_id,
                            Trigger.trigger_type == trigger_type,
                        )
                    )
                )
                existing_trigger = existing_trigger_result.scalars().first()

                if existing_trigger:
                    logger.warning(
                        f"Trigger already exists for user {user_id}, workflow {workflow_id}, "
                        f"trigger_type {trigger_type}. Returning existing trigger ID."
                    )
                    return existing_trigger.id

                # Create new trigger
                trigger = Trigger(
                    user_id=user_id,
                    workflow_id=workflow_id,
                    trigger_type=trigger_type,
                    trigger_name=trigger_name,
                    trigger_config=trigger_config,
                    event_types=event_types,
                    is_active=True,
                )
                session.add(trigger)
                await session.flush()  # Get the ID

                # Create trigger configuration for adapter
                # Convert string event types to enum values
                enum_event_types = []
                for et in event_types:
                    if et == "created":
                        enum_event_types.append(TriggerEventType.CREATED)
                    elif et == "updated":
                        enum_event_types.append(TriggerEventType.UPDATED)
                    elif et == "deleted":
                        enum_event_types.append(TriggerEventType.DELETED)
                    elif et == "reminder":
                        enum_event_types.append(TriggerEventType.REMINDER)

                trigger_config_obj = TriggerConfiguration(
                    trigger_id=trigger.id,
                    user_id=user_id,
                    workflow_id=workflow_id,
                    event_types=enum_event_types,
                    config=trigger_config,
                    is_active=True,
                )

                # Register with adapter, passing the current session
                success = await adapter.register_trigger(trigger_config_obj, session)
                if success:
                    await session.commit()
                    logger.info("Successfully created trigger", trigger_id=trigger.id)
                    return trigger.id
                else:
                    await session.rollback()
                    logger.error(
                        f"Failed to register trigger with adapter {trigger_type}"
                    )
                    return None

        except Exception as e:
            logger.error(f"Failed to create trigger", error=str(e))
            return None

    async def remove_trigger(self, trigger_id: UUID) -> bool:
        """
        Remove an existing trigger.

        Args:
            trigger_id: Unique identifier for the trigger to remove

        Returns:
            bool: True if trigger was removed successfully
        """
        try:
            async with self.db_manager.get_async_session(auto_commit=False) as session:
                # Get trigger from database
                trigger = await session.get(Trigger, trigger_id)
                if not trigger:
                    logger.warning(f"Trigger {trigger_id} not found in database")
                    return False

                # Get adapter
                adapter = self.get_adapter(trigger.trigger_type)
                if not adapter:
                    logger.error(f"Adapter not found: {trigger.trigger_type}")
                    return False

                # Unregister from adapter
                success = await adapter.unregister_trigger(trigger_id)
                if success:
                    # Remove from database
                    await session.delete(trigger)
                    await session.commit()
                    logger.info("Successfully removed trigger", trigger_id=trigger_id)
                    return True
                else:
                    logger.error(
                        f"Failed to unregister trigger {trigger_id} from adapter"
                    )
                    return False

        except Exception as e:
            logger.error(f"Failed to remove trigger {trigger_id}", error=str(e))
            return False

    async def process_event(self, adapter_name: str, raw_event: Dict[str, Any]) -> bool:
        """
        Process an incoming event from an adapter and execute matching workflows.

        Args:
            adapter_name: Name of the adapter that received the event
            raw_event: Raw event data from the external service

        Returns:
            bool: True if event was processed successfully
        """
        try:
            adapter = self.get_adapter(adapter_name)
            if not adapter:
                logger.error(f"Adapter not found: {adapter_name}")
                return False

            # Process the raw event
            trigger_event = await adapter.process_event(raw_event)

            if not trigger_event:
                logger.debug("Event ignored by adapter", adapter_name=adapter_name)
                return True  # Not an error, just ignored

            # Find matching triggers
            matching_triggers = await self._find_matching_triggers(
                adapter_name, trigger_event
            )

            # Execute workflows for matching triggers
            for trigger in matching_triggers:
                await self._execute_trigger_workflow(trigger, trigger_event)

            logger.info(
                f"Processed event {trigger_event.event_id} from {adapter_name}, "
                f"matched {len(matching_triggers)} triggers"
            )
            return True

        except Exception as e:
            logger.error(f"Failed to process event from {adapter_name}", error=str(e))
            return False

    async def health_check(self) -> Dict[str, AdapterHealthStatus]:
        """
        Check health of all registered adapters.

        Returns:
            Dict[str, AdapterHealthStatus]: Health status for each adapter
        """
        health_status = {}
        for name, adapter in self._adapters.items():
            try:
                health_status[name] = await adapter.health_check()
            except Exception as e:
                health_status[name] = AdapterHealthStatus(
                    is_healthy=False, last_check=datetime.now(), error_message=str(e)
                )

        return health_status

    async def get_trigger_by_id(self, trigger_id: str) -> Optional[Trigger]:
        """
        Get a specific trigger by its ID.

        Args:
            trigger_id: ID of the trigger

        Returns:
            Optional[Trigger]: Trigger if found, None otherwise
        """
        try:
            async with self.db_manager.get_async_session() as session:
                result = await session.execute(
                    select(Trigger).where(Trigger.id == trigger_id)
                )
                return result.scalars().first()
        except Exception as e:
            logger.error(f"Failed to get trigger {trigger_id}", error=str(e))
            return None

    async def get_triggers_for_user(self, user_id: str) -> List[Trigger]:
        """
        Get all triggers for a specific user.

        Args:
            user_id: ID of the user

        Returns:
            List[Trigger]: List of user's triggers
        """
        try:
            async with self.db_manager.get_async_session() as session:
                result = await session.execute(
                    select(Trigger).where(Trigger.user_id == user_id)
                )
                return result.scalars().all()
        except Exception as e:
            logger.error(f"Failed to get triggers for user {user_id}", error=str(e))
            return []

    async def get_triggers_for_workflow(self, workflow_id: str) -> List[Trigger]:
        """
        Get all triggers for a specific workflow.

        Args:
            workflow_id: ID of the workflow

        Returns:
            List[Trigger]: List of workflow's triggers
        """
        try:
            async with self.db_manager.get_async_session() as session:
                result = await session.execute(
                    select(Trigger).where(Trigger.workflow_id == workflow_id)
                )
                return result.scalars().all()
        except Exception as e:
            logger.error(
                f"Failed to get triggers for workflow {workflow_id}", error=str(e)
            )
            return []

    async def list_triggers(
        self,
        user_id: Optional[str] = None,
        workflow_id: Optional[str] = None,
        trigger_type: Optional[str] = None,
        is_active: Optional[bool] = None,
        page: int = 1,
        page_size: int = 20,
    ) -> Tuple[List[Trigger], int]:
        """
        List triggers with optional filtering and pagination.

        Args:
            user_id: Optional user ID filter
            workflow_id: Optional workflow ID filter
            trigger_type: Optional trigger type filter
            is_active: Optional active status filter
            page: Page number (1-based)
            page_size: Number of items per page

        Returns:
            tuple[List[Trigger], int]: Tuple of (triggers, total_count)
        """
        try:
            async with self.db_manager.get_async_session() as session:
                # Build base query
                query = select(Trigger)
                count_query = select(func.count(Trigger.id))

                # Apply filters
                conditions = []
                if user_id:
                    conditions.append(Trigger.user_id == user_id)
                if workflow_id:
                    conditions.append(Trigger.workflow_id == workflow_id)
                if trigger_type:
                    conditions.append(Trigger.trigger_type == trigger_type)
                if is_active is not None:
                    conditions.append(Trigger.is_active == is_active)

                if conditions:
                    query = query.where(and_(*conditions))
                    count_query = count_query.where(and_(*conditions))

                # Get total count
                total_result = await session.execute(count_query)
                total_count = total_result.scalar()

                # Apply pagination and ordering
                query = query.order_by(desc(Trigger.created_at))
                query = query.offset((page - 1) * page_size).limit(page_size)

                # Execute query
                result = await session.execute(query)
                triggers = result.scalars().all()

                logger.debug(
                    f"Listed triggers: page={page}, page_size={page_size}, "
                    f"total={total_count}, returned={len(triggers)}, "
                    f"filters: user_id={user_id}, workflow_id={workflow_id}, "
                    f"trigger_type={trigger_type}, is_active={is_active}"
                )

                return triggers, total_count

        except Exception as e:
            logger.error(f"Failed to list triggers", error=str(e))
            return [], 0

    async def toggle_trigger(self, trigger_id: UUID, is_active: bool) -> bool:
        """
        Enable or disable a trigger.

        Args:
            trigger_id: ID of the trigger
            is_active: Whether to activate or deactivate

        Returns:
            bool: True if toggle was successful
        """
        try:
            async with self.db_manager.get_async_session(auto_commit=False) as session:
                trigger = await session.get(Trigger, trigger_id)
                if not trigger:
                    logger.warning(f"Trigger {trigger_id} not found")
                    return False

                trigger.is_active = is_active
                await session.commit()

                # Update adapter
                adapter = self.get_adapter(trigger.trigger_type)
                if adapter:
                    if is_active:
                        await adapter.resume_trigger(trigger_id)
                    else:
                        await adapter.pause_trigger(trigger_id)

                logger.info(
                    f"Trigger {trigger_id} {'activated' if is_active else 'deactivated'}"
                )
                return True

        except Exception as e:
            logger.error(f"Failed to toggle trigger {trigger_id}", error=str(e))
            return False

    async def _find_matching_triggers(
        self, adapter_name: str, trigger_event: TriggerEvent
    ) -> List[Trigger]:
        """
        Find triggers that match the given event.

        Args:
            adapter_name: Name of the adapter
            trigger_event: Event to match against

        Returns:
            List[Trigger]: List of matching triggers
        """
        try:
            async with self.db_manager.get_async_session() as session:
                # Build the query
                query = select(Trigger).where(
                    and_(
                        Trigger.trigger_type == adapter_name,
                        Trigger.is_active == True,
                        Trigger.event_types.contains([trigger_event.event_type]),
                    )
                )

                # Find active triggers for this adapter type that monitor this event type
                # Use eager loading to prevent N+1 queries when accessing executions
                query = query.options(selectinload(Trigger.executions))
                result = await session.execute(query)
                triggers = result.scalars().all()

                logger.debug(
                    f"Found {len(triggers)} matching triggers for {adapter_name}"
                )

                # Additional filtering based on trigger configuration could be added here
                # For now, return all matching triggers
                return triggers

        except Exception as e:
            logger.error(f"Failed to find matching triggers", error=str(e))
            return []

    async def _execute_trigger_workflow(
        self, trigger: Trigger, trigger_event: TriggerEvent
    ) -> None:
        """
        Execute workflow for a triggered event.

        Args:
            trigger: Trigger that was activated
            trigger_event: Event that activated the trigger
        """
        try:
            # Create execution record
            async with self.db_manager.get_async_session(auto_commit=False) as session:
                # Convert trigger_event to JSON-serializable format
                import json

                event_data_json = json.loads(trigger_event.model_dump_json())

                # Add trigger configuration to event data for workflow executor
                event_data_json["trigger_config"] = trigger.trigger_config

                execution = TriggerExecution(
                    trigger_id=trigger.id,
                    event_data=event_data_json,
                    status="pending",
                )
                session.add(execution)
                await session.flush()  # Get the ID

                logger.info(
                    f"Created trigger execution record {execution.id} for trigger {trigger.id}"
                )

                try:
                    # Execute workflow with fetch using the new method
                    correlation_id = (
                        await self._workflow_executor.execute_workflow_with_fetch(
                            db_session=session,
                            trigger_execution=execution,
                            user_id=trigger.user_id,
                            workflow_id=trigger.workflow_id,
                            event_data=event_data_json,
                        )
                    )

                    if correlation_id:
                        # Update trigger last_triggered_at
                        trigger.last_triggered_at = datetime.now()
                        execution.completed_at = datetime.now()

                        logger.info(
                            f"Successfully executed workflow for trigger {trigger.id}, "
                            f"correlation_id: {correlation_id}"
                        )
                    else:
                        execution.completed_at = datetime.now()
                        logger.error(
                            f"Workflow execution failed for trigger {trigger.id}"
                        )

                    # Commit the transaction
                    await session.commit()

                except Exception as e:
                    execution.status = "failed"
                    execution.error_message = str(e)
                    execution.completed_at = datetime.now()
                    await session.commit()
                    logger.error(
                        f"Failed to execute workflow for trigger {trigger.id}",
                        error=str(e),
                    )

        except Exception as e:
            logger.error(
                f"Failed to process trigger execution for trigger {trigger.id}",
                error=str(e),
            )

    async def get_execution_history(
        self, trigger_id: UUID, limit: int = 100
    ) -> List[TriggerExecution]:
        """
        Get execution history for a trigger.

        Args:
            trigger_id: ID of the trigger
            limit: Maximum number of executions to return

        Returns:
            List[TriggerExecution]: List of executions
        """
        try:
            async with self.db_manager.get_async_session() as session:
                result = await session.execute(
                    select(TriggerExecution)
                    .where(TriggerExecution.trigger_id == trigger_id)
                    .order_by(desc(TriggerExecution.executed_at))
                    .limit(limit)
                )
                return result.scalars().all()

        except Exception as e:
            logger.error(
                f"Failed to get execution history for trigger {trigger_id}",
                error=str(e),
            )
            return []

    async def get_adapter_statistics(self) -> Dict[str, Dict[str, Any]]:
        """
        Get statistics for all adapters.

        Returns:
            Dict[str, Dict[str, Any]]: Statistics for each adapter
        """
        stats = {}
        for name, adapter in self._adapters.items():
            try:
                health = await adapter.health_check()
                stats[name] = {
                    "is_healthy": health.is_healthy,
                    "last_check": health.last_check.isoformat(),
                    "active_triggers": adapter.get_trigger_count(),
                    "error_message": health.error_message,
                }
            except Exception as e:
                stats[name] = {
                    "is_healthy": False,
                    "last_check": datetime.now().isoformat(),
                    "active_triggers": 0,
                    "error_message": str(e),
                }

        return stats
