"""
Exception hierarchy for the Trigger Service.

This module defines a comprehensive exception hierarchy for consistent
error handling across the application.
"""

from typing import Optional, Dict, Any


class TriggerServiceError(Exception):
    """
    Base exception for all Trigger Service errors.
    
    This is the root exception class that all other service-specific
    exceptions should inherit from.
    """
    
    def __init__(
        self, 
        message: str, 
        error_code: Optional[str] = None,
        details: Optional[Dict[str, Any]] = None,
        cause: Optional[Exception] = None
    ):
        """
        Initialize the base exception.
        
        Args:
            message: Human-readable error message
            error_code: Machine-readable error code
            details: Additional error details
            cause: Original exception that caused this error
        """
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.details = details or {}
        self.cause = cause
    
    def to_dict(self) -> Dict[str, Any]:
        """
        Convert exception to dictionary format.
        
        Returns:
            Dict[str, Any]: Exception data as dictionary
        """
        return {
            "error_type": self.__class__.__name__,
            "error_code": self.error_code,
            "message": self.message,
            "details": self.details,
            "cause": str(self.cause) if self.cause else None,
        }


class ConfigurationError(TriggerServiceError):
    """Raised when there's a configuration-related error."""
    pass


class ValidationError(TriggerServiceError):
    """Raised when input validation fails."""
    pass


class AuthenticationError(TriggerServiceError):
    """Raised when authentication fails."""
    pass


class AuthorizationError(TriggerServiceError):
    """Raised when authorization fails."""
    pass


class DatabaseError(TriggerServiceError):
    """Base class for database-related errors."""
    pass


class DatabaseConnectionError(DatabaseError):
    """Raised when database connection fails."""
    pass


class DatabaseTransactionError(DatabaseError):
    """Raised when database transaction fails."""
    pass


class AdapterError(TriggerServiceError):
    """Base class for adapter-related errors."""
    pass


class AdapterNotFoundError(AdapterError):
    """Raised when a requested adapter is not found."""
    pass


class AdapterConfigurationError(AdapterError):
    """Raised when adapter configuration is invalid."""
    pass


class AdapterConnectionError(AdapterError):
    """Raised when adapter cannot connect to external service."""
    pass


class AdapterAuthenticationError(AdapterError):
    """Raised when adapter authentication fails."""
    pass


class AdapterRateLimitError(AdapterError):
    """Raised when adapter hits rate limits."""
    pass


class WebhookError(TriggerServiceError):
    """Base class for webhook-related errors."""
    pass


class WebhookValidationError(WebhookError):
    """Raised when webhook validation fails."""
    pass


class WebhookProcessingError(WebhookError):
    """Raised when webhook processing fails."""
    pass


class WorkflowError(TriggerServiceError):
    """Base class for workflow-related errors."""
    pass


class WorkflowNotFoundError(WorkflowError):
    """Raised when a workflow is not found."""
    pass


class WorkflowExecutionError(WorkflowError):
    """Raised when workflow execution fails."""
    pass


class TriggerError(TriggerServiceError):
    """Base class for trigger-related errors."""
    pass


class TriggerNotFoundError(TriggerError):
    """Raised when a trigger is not found."""
    pass


class TriggerConfigurationError(TriggerError):
    """Raised when trigger configuration is invalid."""
    pass


class TriggerExecutionError(TriggerError):
    """Raised when trigger execution fails."""
    pass


class SchedulerError(TriggerServiceError):
    """Base class for scheduler-related errors."""
    pass


class SchedulerNotFoundError(SchedulerError):
    """Raised when a scheduler is not found."""
    pass


class SchedulerConfigurationError(SchedulerError):
    """Raised when scheduler configuration is invalid."""
    pass


class ExternalServiceError(TriggerServiceError):
    """Base class for external service errors."""
    pass


class ExternalServiceUnavailableError(ExternalServiceError):
    """Raised when external service is unavailable."""
    pass


class ExternalServiceTimeoutError(ExternalServiceError):
    """Raised when external service times out."""
    pass


class ExternalServiceRateLimitError(ExternalServiceError):
    """Raised when external service rate limit is exceeded."""
    pass


# Google Calendar specific exceptions
class GoogleCalendarError(AdapterError):
    """Base class for Google Calendar adapter errors."""
    pass


class GoogleCalendarAuthError(GoogleCalendarError):
    """Raised when Google Calendar authentication fails."""
    pass


class GoogleCalendarAPIError(GoogleCalendarError):
    """Raised when Google Calendar API returns an error."""
    pass


class GoogleCalendarWebhookError(GoogleCalendarError):
    """Raised when Google Calendar webhook operations fail."""
    pass


class GoogleCalendarQuotaError(GoogleCalendarError):
    """Raised when Google Calendar API quota is exceeded."""
    pass


# Retryable error marker
class RetryableError(Exception):
    """
    Mixin class to mark exceptions as retryable.
    
    Exceptions that inherit from this class will be automatically
    retried by the retry mechanism.
    """
    pass


# Specific retryable exceptions
class RetryableAdapterError(AdapterError, RetryableError):
    """Adapter error that should be retried."""
    pass


class RetryableDatabaseError(DatabaseError, RetryableError):
    """Database error that should be retried."""
    pass


class RetryableExternalServiceError(ExternalServiceError, RetryableError):
    """External service error that should be retried."""
    pass


class RetryableGoogleCalendarAPIError(GoogleCalendarAPIError, RetryableError):
    """Google Calendar API error that should be retried."""
    pass


def is_retryable_error(error: Exception) -> bool:
    """
    Check if an error is retryable.
    
    Args:
        error: Exception to check
        
    Returns:
        bool: True if error should be retried, False otherwise
    """
    # Check if error inherits from RetryableError
    if isinstance(error, RetryableError):
        return True
    
    # Check for specific error types that should be retried
    retryable_types = (
        ConnectionError,
        TimeoutError,
        ExternalServiceTimeoutError,
        ExternalServiceUnavailableError,
        DatabaseConnectionError,
        AdapterConnectionError,
    )
    
    return isinstance(error, retryable_types)


def get_error_category(error: Exception) -> str:
    """
    Get the category of an error for logging and monitoring.
    
    Args:
        error: Exception to categorize
        
    Returns:
        str: Error category
    """
    if isinstance(error, AuthenticationError):
        return "authentication"
    elif isinstance(error, AuthorizationError):
        return "authorization"
    elif isinstance(error, ValidationError):
        return "validation"
    elif isinstance(error, ConfigurationError):
        return "configuration"
    elif isinstance(error, DatabaseError):
        return "database"
    elif isinstance(error, AdapterError):
        return "adapter"
    elif isinstance(error, WebhookError):
        return "webhook"
    elif isinstance(error, WorkflowError):
        return "workflow"
    elif isinstance(error, TriggerError):
        return "trigger"
    elif isinstance(error, SchedulerError):
        return "scheduler"
    elif isinstance(error, ExternalServiceError):
        return "external_service"
    else:
        return "unknown"


def create_error_response(error: Exception) -> Dict[str, Any]:
    """
    Create a standardized error response from an exception.
    
    Args:
        error: Exception to convert
        
    Returns:
        Dict[str, Any]: Standardized error response
    """
    if isinstance(error, TriggerServiceError):
        return error.to_dict()
    
    return {
        "error_type": error.__class__.__name__,
        "error_code": error.__class__.__name__,
        "message": str(error),
        "details": {},
        "cause": None,
    }
