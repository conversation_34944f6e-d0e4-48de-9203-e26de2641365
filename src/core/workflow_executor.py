"""
Workflow Executor - Handles execution of workflows when triggers are activated.

This module provides the WorkflowExecutor class that fetches workflow details
and executes workflows when triggers are activated.
"""

import json
from typing import Dict, Any, Optional, List
import httpx
import structlog
from sqlalchemy.ext.asyncio import AsyncSession

from src.utils.config import get_settings
from src.database.models import TriggerExecution

logger = structlog.get_logger(__name__)


class WorkflowExecutor:
    """
    Handles workflow fetching and execution requests to the workflow service.

    This class is responsible for:
    1. Fetching workflow details from the workflow service
    2. Formatting trigger event data into proper workflow execution format
    3. Making HTTP requests to execute workflows
    4. Storing execution results in the database
    """

    def __init__(self, db_session: Optional[AsyncSession] = None):
        """Initialize the workflow executor."""
        self.settings = get_settings()
        self.client = httpx.AsyncClient(
            timeout=httpx.Timeout(60.0),
            headers={
                "Content-Type": "application/json",
                "X-Server-Auth-Key": self.settings.workflow_service_api_key,
            },
        )
        self.db_session = db_session

    async def fetch_workflow(self, workflow_id: str) -> Optional[Dict[str, Any]]:
        """
        Fetch workflow details from the workflow service.

        Args:
            workflow_id: ID of the workflow to fetch

        Returns:
            Dict[str, Any]: Workflow details, or None if failed
        """
        try:
            logger.info("Fetching workflow details", workflow_id=workflow_id)

            # Make GET request to fetch workflow
            response = await self.client.get(
                f"https://app-dev.rapidinnovation.dev/api/v1/workflows/orchestration/{workflow_id}",
                headers={"accept": "application/json"},
            )

            if response.status_code == 200:
                result = response.json()
                if result.get("success"):
                    workflow_data = result.get("workflow")
                    logger.info(
                        "Workflow fetched successfully",
                        workflow_id=workflow_id,
                        workflow_name=workflow_data.get("name"),
                    )
                    return workflow_data
                else:
                    logger.error(
                        "Workflow fetch failed - API returned success=false",
                        workflow_id=workflow_id,
                        message=result.get("message"),
                    )
                    return None
            else:
                logger.error(
                    "Workflow fetch failed",
                    workflow_id=workflow_id,
                    status_code=response.status_code,
                    response=response.text,
                )
                return None

        except Exception as e:
            logger.error(
                "Error fetching workflow", workflow_id=workflow_id, error=str(e)
            )
            return None

    async def execute_workflow_with_fetch(
        self,
        trigger_execution: TriggerExecution,
        user_id: str,
        workflow_id: str,
        event_data: Dict[str, Any],
        db_session: Optional[AsyncSession] = None,
    ) -> Optional[str]:
        """
        Fetch workflow details and execute the workflow with event data.

        Args:
            db_session: Database session for storing execution results
            trigger_execution: TriggerExecution record to update
            user_id: ID of the user who owns the workflow
            workflow_id: ID of the workflow to execute
            event_data: Event data from the trigger

        Returns:
            str: Correlation ID from the workflow service, or None if failed
        """
        try:
            # Use the provided db_session or the one from initialization
            session_to_use = db_session if db_session else self.db_session
            if not session_to_use:
                logger.error("No database session provided for workflow execution.")
                return None

            # Step 1: Fetch workflow details
            workflow_data = await self.fetch_workflow(workflow_id)
            if not workflow_data:
                trigger_execution.status = "failed"
                trigger_execution.error_message = "Failed to fetch workflow details"
                await session_to_use.commit()
                return None

            # Step 2: Execute the workflow
            correlation_id = await self.execute_workflow(
                user_id=user_id,
                workflow_id=workflow_id,
                workflow_data=workflow_data,
                event_data=event_data,
            )

            # Step 3: Update execution record
            if correlation_id:
                trigger_execution.workflow_execution_id = correlation_id
                trigger_execution.status = "success"
                logger.info(
                    "Workflow execution completed successfully",
                    correlation_id=correlation_id,
                    trigger_execution_id=str(trigger_execution.id),
                )
            else:
                trigger_execution.status = "failed"
                trigger_execution.error_message = "Workflow execution failed"
                logger.error(
                    "Workflow execution failed",
                    trigger_execution_id=str(trigger_execution.id),
                )

            await session_to_use.commit()
            return correlation_id

        except Exception as e:
            logger.error(
                "Error in workflow execution with fetch",
                error=str(e),
                trigger_execution_id=str(trigger_execution.id),
            )
            trigger_execution.status = "failed"
            trigger_execution.error_message = f"Execution error: {str(e)}"
            await session_to_use.commit()
            return None

    async def execute_workflow(
        self,
        user_id: str,
        workflow_id: str,
        workflow_data: Dict[str, Any],
        event_data: Dict[str, Any],
    ) -> Optional[str]:
        """
        Execute a workflow with the provided event data.

        Args:
            user_id: ID of the user who owns the workflow
            workflow_id: ID of the workflow to execute
            workflow_data: Complete workflow configuration from workflow service
            event_data: Event data from the trigger

        Returns:
            str: Correlation ID from the workflow service, or None if failed
        """
        try:
            # Transform event data into workflow payload format
            payload = self._transform_event_to_payload(event_data, workflow_data)

            # Prepare the workflow execution request
            request_data = {
                "approval": True,
                "payload": payload,
                "user_id": user_id,
                "workflow_id": workflow_id,
            }

            # Log the request data for debugging
            logger.info(
                "Executing workflow",
                workflow_id=workflow_id,
                user_id=user_id,
                payload=payload,
                request_data=request_data,
            )

            # Make the HTTP request to workflow execution service
            response = await self.client.post(
                "https://ruh-test-api.rapidinnovation.dev/api/v1/workflow-execute/server/execute",
                json=request_data,
            )

            if response.status_code == 202:
                result = response.json()
                correlation_id = result.get("correlationId")

                logger.info(
                    "Workflow execution initiated",
                    correlation_id=correlation_id,
                    user_id=user_id,
                    workflow_id=workflow_id,
                )

                return correlation_id
            else:
                logger.error(
                    "Workflow execution failed",
                    status_code=response.status_code,
                    response=response.text,
                    user_id=user_id,
                    workflow_id=workflow_id,
                )
                return None

        except Exception as e:
            logger.error(
                "Error executing workflow",
                error=str(e),
                user_id=user_id,
                workflow_id=workflow_id,
            )
            return None

    def _transform_event_to_payload(
        self, event_data: Dict[str, Any], workflow_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Transform trigger event data into workflow payload format.

        Args:
            event_data: Event data from the trigger
            workflow_data: Workflow configuration from workflow service

        Returns:
            Dict[str, Any]: Formatted payload for workflow execution
        """
        # Check if this is a scheduler event with input values
        scheduler_input_values = event_data.get("scheduler_input_values")

        if scheduler_input_values:
            # Use scheduler input values
            event_fields = self._extract_scheduler_input_values(scheduler_input_values)
        else:
            # Check if trigger has selected event fields configuration
            trigger_config = event_data.get("trigger_config", {})
            selected_event_fields = trigger_config.get("selected_event_fields")

            if selected_event_fields:
                # Use selected event fields mapping
                event_fields = self._extract_selected_event_fields(
                    event_data, selected_event_fields
                )
            else:
                # Fall back to default field extraction
                event_fields = self._extract_event_fields(event_data)

        # Get start nodes from workflow data to determine required fields
        start_nodes = workflow_data.get("start_nodes", [])

        # Create user payload template with event data
        user_payload_template = {}
        user_dependent_fields = []

        # Map event fields to workflow start nodes
        for start_node in start_nodes:
            field_name = start_node.get("field")
            field_type = start_node.get("type")
            transition_id = start_node.get("transition_id")

            if field_name and transition_id:
                user_dependent_fields.append(field_name)

                # Use event data if available, otherwise use a default value based on field type
                if field_name in event_fields:
                    field_value = event_fields[field_name]
                else:
                    # Generate appropriate default value based on field type
                    if field_type == "string":
                        field_value = f"trigger_event_{field_name}"
                    elif field_type == "number":
                        field_value = 0
                    elif field_type == "boolean":
                        field_value = False
                    else:
                        field_value = f"trigger_event_{field_name}"

                user_payload_template[field_name] = {
                    "transition_id": transition_id,
                    "value": field_value,
                }

        # If no start nodes found, use default mapping from event fields
        if not user_dependent_fields and event_fields:
            for field_name, field_value in event_fields.items():
                if field_value is not None:  # Only include non-null values
                    user_dependent_fields.append(field_name)
                    user_payload_template[field_name] = {
                        "transition_id": f"transition-{field_name}-default",
                        "value": field_value,
                    }

        return {
            "user_dependent_fields": user_dependent_fields,
            "user_payload_template": user_payload_template,
        }

    def _extract_selected_event_fields(
        self, event_data: Dict[str, Any], selected_fields: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Extract specific fields from event data based on user selection.

        Args:
            event_data: Event data from the trigger
            selected_fields: List of field mappings from trigger configuration

        Returns:
            Dict[str, Any]: Extracted fields mapped to workflow field names
        """
        extracted = {}

        # Get the actual calendar event data
        calendar_event_data = event_data.get("data", {})

        for field_mapping in selected_fields:
            calendar_field = field_mapping.get("calendar_field")
            workflow_field = field_mapping.get("workflow_field")
            field_type = field_mapping.get("field_type", "string")

            if not calendar_field or not workflow_field:
                continue

            # Extract value using dot notation for nested fields
            field_value = self._get_nested_field_value(
                calendar_event_data, calendar_field
            )

            # Convert value based on expected field type
            if field_value is not None:
                try:
                    if field_type == "number":
                        field_value = (
                            float(field_value)
                            if isinstance(field_value, (str, int, float))
                            else field_value
                        )
                    elif field_type == "boolean":
                        if isinstance(field_value, str):
                            field_value = field_value.lower() in (
                                "true",
                                "1",
                                "yes",
                                "on",
                            )
                        else:
                            field_value = bool(field_value)
                    # For string and array types, keep as-is
                except (ValueError, TypeError):
                    # If conversion fails, keep original value
                    pass

                extracted[workflow_field] = field_value

        return extracted

    def _get_nested_field_value(self, data: Dict[str, Any], field_path: str) -> Any:
        """
        Get value from nested dictionary using dot notation.

        Args:
            data: Dictionary to extract from
            field_path: Dot-separated path (e.g., 'start.dateTime')

        Returns:
            Any: Field value or None if not found
        """
        try:
            current = data
            for key in field_path.split("."):
                if isinstance(current, dict) and key in current:
                    current = current[key]
                else:
                    return None
            return current
        except (KeyError, TypeError, AttributeError):
            return None

    def _extract_event_fields(self, event_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract relevant fields from event data for workflow execution.

        Args:
            event_data: Event data from the trigger

        Returns:
            Dict[str, Any]: Extracted fields for workflow
        """
        extracted = {
            "event_type": event_data.get("event_type"),
            "event_id": event_data.get("event_id"),
            "timestamp": event_data.get("timestamp"),
            "source": event_data.get("source"),
        }

        # Add adapter-specific data
        if "data" in event_data:
            extracted.update(event_data["data"])

        return extracted

    def _extract_scheduler_input_values(
        self, input_values: List[Dict[str, Any]]
    ) -> Dict[str, Any]:
        """
        Extract input values from scheduler configuration.

        Args:
            input_values: List of input value mappings from scheduler configuration

        Returns:
            Dict[str, Any]: Extracted fields mapped to workflow field names
        """
        extracted = {}

        if not input_values:
            return extracted

        for input_value in input_values:
            field_name = input_value.get("field_name")
            field_value = input_value.get("field_value")
            field_type = input_value.get("field_type", "string")

            if not field_name or field_value is None:
                continue

            # Convert value based on expected field type
            try:
                if field_type == "number":
                    field_value = (
                        float(field_value)
                        if isinstance(field_value, (str, int, float))
                        else field_value
                    )
                elif field_type == "boolean":
                    if isinstance(field_value, str):
                        field_value = field_value.lower() in (
                            "true",
                            "1",
                            "yes",
                            "on",
                        )
                    else:
                        field_value = bool(field_value)
                # For string and array types, keep as-is
            except (ValueError, TypeError):
                # If conversion fails, keep original value
                pass

            extracted[field_name] = field_value

        return extracted

    async def close(self):
        """Close the HTTP client."""
        await self.client.aclose()
