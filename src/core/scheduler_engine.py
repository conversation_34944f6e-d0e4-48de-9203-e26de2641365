from datetime import datetime, timedelta, timezone
from typing import List, Optional, AsyncGenerator
from uuid import uuid4

from sqlalchemy.ext.asyncio import AsyncSession
import asyncio
from asyncio import Semaphore
import time

from src.core.distributed_lock import LockManager  # Import LockManager
from src.core.task_queue import (
    Task,
    TaskStatus,
    RedisTaskQueue,
)  # Import Task Queue classes
from src.core.metrics import SchedulerMetrics  # Import SchedulerMetrics

from src.database.models import Scheduler, SchedulerExecution
from src.core.workflow_executor import WorkflowExecutor
from src.core.scheduler_manager import SchedulerManager
from src.utils.logger import get_logger
from src.utils.retry import retry_async
from src.utils.schedule_parser import ScheduleParser
from src.utils.database import with_db_retry, TransactionManager
from src.schemas.scheduler import (
    SimplifiedSchedulerResponse,
    ScheduleFrequency,
)

logger = get_logger(__name__)


class SchedulerEngine:
    """
    Core engine for processing and executing scheduled tasks using the new simplified frequency-based schema.
    """

    def __init__(
        self,
        db_session: AsyncSession,
        task_queue: Optional[RedisTaskQueue],  # Make TaskQueue optional
        lock_manager: LockManager,  # Accept LockManager instance
        batch_size: int = 50,  # Add batch size configuration
    ):
        self.db_session = db_session
        self.task_queue = task_queue
        self.lock_manager = lock_manager
        self.batch_size = batch_size
        self.metrics = SchedulerMetrics()

    async def process_due_schedulers(self):
        """Process schedulers by queuing workflow execution tasks or executing directly."""
        start_time = time.time()
        mode = "task queue" if self.task_queue else "direct execution (fallback)"

        try:
            scheduler_manager = SchedulerManager(self.db_session)

            # Process schedulers in batches to avoid memory issues
            total_processed = 0
            total_queued = 0

            async for batch in scheduler_manager.get_due_schedulers_batch(
                batch_size=self.batch_size
            ):
                # Only log when we actually have schedulers to process
                if len(batch) > 0:
                    if (
                        total_processed == 0
                    ):  # Log start message only once when we have work
                        logger.info(f"Starting scheduler engine run with {mode}")

                    batch_queued = 0

                    for scheduler in batch:
                        # Process each scheduler in its own transaction scope
                        success = await self._process_single_scheduler(scheduler)
                        if success:
                            batch_queued += 1

                    total_processed += len(batch)
                    total_queued += batch_queued

                    logger.info(
                        f"Processed batch: {len(batch)} schedulers, {batch_queued} queued"
                    )

            execution_time = time.time() - start_time

            # Only log completion when we actually processed schedulers
            if total_processed > 0:
                logger.info(
                    f"Scheduler engine run completed: {total_processed} processed, {total_queued} queued, "
                    f"execution_time={execution_time:.2f}s"
                )

            # Update metrics (always update metrics for monitoring)
            self.metrics.record_batch_execution(
                total=total_processed,
                successful=total_queued,  # Successful here means successfully queued
                failed=total_processed
                - total_queued,  # Failed here means failed to queue
                execution_time=execution_time,
            )

        except Exception as e:
            logger.error(f"Error in process_due_schedulers: {e}", exc_info=True)
            self.metrics.record_engine_error(str(e))

    @with_db_retry(max_retries=3, base_delay=1.0)
    async def _process_single_scheduler(self, scheduler) -> bool:
        """Process a single scheduler in its own transaction scope."""
        # Try to acquire lock for this scheduler
        if not await self.lock_manager.acquire_scheduler_lock(scheduler.id, ttl=300):
            logger.debug(
                f"Scheduler {scheduler.id} is already being processed, skipping"
            )
            return False

        try:
            # Create execution record
            execution_time = datetime.now(timezone.utc)
            scheduler_execution = SchedulerExecution(
                scheduler_id=scheduler.id,
                execution_time=execution_time,
                status="pending",  # Status is pending until task worker picks it up
            )
            self.db_session.add(scheduler_execution)
            # Flush to get the execution ID without committing the transaction
            await self.db_session.flush()
            await self.db_session.refresh(scheduler_execution)

            # Create workflow execution task
            task = Task(
                id=str(uuid4()),  # Task ID
                queue_name="workflow_execution",  # Define a queue name for workflow tasks
                task_type="scheduler_workflow",  # Define a task type
                payload={
                    "scheduler_id": scheduler.id,
                    "execution_id": str(scheduler_execution.id),
                    "user_id": scheduler.user_id,
                    "workflow_id": scheduler.workflow_id,
                    "event_data": {
                        "event_type": "scheduler",
                        "event_id": f"scheduler_{scheduler.id}",
                        "timestamp": execution_time.isoformat(),
                        "source": "scheduler_engine",
                        "scheduler_input_values": (
                            [iv.model_dump() for iv in scheduler.input_values]
                            if scheduler.input_values
                            else None
                        ),
                        "data": {
                            "scheduler_id": scheduler.id,
                            "scheduler_name": scheduler.name,
                            "frequency": scheduler.frequency.value,
                            "execution_id": str(scheduler_execution.id),
                        },
                    },
                },
                status=TaskStatus.PENDING,
                created_at=datetime.utcnow(),
                max_retries=3,  # Configure retries for workflow execution
            )

            if self.task_queue:
                # Queue the task for background processing
                await self.task_queue.enqueue(task)
                logger.info(
                    f"Queued workflow execution task {task.id} for scheduler {scheduler.id}"
                )
            else:
                # Fallback: Execute workflow directly (synchronous mode)
                try:
                    workflow_executor = WorkflowExecutor(self.db_session)
                    result = await workflow_executor.execute_workflow(
                        scheduler.workflow_id,
                        scheduler.user_id,
                        task.payload["event_data"],
                    )

                    # Update execution record with success
                    scheduler_execution.status = "completed"
                    scheduler_execution.result = result
                    scheduler_execution.duration_ms = int(
                        (datetime.now(timezone.utc) - execution_time).total_seconds()
                        * 1000
                    )

                    logger.info(
                        f"Executed workflow directly for scheduler {scheduler.id} (fallback mode)"
                    )
                except Exception as workflow_error:
                    # Update execution record with failure
                    scheduler_execution.status = "failed"
                    scheduler_execution.error_message = str(workflow_error)
                    scheduler_execution.duration_ms = int(
                        (datetime.now(timezone.utc) - execution_time).total_seconds()
                        * 1000
                    )
                    logger.error(
                        f"Failed to execute workflow for scheduler {scheduler.id}: {workflow_error}",
                        exc_info=True,
                    )

            # Update scheduler's last_run_at and calculate next_run_at within the same transaction
            await self._update_scheduler_run_times_in_transaction(
                scheduler, execution_time
            )

            # Commit the transaction
            await self.db_session.commit()
            return True

        except Exception as e:
            logger.error(
                f"Failed to process scheduler {scheduler.id}: {e}",
                exc_info=True,
            )
            # Transaction will be rolled back automatically
            return False

        finally:
            # Always release the lock
            await self.lock_manager.release_scheduler_lock(scheduler.id)

    async def _update_scheduler_run_times_in_transaction(
        self, scheduler: SimplifiedSchedulerResponse, execution_time: datetime
    ):
        """Update scheduler's last_run_at and next_run_at within an existing transaction."""
        try:
            # Get the actual database object to update it
            from sqlalchemy import select

            stmt = select(Scheduler).where(Scheduler.id == scheduler.id)
            result = await self.db_session.execute(stmt)
            db_scheduler = result.scalars().first()

            if not db_scheduler:
                raise ValueError(f"Scheduler {scheduler.id} not found in database")

            # Update scheduler's last_run_at
            db_scheduler.last_run_at = execution_time

            logger.debug(
                f"Processing simplified scheduler ID: {scheduler.id} with frequency: {scheduler.frequency}"
            )

            # Create a temporary response object with the updated last_run_at for next run calculation
            updated_scheduler = scheduler.model_copy(
                update={"last_run_at": execution_time}
            )

            # Calculate next run time using simplified parser
            next_run_time = ScheduleParser.get_next_run_time(
                updated_scheduler, execution_time
            )

            # Update next_run_at
            db_scheduler.next_run_at = next_run_time
            logger.debug(
                f"Scheduler ID: {scheduler.id}, Next run at: {db_scheduler.next_run_at}"
            )

            self.db_session.add(db_scheduler)
            # No commit here - will be handled by the transaction context manager

        except Exception as e:
            logger.error(
                f"Failed to update scheduler {scheduler.id} run times: {e}",
                exc_info=True,
            )
            raise  # Re-raise to trigger transaction rollback

    async def _update_scheduler_run_times(
        self, scheduler: SimplifiedSchedulerResponse, execution_time: datetime
    ):
        """Update scheduler's last_run_at and next_run_at after queuing execution."""
        try:
            # Use a separate transaction for scheduler updates
            async with self.db_session.begin():
                await self._update_scheduler_run_times_in_transaction(
                    scheduler, execution_time
                )

        except Exception as e:
            logger.error(
                f"Failed to update scheduler {scheduler.id} run times: {e}",
                exc_info=True,
            )
            # Note: If this commit fails, the scheduler's next_run_at might not be updated,
            # potentially leading to duplicate executions. This needs robust error handling
            # or a separate cleanup process. For now, we log the error.

    async def run_pending_schedules(self):
        """
        Alias for process_due_schedulers to maintain compatibility.
        """
        await self.process_due_schedulers()
