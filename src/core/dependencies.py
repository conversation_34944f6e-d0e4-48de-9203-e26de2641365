"""
Dependency injection module for the Trigger Service.

This module provides FastAPI dependencies for core services and adapters,
replacing singleton patterns with proper dependency injection.
"""

from typing import Dict, Any, Optional
from functools import lru_cache
from fastapi import Depends
from sqlalchemy.ext.asyncio import AsyncSession

from src.database.connection import get_async_session, get_db_manager
from src.core.trigger_manager import TriggerManager
from src.core.workflow_executor import WorkflowExecutor
from src.adapters.factory import get_adapter_factory, AdapterFactory
from src.adapters.google_calendar import GoogleCalendarAdapter
from src.utils.auth_client import get_auth_client, AuthClient
from src.utils.config import get_settings, Settings
from src.utils.logger import get_logger

logger = get_logger(__name__)


class ServiceContainer:
    """
    Service container for managing application dependencies.

    This class provides a centralized way to manage service instances
    and their dependencies without using singleton patterns.
    """

    def __init__(self):
        self._services: Dict[str, Any] = {}
        self._adapters: Dict[str, Any] = {}

    def register_service(self, name: str, service: Any) -> None:
        """Register a service instance."""
        self._services[name] = service

    def get_service(self, name: str) -> Optional[Any]:
        """Get a service instance by name."""
        return self._services.get(name)

    def register_adapter(self, name: str, adapter: Any) -> None:
        """Register an adapter instance."""
        self._adapters[name] = adapter

    def get_adapter(self, name: str) -> Optional[Any]:
        """Get an adapter instance by name."""
        return self._adapters.get(name)

    def get_all_adapters(self) -> Dict[str, Any]:
        """Get all registered adapters."""
        return self._adapters.copy()


# Global service container instance
_service_container: Optional[ServiceContainer] = None


def get_service_container() -> ServiceContainer:
    """
    Get the global service container instance.

    Returns:
        ServiceContainer: The global service container
    """
    global _service_container
    if _service_container is None:
        _service_container = ServiceContainer()
    return _service_container


@lru_cache()
def get_settings_dependency() -> Settings:
    """
    FastAPI dependency for application settings.

    Returns:
        Settings: Application settings instance
    """
    return get_settings()


async def get_auth_client_dependency() -> AuthClient:
    """
    FastAPI dependency for auth client.

    Returns:
        AuthClient: Auth client instance
    """
    return get_auth_client()


async def get_workflow_executor_dependency(
    db_session: AsyncSession = Depends(get_async_session),
) -> WorkflowExecutor:
    """
    FastAPI dependency for workflow executor.

    Args:
        db_session: Database session dependency

    Returns:
        WorkflowExecutor: Workflow executor instance
    """
    return WorkflowExecutor(db_session)


async def get_adapter_factory_dependency() -> AdapterFactory:
    """
    FastAPI dependency for adapter factory.

    Returns:
        AdapterFactory: Adapter factory instance
    """
    return get_adapter_factory()


async def get_google_calendar_adapter_dependency(
    adapter_factory: AdapterFactory = Depends(get_adapter_factory_dependency),
) -> GoogleCalendarAdapter:
    """
    FastAPI dependency for Google Calendar adapter.

    Args:
        adapter_factory: Adapter factory dependency

    Returns:
        GoogleCalendarAdapter: Google Calendar adapter instance
    """
    adapter = adapter_factory.get_or_create_adapter("google_calendar")
    if not adapter:
        raise RuntimeError("Failed to create Google Calendar adapter")
    return adapter


async def get_trigger_manager_dependency(
    db_session: AsyncSession = Depends(get_async_session),
    workflow_executor: WorkflowExecutor = Depends(get_workflow_executor_dependency),
    adapter_factory: AdapterFactory = Depends(get_adapter_factory_dependency),
) -> TriggerManager:
    """
    FastAPI dependency for trigger manager.

    Args:
        db_session: Database session dependency
        workflow_executor: Workflow executor dependency
        adapter_factory: Adapter factory dependency

    Returns:
        TriggerManager: Trigger manager instance
    """
    container = get_service_container()
    trigger_manager = container.get_service("trigger_manager")

    if trigger_manager is None:
        # Create new trigger manager instance
        trigger_manager = TriggerManager(
            db_session=db_session, workflow_executor=workflow_executor
        )

        # Register adapters with the trigger manager using factory
        for adapter_name in adapter_factory.list_adapters():
            adapter = adapter_factory.get_or_create_adapter(adapter_name)
            if adapter:
                trigger_manager.register_adapter(adapter_name, adapter)

        container.register_service("trigger_manager", trigger_manager)
        logger.info("Created new trigger manager instance")

    return trigger_manager


async def initialize_services() -> None:
    """
    Initialize all core services and adapters.

    This function should be called during application startup
    to ensure all services are properly initialized.
    """
    try:
        logger.info("Initializing core services...")

        # Initialize service container
        container = get_service_container()

        # Initialize database manager
        db_manager = get_db_manager()

        # Initialize adapter factory (this will register default adapters)
        adapter_factory = get_adapter_factory()

        # Initialize workflow executor
        async for db_session in get_async_session():
            workflow_executor = WorkflowExecutor(db_session)
            container.register_service("workflow_executor", workflow_executor)
            break

        # Initialize trigger manager
        async for db_session in get_async_session():
            trigger_manager = TriggerManager(
                db_session=db_session, workflow_executor=workflow_executor
            )

            # Register all available adapters
            for adapter_name in adapter_factory.list_adapters():
                adapter = adapter_factory.get_or_create_adapter(adapter_name)
                if adapter:
                    trigger_manager.register_adapter(adapter_name, adapter)

            container.register_service("trigger_manager", trigger_manager)
            break

        logger.info("Core services initialized successfully")

    except Exception as e:
        logger.error("Failed to initialize core services", error=str(e))
        raise


async def cleanup_services() -> None:
    """
    Cleanup all services and adapters.

    This function should be called during application shutdown
    to ensure proper cleanup of resources.
    """
    try:
        logger.info("Cleaning up core services...")

        container = get_service_container()

        # Cleanup adapter factory and all adapters
        adapter_factory = get_adapter_factory()
        await adapter_factory.cleanup_all()

        # Clear service container
        container._services.clear()
        container._adapters.clear()

        logger.info("Core services cleaned up successfully")

    except Exception as e:
        logger.error("Failed to cleanup core services", error=str(e))
