"""
Pydantic schemas for trigger-related API operations.

This module contains all Pydantic models for trigger creation, updates,
and responses.
"""

from datetime import datetime
from typing import Dict, Any, List, Optional
from uuid import UUID

from pydantic import BaseModel, Field, ConfigDict

# Simplified - remove complex validation imports


class Trigger(BaseModel):
    """Schema for trigger information."""

    id: UUID = Field(..., description="Unique identifier for the trigger")
    user_id: str = Field(..., description="ID of the user who owns the trigger")
    workflow_id: str = Field(..., description="ID of the associated workflow")
    trigger_type: str = Field(..., description="Type of trigger")
    trigger_name: str = Field(..., description="Human-readable name for the trigger")
    trigger_config: Dict[str, Any] = Field(
        ..., description="Adapter-specific configuration"
    )
    event_types: List[str] = Field(
        ..., description="List of event types being monitored"
    )
    is_active: bool = Field(..., description="Whether the trigger is active")

    model_config = ConfigDict(from_attributes=True)


class TriggerCreate(BaseModel):
    """Schema for creating a new trigger."""

    workflow_id: str = Field(
        ..., min_length=1, max_length=255, description="ID of the workflow to execute"
    )
    trigger_type: str = Field(
        ...,
        min_length=1,
        max_length=50,
        description="Type of trigger (e.g., 'google_calendar')",
    )
    trigger_name: str = Field(
        ...,
        min_length=1,
        max_length=255,
        description="Human-readable name for the trigger",
    )
    trigger_config: Dict[str, Any] = Field(
        ..., description="Adapter-specific configuration"
    )
    event_types: List[str] = Field(
        ..., min_items=1, description="List of event types to monitor"
    )

    # Simplified - no complex validation for now

    model_config = ConfigDict(
        use_enum_values=True,
        json_schema_extra={
            "example": {
                "workflow_id": "workflow-456",
                "trigger_type": "google_calendar",
                "trigger_name": "Meeting Reminder Workflow",
                "trigger_config": {
                    "calendar_id": "primary",
                    "use_polling": False,
                    "poll_interval_seconds": 60,
                    "webhook_ttl": 604800,
                    "event_filters": {
                        "title_contains": ["meeting", "call"],
                        "attendee_count_min": 2,
                    },
                },
                "event_types": ["created", "updated"],
            }
        },
    )


class TriggerUpdate(BaseModel):
    """Schema for updating an existing trigger."""

    trigger_name: Optional[str] = Field(
        None, description="Human-readable name for the trigger"
    )
    trigger_config: Optional[Dict[str, Any]] = Field(
        None, description="Adapter-specific configuration"
    )
    event_types: Optional[List[str]] = Field(
        None, description="List of event types to monitor"
    )
    is_active: Optional[bool] = Field(None, description="Whether the trigger is active")


class TriggerResponse(BaseModel):
    """Schema for trigger API responses."""

    id: UUID = Field(..., description="Unique identifier for the trigger")
    user_id: str = Field(..., description="ID of the user who owns the trigger")
    workflow_id: str = Field(..., description="ID of the associated workflow")
    trigger_type: str = Field(..., description="Type of trigger")
    trigger_name: str = Field(..., description="Human-readable name for the trigger")
    trigger_config: Dict[str, Any] = Field(
        ..., description="Adapter-specific configuration"
    )
    event_types: List[str] = Field(
        ..., description="List of event types being monitored"
    )
    is_active: bool = Field(..., description="Whether the trigger is active")
    created_at: datetime = Field(..., description="When the trigger was created")
    updated_at: datetime = Field(..., description="When the trigger was last updated")
    last_triggered_at: Optional[datetime] = Field(
        None, description="When the trigger was last activated"
    )

    model_config = ConfigDict(from_attributes=True)


class TriggerListResponse(BaseModel):
    """Schema for paginated trigger list responses."""

    triggers: List[TriggerResponse] = Field(..., description="List of triggers")
    total: int = Field(..., description="Total number of triggers")
    page: int = Field(..., description="Current page number")
    page_size: int = Field(..., description="Number of items per page")


class TriggerToggleRequest(BaseModel):
    """Schema for enabling/disabling triggers."""

    is_active: bool = Field(
        ..., description="Whether to activate or deactivate the trigger"
    )


class TriggerExecutionResponse(BaseModel):
    """Schema for trigger execution responses."""

    id: UUID = Field(..., description="Unique identifier for the execution")
    trigger_id: UUID = Field(..., description="ID of the trigger that was executed")
    event_data: Dict[str, Any] = Field(
        ..., description="Event data that triggered the execution"
    )
    workflow_execution_id: Optional[str] = Field(
        None, description="Correlation ID from workflow service"
    )
    status: str = Field(..., description="Execution status")
    error_message: Optional[str] = Field(
        None, description="Error message if execution failed"
    )
    retry_count: int = Field(..., description="Number of retry attempts")
    executed_at: datetime = Field(..., description="When the execution was initiated")
    completed_at: Optional[datetime] = Field(
        None, description="When the execution was completed"
    )

    model_config = ConfigDict(from_attributes=True)


class TriggerStatsResponse(BaseModel):
    """Schema for trigger statistics responses."""

    total_triggers: int = Field(..., description="Total number of triggers")
    active_triggers: int = Field(..., description="Number of active triggers")
    inactive_triggers: int = Field(..., description="Number of inactive triggers")
    triggers_by_type: Dict[str, int] = Field(
        ..., description="Count of triggers by type"
    )
    recent_executions: int = Field(
        ..., description="Number of executions in the last 24 hours"
    )
    success_rate: float = Field(..., description="Success rate percentage")


class AdapterHealthResponse(BaseModel):
    """Schema for adapter health responses."""

    adapter_name: str = Field(..., description="Name of the adapter")
    is_healthy: bool = Field(..., description="Whether the adapter is healthy")
    last_check: datetime = Field(..., description="When the health check was performed")
    error_message: Optional[str] = Field(None, description="Error message if unhealthy")
    active_triggers: int = Field(
        ..., description="Number of active triggers for this adapter"
    )
    external_service_status: Optional[Dict[str, Any]] = Field(
        None, description="External service status details"
    )


class TriggerFilterRequest(BaseModel):
    """Schema for filtering triggers."""

    user_id: Optional[str] = Field(None, description="Filter by user ID")
    workflow_id: Optional[str] = Field(None, description="Filter by workflow ID")
    trigger_type: Optional[str] = Field(None, description="Filter by trigger type")
    is_active: Optional[bool] = Field(None, description="Filter by active status")
    created_after: Optional[datetime] = Field(
        None, description="Filter by creation date"
    )
    created_before: Optional[datetime] = Field(
        None, description="Filter by creation date"
    )
    page: int = Field(1, ge=1, description="Page number")
    page_size: int = Field(20, ge=1, le=100, description="Number of items per page")


class EventFieldMapping(BaseModel):
    """Schema for mapping Google Calendar event fields to workflow fields."""

    calendar_field: str = Field(
        ...,
        description="Google Calendar event field name (e.g., 'summary', 'start.dateTime')",
    )
    workflow_field: str = Field(..., description="Workflow field name to map to")
    field_type: Optional[str] = Field(
        default="string", description="Expected field type (string, number, boolean)"
    )


class GoogleCalendarTriggerConfig(BaseModel):
    """Schema for Google Calendar trigger configuration."""

    calendar_id: str = Field(
        default="primary",
        description="Google Calendar ID to monitor (default: 'primary')",
    )
    use_polling: bool = Field(
        default=False, description="Force polling mode instead of webhooks"
    )
    poll_interval_seconds: int = Field(
        default=60, ge=15, le=3600, description="Polling interval in seconds (15-3600)"
    )
    webhook_ttl: int = Field(
        default=604800,
        ge=3600,
        le=2592000,
        description="Webhook subscription TTL in seconds (1 hour - 30 days)",
    )
    event_filters: Optional[Dict[str, Any]] = Field(
        default=None, description="Optional event filtering criteria"
    )
    selected_event_fields: Optional[List[EventFieldMapping]] = Field(
        default=None,
        description="Optional mapping of Google Calendar event fields to workflow fields",
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "calendar_id": "primary",
                "use_polling": False,
                "poll_interval_seconds": 60,
                "webhook_ttl": 604800,
                "event_filters": {
                    "title_contains": ["meeting", "call"],
                    "attendee_count_min": 2,
                },
                "selected_event_fields": [
                    {
                        "calendar_field": "summary",
                        "workflow_field": "event_title",
                        "field_type": "string",
                    },
                    {
                        "calendar_field": "start.dateTime",
                        "workflow_field": "start_time",
                        "field_type": "string",
                    },
                    {
                        "calendar_field": "attendees",
                        "workflow_field": "attendee_list",
                        "field_type": "array",
                    },
                ],
            }
        }
    )


class WebhookChannelInfo(BaseModel):
    """Schema for webhook channel information."""

    channel_id: str = Field(..., description="Unique channel identifier")
    resource_id: str = Field(..., description="Google Calendar resource ID")
    trigger_id: str = Field(..., description="Associated trigger ID")
    user_id: str = Field(..., description="User ID who owns this channel")
    calendar_id: str = Field(..., description="Calendar ID being monitored")
    expires_at: datetime = Field(..., description="When the channel expires")
    is_expired: bool = Field(..., description="Whether the channel has expired")
    created_at: datetime = Field(..., description="When the channel was created")


class WebhookStatusResponse(BaseModel):
    """Schema for webhook status response."""

    active_channels: List[WebhookChannelInfo] = Field(
        ..., description="List of active webhook channels"
    )
    total_channels: int = Field(..., description="Total number of active channels")
    expired_channels: int = Field(..., description="Number of expired channels")


class WebhookCleanupResponse(BaseModel):
    """Schema for webhook cleanup response."""

    cleaned_channels: int = Field(..., description="Number of channels cleaned up")
    remaining_channels: int = Field(..., description="Number of channels remaining")
    errors: List[str] = Field(default=[], description="Any errors during cleanup")


class WebhookStopRequest(BaseModel):
    """Schema for stopping a webhook channel."""

    channel_id: str = Field(..., description="Channel ID to stop")


class WebhookStopResponse(BaseModel):
    """Schema for webhook stop response."""

    success: bool = Field(
        ..., description="Whether the channel was stopped successfully"
    )
    message: str = Field(..., description="Result message")
    channel_id: str = Field(..., description="Channel ID that was stopped")


class TriggerTypeInfo(BaseModel):
    """Schema for trigger type information."""

    trigger_type: str = Field(..., description="Type identifier for the trigger")
    name: str = Field(..., description="Human-readable name of the trigger")
    description: str = Field(..., description="Description of what this trigger does")
    supported_event_types: List[str] = Field(
        ..., description="List of supported event types"
    )
    configuration_schema: Dict[str, Any] = Field(
        ..., description="JSON schema for trigger configuration"
    )
    sample_event_data: Dict[str, Any] = Field(
        ..., description="Sample event data structure"
    )
    available_fields: List[Dict[str, str]] = Field(
        ..., description="List of available fields for mapping"
    )


class TriggerTypesResponse(BaseModel):
    """Schema for trigger types list response."""

    trigger_types: List[TriggerTypeInfo] = Field(
        ..., description="List of available trigger types"
    )
    total_count: int = Field(..., description="Total number of trigger types")


class DriveFieldMapping(BaseModel):
    """Schema for mapping Google Drive file fields to workflow fields."""

    drive_field: str = Field(
        ...,
        description="Google Drive file field name (e.g., 'name', 'mimeType', 'modifiedTime')",
    )
    workflow_field: str = Field(..., description="Workflow field name to map to")
    field_type: Optional[str] = Field(
        default="string",
        description="Expected field type (string, number, boolean, array)",
    )


class GoogleDriveServiceAccountTriggerConfig(BaseModel):
    """Schema for Google Drive service account trigger configuration."""

    organization_id: str = Field(
        ..., description="Organization ID for service account credentials"
    )
    folder_ids: Optional[List[str]] = Field(
        default=None,
        description="Specific folder IDs to monitor (if empty, monitors all accessible folders)",
    )
    file_types: Optional[List[str]] = Field(
        default=None,
        description="MIME types to monitor (e.g., ['application/pdf', 'image/jpeg'])",
    )
    use_polling: bool = Field(
        default=False, description="Force polling mode instead of webhooks"
    )
    poll_interval_seconds: int = Field(
        default=300, ge=60, le=3600, description="Polling interval in seconds (60-3600)"
    )
    webhook_ttl: int = Field(
        default=604800,
        ge=3600,
        le=2592000,
        description="Webhook subscription TTL in seconds (1 hour - 30 days)",
    )
    event_filters: Optional[Dict[str, Any]] = Field(
        default=None, description="Optional event filtering criteria"
    )
    selected_file_fields: Optional[List[DriveFieldMapping]] = Field(
        default=None,
        description="Optional mapping of Google Drive file fields to workflow fields",
    )
    sync_endpoint_config: Optional[Dict[str, Any]] = Field(
        default=None,
        description="Configuration for sync endpoint calls (timeout, retries, etc.)",
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "organization_id": "org_123456",
                "folder_ids": ["1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"],
                "file_types": [
                    "application/pdf",
                    "application/vnd.google-apps.document",
                ],
                "use_polling": False,
                "poll_interval_seconds": 300,
                "webhook_ttl": 604800,
                "event_filters": {
                    "name_contains": ["report", "invoice"],
                    "size_max_bytes": 10485760,  # 10MB
                },
                "selected_file_fields": [
                    {
                        "drive_field": "name",
                        "workflow_field": "file_name",
                        "field_type": "string",
                    },
                    {
                        "drive_field": "mimeType",
                        "workflow_field": "file_type",
                        "field_type": "string",
                    },
                    {
                        "drive_field": "size",
                        "workflow_field": "file_size",
                        "field_type": "number",
                    },
                ],
                "sync_endpoint_config": {
                    "timeout_seconds": 30,
                    "max_retries": 3,
                    "retry_delay_seconds": 5,
                },
            }
        }
    )


class GoogleDriveUserTriggerConfig(BaseModel):
    """Schema for Google Drive user trigger configuration."""

    folder_ids: Optional[List[str]] = Field(
        default=None,
        description="Specific folder IDs to monitor (if empty, monitors all accessible folders)",
    )
    file_types: Optional[List[str]] = Field(
        default=None,
        description="MIME types to monitor (e.g., ['application/pdf', 'image/jpeg'])",
    )
    use_polling: bool = Field(
        default=False, description="Force polling mode instead of webhooks"
    )
    poll_interval_seconds: int = Field(
        default=300, ge=60, le=3600, description="Polling interval in seconds (60-3600)"
    )
    webhook_ttl: int = Field(
        default=604800,
        ge=3600,
        le=2592000,
        description="Webhook subscription TTL in seconds (1 hour - 30 days)",
    )
    event_filters: Optional[Dict[str, Any]] = Field(
        default=None, description="Optional event filtering criteria"
    )
    selected_file_fields: Optional[List[DriveFieldMapping]] = Field(
        default=None,
        description="Optional mapping of Google Drive file fields to workflow fields",
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "folder_ids": ["1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"],
                "file_types": ["application/pdf", "image/jpeg"],
                "use_polling": False,
                "poll_interval_seconds": 300,
                "webhook_ttl": 604800,
                "event_filters": {
                    "name_contains": ["document", "report"],
                    "size_max_bytes": 52428800,  # 50MB
                    "created_after": "2024-01-01T00:00:00Z",
                },
                "selected_file_fields": [
                    {
                        "drive_field": "name",
                        "workflow_field": "file_name",
                        "field_type": "string",
                    },
                    {
                        "drive_field": "webViewLink",
                        "workflow_field": "file_url",
                        "field_type": "string",
                    },
                    {
                        "drive_field": "owners",
                        "workflow_field": "file_owners",
                        "field_type": "array",
                    },
                ],
            }
        }
    )


class ServiceAccountCredentialsCreate(BaseModel):
    """Schema for creating service account credentials."""

    organization_id: str = Field(
        ..., description="Organization ID for these credentials"
    )
    service_account_json: Dict[str, Any] = Field(
        ..., description="Google service account JSON credentials"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "organization_id": "org_123456",
                "service_account_json": {
                    "type": "service_account",
                    "project_id": "my-project-123",
                    "private_key_id": "key123",
                    "private_key": "-----BEGIN PRIVATE KEY-----\n...\n-----END PRIVATE KEY-----\n",
                    "client_email": "<EMAIL>",
                    "client_id": "*********",
                    "auth_uri": "https://accounts.google.com/o/oauth2/auth",
                    "token_uri": "https://oauth2.googleapis.com/token",
                },
            }
        }
    )


class ServiceAccountCredentialsResponse(BaseModel):
    """Schema for service account credentials response (without sensitive data)."""

    id: UUID = Field(..., description="Unique identifier for the credentials")
    organization_id: str = Field(..., description="Organization ID")
    service_account_email: str = Field(..., description="Service account email")
    project_id: str = Field(..., description="Google Cloud project ID")
    is_active: bool = Field(..., description="Whether credentials are active")
    last_validated_at: Optional[datetime] = Field(
        None, description="When credentials were last validated"
    )
    validation_error: Optional[str] = Field(
        None, description="Last validation error if any"
    )
    created_at: datetime = Field(..., description="When credentials were created")
    updated_at: datetime = Field(..., description="When credentials were last updated")

    model_config = ConfigDict(from_attributes=True)


class ServiceAccountCredentialsUpdate(BaseModel):
    """Schema for updating service account credentials."""

    service_account_json: Optional[Dict[str, Any]] = Field(
        None, description="Updated Google service account JSON credentials"
    )
    is_active: Optional[bool] = Field(
        None, description="Whether to activate/deactivate credentials"
    )


class DriveSyncRequest(BaseModel):
    """Schema for Google Drive sync endpoint requests."""

    organization_id: str = Field(..., description="Organization ID to sync for")
    folder_ids: List[str] = Field(
        ..., description="List of folder IDs to sync (from drive events)"
    )
    event_context: Optional[Dict[str, Any]] = Field(
        default=None, description="Additional context from the triggering event"
    )

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "organization_id": "org_123456",
                "folder_ids": ["1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"],
                "event_context": {
                    "trigger_event": "file_created",
                    "file_id": "*********************************",
                    "timestamp": "2024-01-15T10:30:00Z",
                },
            }
        }
    )


class DriveSyncResponse(BaseModel):
    """Schema for Google Drive sync endpoint responses."""

    success: bool = Field(..., description="Whether sync was successful")
    organization_id: str = Field(..., description="Organization ID that was synced")
    synced_folders: List[str] = Field(
        ..., description="List of folder IDs that were synced"
    )
    files_processed: int = Field(..., description="Number of files processed")
    sync_duration_ms: int = Field(..., description="Sync duration in milliseconds")
    errors: List[str] = Field(default=[], description="Any errors during sync")
    sync_id: str = Field(..., description="Unique identifier for this sync operation")

    model_config = ConfigDict(
        json_schema_extra={
            "example": {
                "success": True,
                "organization_id": "org_123456",
                "synced_folders": ["1BxiMVs0XRA5nFMdKvBdBZjgmUUqptlbs74OgvE2upms"],
                "files_processed": 42,
                "sync_duration_ms": 1250,
                "errors": [],
                "sync_id": "sync_789abc123def",
            }
        }
    )


class DriveEventResponse(BaseModel):
    """Schema for Google Drive event responses."""

    id: UUID = Field(..., description="Unique identifier for the drive event")
    channel_id: str = Field(..., description="Google Drive webhook channel ID")
    resource_id: str = Field(..., description="Google Drive resource ID")
    user_id: Optional[str] = Field(None, description="User ID (for user triggers)")
    organization_id: Optional[str] = Field(
        None, description="Organization ID (for service account triggers)"
    )
    file_id: str = Field(..., description="Google Drive file/folder ID")
    folder_id: Optional[str] = Field(None, description="Parent folder ID if applicable")
    event_data: Dict[str, Any] = Field(
        ..., description="Complete event data from Google Drive API"
    )
    event_type: str = Field(
        ..., description="Type of event (created, updated, deleted, moved)"
    )
    trigger_type: str = Field(
        ..., description="Type of trigger (user, service_account)"
    )
    processed: bool = Field(..., description="Whether this event has been processed")
    processing_error: Optional[str] = Field(
        None, description="Error message if processing failed"
    )
    created_at: datetime = Field(..., description="When this record was created")
    updated_at: datetime = Field(..., description="When this record was last updated")

    model_config = ConfigDict(from_attributes=True)
