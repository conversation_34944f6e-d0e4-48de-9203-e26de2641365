"""
Common test utilities and helpers for the Trigger Service test suite.

This module consolidates common test patterns, fixtures, and utilities
to eliminate code duplication across test files.
"""

import asyncio
import json
import uuid
from datetime import datetime, timezone
from typing import Any, Dict, List, Optional
from unittest.mock import AsyncMock, MagicMock

import pytest
from fastapi.testclient import TestClient
from sqlalchemy.ext.asyncio import AsyncSession

from src.adapters.base import BaseTriggerAdapter, TriggerEvent, TriggerEventType
from src.database.models import Trigger, TriggerExecution, Scheduler
from src.utils.common import generate_correlation_id


class MockAdapter(BaseTriggerAdapter):
    """Mock adapter for testing purposes."""
    
    def __init__(self, adapter_name: str = "mock_adapter"):
        """Initialize mock adapter."""
        super().__init__(adapter_name)
        self.setup_calls = []
        self.remove_calls = []
        self.health_check_result = True
        self.events_to_return = []
    
    async def setup_trigger(self, trigger_config, session=None) -> bool:
        """Mock setup trigger."""
        self.setup_calls.append(trigger_config)
        return True
    
    async def remove_trigger(self, trigger_id: uuid.UUID) -> bool:
        """Mock remove trigger."""
        self.remove_calls.append(trigger_id)
        return True
    
    async def _perform_health_check(self) -> bool:
        """Mock health check."""
        return self.health_check_result
    
    def get_supported_event_types(self) -> List[TriggerEventType]:
        """Get supported event types."""
        return [TriggerEventType.CREATED, TriggerEventType.UPDATED]
    
    def get_sample_event_data(self) -> Dict[str, Any]:
        """Get sample event data."""
        return {"sample": "data", "timestamp": datetime.now(timezone.utc).isoformat()}
    
    def get_available_fields(self) -> List[Dict[str, str]]:
        """Get available fields."""
        return [
            {"name": "id", "type": "string", "description": "Event ID"},
            {"name": "title", "type": "string", "description": "Event title"},
        ]


def create_mock_trigger_event(
    event_id: str = None,
    event_type: TriggerEventType = TriggerEventType.CREATED,
    source: str = "test",
    data: Optional[Dict[str, Any]] = None,
    metadata: Optional[Dict[str, Any]] = None
) -> TriggerEvent:
    """
    Create a mock trigger event for testing.
    
    Args:
        event_id: Event ID (generates random if None)
        event_type: Type of event
        source: Event source
        data: Event data
        metadata: Event metadata
        
    Returns:
        TriggerEvent: Mock trigger event
    """
    return TriggerEvent(
        event_id=event_id or str(uuid.uuid4()),
        event_type=event_type,
        source=source,
        timestamp=datetime.now(timezone.utc),
        data=data or {"test": "data"},
        metadata=metadata or {"test": "metadata"}
    )


def create_mock_trigger(
    user_id: str = "test_user",
    trigger_type: str = "test_trigger",
    workflow_id: str = "test_workflow",
    **kwargs
) -> Trigger:
    """
    Create a mock trigger for testing.
    
    Args:
        user_id: User ID
        trigger_type: Trigger type
        workflow_id: Workflow ID
        **kwargs: Additional trigger properties
        
    Returns:
        Trigger: Mock trigger instance
    """
    trigger_data = {
        "id": uuid.uuid4(),
        "user_id": user_id,
        "name": kwargs.get("name", "Test Trigger"),
        "description": kwargs.get("description", "Test trigger description"),
        "trigger_type": trigger_type,
        "workflow_id": workflow_id,
        "event_types": kwargs.get("event_types", ["created"]),
        "config": kwargs.get("config", {}),
        "is_active": kwargs.get("is_active", True),
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
    }
    
    return Trigger(**trigger_data)


def create_mock_scheduler(
    user_id: str = "test_user",
    workflow_id: str = "test_workflow",
    **kwargs
) -> Scheduler:
    """
    Create a mock scheduler for testing.
    
    Args:
        user_id: User ID
        workflow_id: Workflow ID
        **kwargs: Additional scheduler properties
        
    Returns:
        Scheduler: Mock scheduler instance
    """
    scheduler_data = {
        "id": uuid.uuid4(),
        "user_id": user_id,
        "name": kwargs.get("name", "Test Scheduler"),
        "description": kwargs.get("description", "Test scheduler description"),
        "workflow_id": workflow_id,
        "schedule": kwargs.get("schedule", {"frequency": "daily", "time": "09:00"}),
        "input_values": kwargs.get("input_values", {}),
        "is_active": kwargs.get("is_active", True),
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
    }
    
    return Scheduler(**scheduler_data)


def create_mock_execution(
    trigger_id: uuid.UUID = None,
    status: str = "completed",
    **kwargs
) -> TriggerExecution:
    """
    Create a mock trigger execution for testing.
    
    Args:
        trigger_id: Trigger ID
        status: Execution status
        **kwargs: Additional execution properties
        
    Returns:
        TriggerExecution: Mock execution instance
    """
    execution_data = {
        "id": uuid.uuid4(),
        "trigger_id": trigger_id or uuid.uuid4(),
        "status": status,
        "event_data": kwargs.get("event_data", {"test": "data"}),
        "workflow_correlation_id": kwargs.get("workflow_correlation_id", generate_correlation_id()),
        "started_at": kwargs.get("started_at", datetime.now(timezone.utc)),
        "completed_at": kwargs.get("completed_at", datetime.now(timezone.utc)),
        "created_at": datetime.now(timezone.utc),
        "updated_at": datetime.now(timezone.utc),
    }
    
    return TriggerExecution(**execution_data)


class MockAsyncSession:
    """Mock async database session for testing."""
    
    def __init__(self):
        """Initialize mock session."""
        self.add_calls = []
        self.execute_calls = []
        self.commit_calls = []
        self.rollback_calls = []
        self.refresh_calls = []
        self.flush_calls = []
        self.query_results = {}
    
    def add(self, instance):
        """Mock add method."""
        self.add_calls.append(instance)
    
    async def execute(self, query):
        """Mock execute method."""
        self.execute_calls.append(query)
        # Return mock result based on query type
        result = MagicMock()
        result.scalar.return_value = self.query_results.get("scalar", None)
        result.scalars.return_value.all.return_value = self.query_results.get("all", [])
        result.scalar_one_or_none.return_value = self.query_results.get("scalar_one_or_none", None)
        return result
    
    async def commit(self):
        """Mock commit method."""
        self.commit_calls.append(True)
    
    async def rollback(self):
        """Mock rollback method."""
        self.rollback_calls.append(True)
    
    async def refresh(self, instance):
        """Mock refresh method."""
        self.refresh_calls.append(instance)
    
    async def flush(self):
        """Mock flush method."""
        self.flush_calls.append(True)
    
    def set_query_result(self, result_type: str, value: Any):
        """Set mock query result."""
        self.query_results[result_type] = value


class MockHTTPClient:
    """Mock HTTP client for testing external API calls."""
    
    def __init__(self):
        """Initialize mock HTTP client."""
        self.requests = []
        self.responses = {}
        self.default_response = {"status_code": 200, "json": {}}
    
    async def request(self, method: str, url: str, **kwargs):
        """Mock HTTP request."""
        request_data = {
            "method": method,
            "url": url,
            "headers": kwargs.get("headers", {}),
            "data": kwargs.get("data"),
            "json": kwargs.get("json"),
        }
        self.requests.append(request_data)
        
        # Return configured response or default
        response_key = f"{method.upper()} {url}"
        response_data = self.responses.get(response_key, self.default_response)
        
        response = MagicMock()
        response.status_code = response_data.get("status_code", 200)
        response.json.return_value = response_data.get("json", {})
        response.text = response_data.get("text", "")
        response.headers = response_data.get("headers", {})
        
        return response
    
    def set_response(self, method: str, url: str, response_data: Dict[str, Any]):
        """Set mock response for specific request."""
        self.responses[f"{method.upper()} {url}"] = response_data


def assert_api_response(
    response,
    expected_status: int = 200,
    expected_keys: Optional[List[str]] = None,
    expected_data: Optional[Dict[str, Any]] = None
):
    """
    Assert API response format and content.
    
    Args:
        response: API response
        expected_status: Expected status code
        expected_keys: Expected keys in response
        expected_data: Expected data values
    """
    assert response.status_code == expected_status
    
    if expected_status == 200:
        response_data = response.json()
        
        # Check standard response format
        assert "status" in response_data
        assert "message" in response_data
        assert "timestamp" in response_data
        
        # Check expected keys
        if expected_keys:
            for key in expected_keys:
                assert key in response_data
        
        # Check expected data
        if expected_data:
            for key, value in expected_data.items():
                assert response_data.get(key) == value


def run_async_test(coro):
    """
    Run async test function.
    
    Args:
        coro: Async coroutine to run
        
    Returns:
        Any: Result of the coroutine
    """
    loop = asyncio.new_event_loop()
    asyncio.set_event_loop(loop)
    try:
        return loop.run_until_complete(coro)
    finally:
        loop.close()


@pytest.fixture
def mock_adapter():
    """Fixture for mock adapter."""
    return MockAdapter()


@pytest.fixture
def mock_session():
    """Fixture for mock database session."""
    return MockAsyncSession()


@pytest.fixture
def mock_http_client():
    """Fixture for mock HTTP client."""
    return MockHTTPClient()


@pytest.fixture
def sample_trigger():
    """Fixture for sample trigger."""
    return create_mock_trigger()


@pytest.fixture
def sample_scheduler():
    """Fixture for sample scheduler."""
    return create_mock_scheduler()


@pytest.fixture
def sample_execution():
    """Fixture for sample execution."""
    return create_mock_execution()


@pytest.fixture
def sample_trigger_event():
    """Fixture for sample trigger event."""
    return create_mock_trigger_event()


# Common test data
TEST_USER_ID = "test_user_123"
TEST_WORKFLOW_ID = "test_workflow_456"
TEST_TRIGGER_CONFIG = {
    "calendar_id": "primary",
    "webhook_ttl": 3600,
}
TEST_SCHEDULER_CONFIG = {
    "frequency": "daily",
    "time": "09:00",
    "timezone": "UTC",
}
TEST_EVENT_DATA = {
    "id": "event_123",
    "title": "Test Event",
    "start": "2024-01-01T09:00:00Z",
    "end": "2024-01-01T10:00:00Z",
}
