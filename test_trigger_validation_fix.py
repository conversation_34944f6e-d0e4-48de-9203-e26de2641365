#!/usr/bin/env python3
"""
Test script to verify the trigger validation fix.

This script demonstrates the proper API usage and validates that the
event_types field validation works correctly.
"""

import json
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, ValidationError

# Simplified models for testing without dependencies
class TriggerConfigModel(BaseModel):
    calendar_id: Optional[str] = None
    webhook_ttl: Optional[int] = 3600
    additional_config: Optional[Dict[str, Any]] = {}

class CreateTriggerRequest(BaseModel):
    name: str = Field(min_length=1, max_length=255)
    description: Optional[str] = None
    trigger_type: str = Field(min_length=1, max_length=50)
    workflow_id: str = Field(min_length=1, max_length=255)
    event_types: List[str] = Field(min_items=1, description="List of event types to monitor - REQUIRED")
    config: TriggerConfigModel = Field(default_factory=TriggerConfigModel)
    is_active: bool = True


def test_missing_event_types():
    """Test that missing event_types field raises proper validation error."""
    print("Testing missing event_types field...")
    
    # This is the request that was causing the error
    invalid_request_data = {
        'name': 'my-trigger',
        'description': 'string',
        'trigger_type': 'google_calendar',
        'workflow_id': 'b8a8dda7-cbb5-499d-901e-b280856d1e71',
        'config': {
            'calendar_id': 'primary',
            'webhook_ttl': 3600,
            'additional_config': {'additionalProp1': {}}
        },
        'is_active': True
        # Missing event_types field!
    }
    
    try:
        request = CreateTriggerRequest(**invalid_request_data)
        print("❌ ERROR: Validation should have failed!")
        return False
    except ValidationError as e:
        print("✅ Validation correctly failed for missing event_types")
        print(f"   Error details: {e.errors()}")
        
        # Check that the error mentions event_types
        error_messages = [error['msg'] for error in e.errors()]
        if any('event_types' in msg.lower() for msg in error_messages):
            print("✅ Error message correctly mentions event_types field")
        else:
            print("❌ Error message should mention event_types field")
            return False
        
        return True


def test_valid_request():
    """Test that a valid request with event_types works correctly."""
    print("\nTesting valid request with event_types...")
    
    # This is the corrected request
    valid_request_data = {
        'name': 'my-trigger',
        'description': 'string',
        'trigger_type': 'google_calendar',
        'workflow_id': 'b8a8dda7-cbb5-499d-901e-b280856d1e71',
        'event_types': ['created', 'updated'],  # Added this required field
        'config': {
            'calendar_id': 'primary',
            'webhook_ttl': 3600,
            'additional_config': {'additionalProp1': {}}
        },
        'is_active': True
    }
    
    try:
        request = CreateTriggerRequest(**valid_request_data)
        print("✅ Valid request created successfully")
        print(f"   Event types: {request.event_types}")
        return True
    except ValidationError as e:
        print(f"❌ ERROR: Valid request should not fail: {e.errors()}")
        return False


def test_invalid_event_types():
    """Test that invalid event types are properly rejected."""
    print("\nTesting invalid event types...")
    
    invalid_request_data = {
        'name': 'my-trigger',
        'description': 'string',
        'trigger_type': 'google_calendar',
        'workflow_id': 'b8a8dda7-cbb5-499d-901e-b280856d1e71',
        'event_types': ['invalid_event', 'another_invalid'],  # Invalid event types
        'config': {
            'calendar_id': 'primary',
            'webhook_ttl': 3600,
            'additional_config': {}
        },
        'is_active': True
    }
    
    try:
        request = CreateTriggerRequest(**invalid_request_data)
        print("❌ ERROR: Validation should have failed for invalid event types!")
        return False
    except ValidationError as e:
        print("✅ Validation correctly failed for invalid event types")
        print(f"   Error details: {e.errors()}")
        return True


def print_correct_usage_example():
    """Print an example of correct API usage."""
    print("\n" + "="*60)
    print("CORRECT API USAGE EXAMPLE")
    print("="*60)
    
    correct_request = {
        "name": "Meeting Notifications",
        "description": "Notify when calendar events are created or updated",
        "trigger_type": "google_calendar",
        "workflow_id": "b8a8dda7-cbb5-499d-901e-b280856d1e71",
        "event_types": ["created", "updated"],  # This field is REQUIRED
        "config": {
            "calendar_id": "primary",
            "webhook_ttl": 3600,
            "additional_config": {}
        },
        "is_active": True
    }
    
    print("POST /api/v1/triggers")
    print("Content-Type: application/json")
    print()
    print(json.dumps(correct_request, indent=2))
    print()
    print("Key points:")
    print("- event_types is a REQUIRED field")
    print("- event_types must be a non-empty list")
    print("- Valid event types: created, updated, deleted")
    print("- At least one event type must be specified")


def main():
    """Run all validation tests."""
    print("Trigger Validation Fix Test")
    print("="*40)
    
    tests_passed = 0
    total_tests = 3
    
    if test_missing_event_types():
        tests_passed += 1
    
    if test_valid_request():
        tests_passed += 1
    
    if test_invalid_event_types():
        tests_passed += 1
    
    print(f"\n{'='*40}")
    print(f"Test Results: {tests_passed}/{total_tests} tests passed")
    
    if tests_passed == total_tests:
        print("✅ All tests passed! The validation fix is working correctly.")
    else:
        print("❌ Some tests failed. Please check the implementation.")
    
    print_correct_usage_example()
    
    return tests_passed == total_tests


if __name__ == "__main__":
    main()